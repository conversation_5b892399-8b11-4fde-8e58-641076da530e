name: CI/CD

on:
  push:
    branches:
      - master
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        default: dev.uk
        type: choice
        options:
          - dev.uk
          - stage.uk
          - prod.uk
          - stage.eu
          - prod.eu

permissions:
  contents: read
  id-token: write
  pull-requests: write
  actions: read

jobs:
  build:
    uses: Rentancy-com/github-actions/.github/workflows/node-build.yaml@master
    secrets:
      npm-token: ${{ secrets.NPM_TOKEN }}

  push-container-image:
    needs: build
    if: success()
    uses: Rentancy-com/github-actions/.github/workflows/publish-container-image.yaml@master
    with:
      environment: ${{ inputs.environment || 'dev.uk' }}
      build-run-id: ${{ needs.build.outputs.run_id }}

  rollout:
    needs: [ build, push-container-image ]
    if: success()
    uses: Rentancy-com/github-actions/.github/workflows/rollout.yaml@master
    with:
      environment: ${{ inputs.environment || 'dev.uk' }}
      container-image-tag: ${{ needs.push-container-image.outputs.container_image_tag }}
