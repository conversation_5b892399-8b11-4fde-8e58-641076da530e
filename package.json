{"name": "loftyworks-main-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.712.0", "@aws-sdk/client-eventbridge": "^3.758.0", "@aws-sdk/client-lambda": "^3.750.0", "@aws-sdk/client-ses": "^3.741.0", "@aws-sdk/client-sqs": "^3.682.0", "@aws-sdk/client-ssm": "^3.759.0", "@aws-sdk/client-sts": "^3.716.0", "@aws-sdk/lib-dynamodb": "^3.712.0", "@aws-sdk/signature-v4": "^3.370.0", "@modelcontextprotocol/sdk": "^1.11.4", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.0", "@opensearch-project/opensearch": "^2.13.0", "@prisma/adapter-pg": "^6.0.1", "@prisma/client": "^6.0.1", "@rekog/mcp-nest": "^1.5.2", "@rentancy-com/loftyworks-events": "^1.8.0", "aws-amplify": "^6.14.4", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "ioredis": "^5.3.5", "firebase-admin": "^9.1.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "moment": "^2.30.1", "pg": "^8.13.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "tslog": "^4.9.3", "uuid": "^11.0.3", "zod": "^3.24.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/node": "^22.10.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "nodemon": "^3.1.7", "prettier": "^3.0.0", "prisma": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}