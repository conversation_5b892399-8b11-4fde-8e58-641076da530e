[{"ddb_table_name": "Document-7ghe6kcqrrg37ohck7dmvk7zaa-prod", "pg_table_name": "document", "ddb_table_columns": ["id", "key", "name", "type", "status", "description", "mimeType", "archived", "createdAt", "updatedAt", "expiry", "done", "documentTenancyId", "documentPropertyId", "documentSupplierOrganisationId", "documentOrganisationId", "documentUserId", "documentConversationId", "documentEmailAttachmentId", "imageTaskId", "generalDocument", "isParentPropertyDocumentShared", "imageOrganisationId", "parentPropertyEntityId", "documentPermissionId", "documentTaskId", "documentLineItemId"], "pg_table_columns": ["id", "key", "name", "type", "status", "description", "mime_type", "archived", "created_at", "updated_at", "expiry", "done", "document_tenancy_id", "document_property_id", "document_supplier_organisation_id", "document_organisation_id", "document_user_id", "document_conversation_id", "document_email_attachment_id", "image_task_id", "general_document", "is_parent_property_document_shared", "image_organisation_id", "parent_property_entity_id", "document_permission_id", "document_task_id", "document_line_item_id"]}, {"ddb_table_name": "DocumentSuppliers-7ghe6kcqrrg37ohck7dmvk7zaa-prod", "pg_table_name": "document_supplier", "ddb_table_columns": ["id", "documentSuppliersUserId", "documentSuppliersDocumentId", "documentSuppliersOrganisationId", "createdAt", "updatedAt"], "pg_table_columns": ["id", "user_id", "document_id", "organisation_id", "created_at", "updated_at"]}, {"ddb_table_name": "DocumentTemplateTypes-7ghe6kcqrrg37ohck7dmvk7zaa-prod", "pg_table_name": "document_template_type", "ddb_table_columns": ["id", "name", "index", "organisationId", "createdAt"], "pg_table_columns": ["id", "name", "index", "organisation_id", "created_at"]}, {"ddb_table_name": "DocumentTemplate-7ghe6kcqrrg37ohck7dmvk7zaa-prod", "pg_table_name": "document_template", "ddb_table_columns": ["id", "title", "type", "other", "status", "contractTypes", "documentTemplateOrganisationId", "documentTemplateUserId", "documentTemplateDocusignTemplateId", "createdAt", "updatedAt"], "pg_table_columns": ["id", "title", "type", "other", "status", "contract_types", "organisation_id", "user_id", "docusign_template_id", "created_at", "updated_at"]}, {"ddb_table_name": "Permissions-7ghe6kcqrrg37ohck7dmvk7zaa-prod", "pg_table_name": "permission", "ddb_table_columns": ["id", "itemType", "groups", "createdAt", "updatedAt"], "pg_table_columns": ["id", "item_type", "groups", "created_at", "updated_at"]}]