#!/bin/bash
aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin 686255943894.dkr.ecr.eu-west-2.amazonaws.com
# alternative version to have steps separated
#docker buildx build --platform linux/amd64 -t loftyworks-main:latest -f Dockerfile .
#docker tag loftyworks-main:latest 686255943894.dkr.ecr.eu-west-2.amazonaws.com/loftyworks-main:latest
#docker push 686255943894.dkr.ecr.eu-west-2.amazonaws.com/loftyworks-main:latest

docker buildx build \
  --platform linux/amd64 \
  -t 686255943894.dkr.ecr.eu-west-2.amazonaws.com/loftyworks-main:latest \
  -f Dockerfile \
  --secret id=npmrc,src=$HOME/.npmrc \
  --push .