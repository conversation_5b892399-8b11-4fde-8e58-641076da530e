from dataclasses import dataclass, field
import json
import os
from typing import List
import boto3
import psycopg2
import argparse
from psycopg2.extras import execute_values
from dotenv import load_dotenv

dynamodb_region = 'eu-west-2'

@dataclass
class TableCfg:
    ddb_table_name: str
    pg_table_name: str
    ddb_table_columns: List[str] = field(default_factory=list) 
    pg_table_columns: List[str] = field(default_factory=list) 

# Connect to PostgreSQL
def connect_postgresql():
    database_url = os.getenv("DATABASE_URL")
    try:
        conn = psycopg2.connect(database_url)
        return conn
    except Exception as e:
        print(f"Error connecting to PostgreSQL: {e}")
        return None

# Fetch data from DynamoDB
def fetch_dynamodb_data(ddb_table, last_key):
    try:
        if last_key:
            response = ddb_table.scan(ExclusiveStartKey=last_key)
        else:
            response = ddb_table.scan()

        data = response.get("Items", [])
        last_key = response.get("LastEvaluatedKey", None)
        return (data, last_key)

    except Exception as e:
        print(f"Error fetching data from DynamoDB: {e}")
        raise Exception("failed to fetch data from ddb")

# Insert data into PostgreSQL
def insert_data_to_postgresql(conn, data, table_cfg: TableCfg):
    insert_query=None
    values=None
    try:
        with conn.cursor() as cur:
            cur.execute("SET search_path TO public;")
            # Prepare the data for insertion
            values = []
            for item in data:
                value=[]
                for col in table_cfg.ddb_table_columns:
                    a = item.get(col, None)
                    value.append(a)

                values.append(tuple(value))

            # Insert data
            insert_query = f"INSERT INTO {table_cfg.pg_table_name} ({', '.join(table_cfg.pg_table_columns)}) VALUES %s"
            execute_values(cur, insert_query, values)

            conn.commit()
            print(f"Inserted {len(values)} rows into PostgreSQL.")
    except Exception as e:
        print(f"Error inserting data into PostgreSQL: {e}")
        conn.rollback()

# Main script
def transfer_data(table_cfg: TableCfg):
    print("Transfering data for table: ", table_cfg.pg_table_name)
    
    print("Connecting to PostgreSQL...")
    conn = connect_postgresql()
    if not conn:
        print("Failed to connect to PostgreSQL.")
        return
    
    # Initialize DynamoDB client
    dynamodb = boto3.resource('dynamodb', region_name=dynamodb_region)
    table = dynamodb.Table(table_cfg.ddb_table_name)

    count=0
    last_key=None
    while True:
        (data, last_key) = fetch_dynamodb_data(table, last_key)

        print("Inserting data into PostgreSQL...")
        insert_data_to_postgresql(conn, data, table_cfg)
        count+=len(data)
        print('data transfered: {}'.format(count))

        if last_key is None:
            break

    conn.close()
    print(f"Data transfer complete for table: {table_cfg.pg_table_name}\n\n")

def main():
    parser = argparse.ArgumentParser(description="Read command-line arguments.")
    parser.add_argument("--file", type=str, help="Config file for data migration")

    args = parser.parse_args()
    if not args.file:
        print("Invalid argument")
        parser.print_usage()
        exit(0)

    load_dotenv()

    cfgData = ""
    with open(args.file, "r") as file:
        cfgData = file.read()

    cfgs = json.loads(cfgData)
    table_cfg_list: List[TableCfg] = [TableCfg(**cfg) for cfg in cfgs]

    print("start to transfering data...\n")
    for cfg in table_cfg_list:
        transfer_data(cfg)

if __name__ == "__main__":
    main()
