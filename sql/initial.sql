CREATE OR <PERSON><PERSON>LACE FUNCTION update_updated_at_column()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TABLE document_template_type (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(128) NOT NULL,
    index INTEGER NOT NULL,
    organisation_id VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER trigger_update_updated_at_document_template_type
BEFORE UPDATE ON document_template_type
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX index_organisation_id_index ON document_template_type (organisation_id, index);

CREATE TABLE document_supplier (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL,
    document_id VARCHAR(64) NOT NULL,
    organisation_id VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER trigger_update_updated_at_document_supplier
BEFORE UPDATE ON document_supplier
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE document_template (
    id text PRIMARY KEY,
    title VARCHAR(64),
    type VARCHAR(128),
    other VARCHAR(64)[],
    status VARCHAR(32),
    contract_types VARCHAR(32)[],
    organisation_id VARCHAR(64),
    user_id VARCHAR(64),
    docusign_template_id VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER trigger_update_updated_at_document_template
BEFORE UPDATE ON document_template
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE document (
    id VARCHAR(64) PRIMARY KEY,
    key VARCHAR(512),
    name VARCHAR(512),
    type VARCHAR(32),
    status VARCHAR(16),
    description TEXT,
    mime_type VARCHAR(128),
    archived BOOLEAN,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry TIMESTAMP,
    done BOOLEAN,
    mutator VARCHAR(64),
    document_tenancy_id VARCHAR(64),
    document_property_id VARCHAR(64),
    document_supplier_organisation_id VARCHAR(64),
    document_organisation_id VARCHAR(64),
    document_user_id VARCHAR(64),
    document_conversation_id VARCHAR(64),
    document_email_attachment_id VARCHAR(64),
    image_task_id VARCHAR(64),
    general_document BOOLEAN,
    is_parent_property_document_shared BOOLEAN,
    image_organisation_id VARCHAR(64),
    parent_property_entity_id VARCHAR(64),
    document_permission_id VARCHAR(64),
    document_task_id VARCHAR(64),
    document_line_item_id VARCHAR(64)
);

CREATE TRIGGER trigger_update_updated_at_document
BEFORE UPDATE ON document
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE INDEX index_document_tenancy_id ON document (document_tenancy_id);
CREATE INDEX index_document_property_id ON document (document_property_id);
CREATE INDEX index_document_user_id ON document (document_user_id);
CREATE INDEX index_document_organisation_id ON document (document_organisation_id);
CREATE INDEX index_document_conversation_id ON document (document_conversation_id);
CREATE INDEX index_document_supplier_organisation_id ON document (document_supplier_organisation_id);
CREATE INDEX index_image_task_id ON document (image_task_id);
CREATE INDEX index_image_organisation_id ON document (image_organisation_id);
CREATE INDEX index_parent_property_entity_id ON document (parent_property_entity_id);
CREATE INDEX index_document_email_attachment_id ON document (document_email_attachment_id);


CREATE TABLE permission (
    id VARCHAR(64) PRIMARY KEY,
    item_type VARCHAR(32),
    groups VARCHAR(64)[] NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER trigger_update_updated_at_permission
BEFORE UPDATE ON permission
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE end_of_tenancy (
    id VARCHAR(64) PRIMARY KEY,
    tenancy_id VARCHAR(64),
    property_id VARCHAR(64),
    address VARCHAR(64),
    address_line2 VARCHAR(64),
    address_line3 VARCHAR(64),
    city VARCHAR(64),
    country VARCHAR(64),
    postcode VARCHAR(64),
    organisation_id VARCHAR(64) NOT NULL,
    landlord_id VARCHAR(64) NOT NULL,
    landlord_letter TEXT,
    landlord_confirmation TEXT,
    tenant_id VARCHAR(64) NOT NULL,
    primary_tenant_name VARCHAR(64) NOT NULL,
    tenant_letter TEXT,
    tenant_confirmation TEXT,
    final_confirmation TEXT,
    final_letter TEXT,
    end_date TIMESTAMP,
    stage VARCHAR(64),
    reference VARCHAR(128),
    old_price VARCHAR(128),
    proposal_price VARCHAR(128),
    final_price VARCHAR(128),
    status VARCHAR(64),
    task_ids text[],
    created_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
CREATE INDEX index_end_of_tenancy_organisation_id ON end_of_tenancy (organisation_id);
CREATE INDEX index_end_of_tenancy_property_id ON end_of_tenancy (property_id);
CREATE INDEX index_end_of_tenancy_tenancy_id ON end_of_tenancy (tenancy_id);

CREATE TABLE dashboard_skip_item (
    id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64),
    type VARCHAR(50) NOT NULL,
    item_id VARCHAR(64) NOT NULL,
    compliance_type VARCHAR(50) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX index_dashboard_item_id ON dashboard_skip_item (user_id);
CREATE INDEX index_dashboard_type ON dashboard_skip_item (type);

CREATE TABLE chatbot_conversation (
  id              UUID PRIMARY KEY,
  flow_conversation_id VARCHAR(255) NULL,
  user_id         VARCHAR(255) NOT NULL,
  organisation_id VARCHAR(255) NOT NULL,
  variables       JSONB,
  latest_tool_output    JSONB,
  created_at      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at      TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE chatbot_message (
  id                UUID PRIMARY KEY,
  conversation_id    UUID NOT NULL,
  type              VARCHAR(10) NOT NULL,
  content           TEXT NOT NULL,
  total_tokens      INT,
  prompt_tokens     INT,
  completion_tokens INT,
  created_at        TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at        TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tenancy_applications (
    id  UUID PRIMARY KEY,
    property_id UUID NOT NULL,
    tenancy_id UUID,
    status VARCHAR(50) NOT NULL,
    primary_landlord_id UUID,
    landlords UUID[],
    primary_tenant_id UUID,
    tenants UUID[],
    managers UUID[],
    move_in_date TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    remark TEXT,
    organisation_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE IF NOT EXISTS application_task_definitions (
    id UUID PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    task_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE IF NOT EXISTS application_tasks (
    id UUID PRIMARY KEY,
    application_id UUID NOT NULL,
    task_definition_id UUID NOT NULL,
    status VARCHAR(50) NOT NULL,
    details_json JSONB,
    documents UUID[],
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL
);
