datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model DocumentTemplateType {
  id             String     @id
  name           String
  index          Int
  organisationId String     @map("organisation_id")
  createdAt      DateTime?   @map("created_at")
  updatedAt      DateTime?  @map("updated_at")

  @@map("document_template_type")
}

model DocumentSupplier {
  id             String     @id
  userId         String     @map("user_id")
  documentId     String     @map("document_id")
  organisationId String     @map("organisation_id")
  createdAt      DateTime?  @map("created_at")
  updatedAt      DateTime?  @map("updated_at")

  @@map("document_supplier")
}

model DocumentTemplate {
  id                  String         @id
  title               String
  type                String?
  other               String[]
  status              String         @map("status")
  contractTypes       String[]       @map("contract_types")
  organisationId      String         @map("organisation_id")
  userId              String?        @map("user_id")
  docusignTemplateId  String?        @map("docusign_template_id")
  createdAt            DateTime?      @map("created_at")
  updatedAt           DateTime?      @map("updated_at")

  @@map("document_template")
}

model Document {
  id                                  String   @id
  key                                 String
  name                                String?
  type                                String?
  status                              String?
  description                         String?
  mimeType                            String   @map("mime_type")
  archived                            Boolean?
  createdAt                           DateTime? @map("created_at")
  updatedAt                           DateTime? @map("updated_at")
  validFrom                           DateTime? @map("valid_from")
  expiry                              DateTime?
  done                                Boolean?
  mutator                             String?
  documentTenancyId                   String?  @map("document_tenancy_id")
  documentPropertyId                  String?  @map("document_property_id")
  documentSupplierOrganisationId      String?  @map("document_supplier_organisation_id")
  documentOrganisationId              String?  @map("document_organisation_id")
  documentUserId                      String?  @map("document_user_id")
  documentConversationId              String?  @map("document_conversation_id")
  documentEmailAttachmentId           String?  @map("document_email_attachment_id")
  imageTaskId                         String?  @map("image_task_id")
  generalDocument                     Boolean? @map("general_document")
  isParentPropertyDocumentShared      Boolean? @map("is_parent_property_document_shared")
  imageOrganisationId                 String?  @map("image_organisation_id")
  parentPropertyEntityId              String?  @map("parent_property_entity_id")
  documentPermissionId                String?  @map("document_permission_id")
  documentTaskId                      String?  @map("document_task_id")
  documentLineItemId                  String?  @map("document_line_item_id")

  @@map("document")
}

model Permission {
  id                  String         @id
  itemType            String?        @map("item_type")
  groups              String[]
  createAt            DateTime?      @map("created_at")
  updatedAt           DateTime?      @map("updated_at")

  @@map("permission")
}

model EndOfTenancy {
  id                  String         @id
  tenancyId           String         @map("tenancy_id")
  propertyId               String    @map("property_id")
  address             String
  addressLine2       String?        @map("address_line2")
  addressLine3       String?        @map("address_line3")
  city                String?        @map("city")
  country             String?        @map("country")
  postcode             String?        @map("postcode")
  landlordId          String         @map("landlord_id")
  oldPrice          String         @map("old_price")
  proposalPrice          String?         @map("proposal_price")
  finalPrice          String?         @map("final_price")
  finalTerm           String?         @map("final_term")
  landlordLetter      String?         @map("landlord_letter")
  landlordConfirmation      String?         @map("landlord_confirmation")
  tenantId      String?         @map("tenant_id")
  tenantLetter      String?         @map("tenant_letter")
  tenantConfirmation      String?         @map("tenant_confirmation")
  finalConfirmation      String?         @map("final_confirmation")
  primaryTenantName      String?        @map("primary_tenant_name")
  finalLetter           String?         @map("final_letter")
  endDate                DateTime?   @map("end_date")
  stage               String
  status               String
  organisationId         String       @map("organisation_id")
  reference              String
  taskIds               String[]      @map("task_ids")
  createdBy             String?       @map("created_by")
  createdAt            DateTime?      @map("created_at")
  updatedAt           DateTime?      @map("updated_at")
  @@map("end_of_tenancy")
  @@index([propertyId])
}

model Feedback {
  id        String      @id @db.VarChar(64)
  text      String
  createAt  DateTime?   @default(now()) @map("created_at") @db.Timestamp(6)

  @@map("feedback")
}

model DashboardSkipItem{
  id                  String         @id
  userId              String         @map("user_id")
  type                String
  complianceType      String?        @map("compliance_type")
  itemId              String         @map("item_id")
  createdAt           DateTime?      @map("created_at")
  updatedAt           DateTime?      @map("updated_at")
  @@map("dashboard_skip_item")
}

model Conversation {
  id            String    @id
  flowConversationId String?   @map("flow_conversation_id")
  userId        String    @map("user_id")
  organisationId String   @map("organisation_id")
  variables     Json?
  latestToolOutput Json?  @map("latest_tool_output")
  createdAt     DateTime  @map("created_at")
  updatedAt     DateTime  @map("updated_at")
  @@map("chatbot_conversation")
}

model Message {
  id              String       @id
  conversationId  String       @map("conversation_id")
  type            String       // "user" or "ai"
  content         String
  tool_outputs    Json?        @map("tool_outputs")
  totalTokens     Int?    @map("total_tokens")
  promptTokens    Int?    @map("prompt_tokens")
  completionTokens Int?   @map("completion_tokens")
  createdAt     DateTime  @map("created_at")
  updatedAt     DateTime  @map("updated_at")
  @@map("chatbot_message")
}

model TenancyApplication {
  id                  String         @id
  propertyId          String         @map("property_id")
  tenancyId           String?        @map("tenancy_id")
  status              String         @map("status")
  primaryLandlordId   String?        @map("primary_landlord_id")
  landlords           String[]       @map("landlords")
  primaryTenantId     String?        @map("primary_tenant_id")
  tenants             String[]       @map("tenants")
  managers            String[]       @map("managers")
  moveInDate          DateTime?      @map("move_in_date")
  isDeleted           Boolean        @map("is_deleted")
  remark              String?
  organisationId      String         @map("organisation_id")
  createdAt           DateTime       @map("created_at")
  updatedAt           DateTime       @map("updated_at")

  @@map("tenancy_applications")
}

model ApplicationTaskDefinition {
  id                  String         @id
  category            String         @map("category")
  taskName            String         @map("task_name")
  createdAt           DateTime       @map("created_at")
  updatedAt           DateTime       @map("updated_at")

  @@map("application_task_definitions")
}

model ApplicationTask {
  id                  String         @id
  applicationId       String         @map("application_id")
  taskDefinitionId    String         @map("task_definition_id")
  status              String         @map("status")
  detailsJson         Json?          @map("details_json")
  documents           String[]       @map("documents")
  createdAt           DateTime       @map("created_at")
  updatedAt           DateTime       @map("updated_at")

  @@map("application_tasks")
}
