#!/bin/bash

if [ -n "${1}" ]; then
    ENV="${1}"
else
    echo "Environment variable 'ENV' does not exist, will check from export env"
fi

if [ -z "${ENV}" ]; then
    echo "Environment variable 'ENV' does not exist, will exit"
    exit 1
fi

ENV_FILE=./config/.env.${ENV}
if [ ! -f "$ENV_FILE" ]; then
    echo "Environment file '$ENV_FILE' does not exist."
    exit 1
fi
ls -l /mnt/secrets-store
SECRET_FILE=/mnt/secrets-store/secrets_loftyworks-main-service
if [ -f "$SECRET_FILE" ]; then
    echo "secret file found, will load secret config..."

    while IFS="=" read -r key value; do
        case "$key" in
            "DATABASE_URL"|"SUB_API_KEY"|"GOOGLE_APPLICATION_CREDENTIALS")
                export "$key=$value"
                echo "Exported $key"
                ;;
        esac
    done < <(jq -r 'to_entries|map("\(.key)=\(.value|tostring)")|.[]' $SECRET_FILE)
fi

# Read the file line by line and export variables
while IFS='=' read -r key value || [[ -n "$key" ]]; do
    # Ignore lines starting with # (comments) or empty lines
    if [[ ! "$key" =~ ^#.*$ ]] && [[ -n "$key" ]]; then
        export "$key"="$value"
    fi
done < "$ENV_FILE"

echo "Service start to run with environment [${ENV}]..."
exec node dist/main.js

