import { <PERSON>, Get, Logger, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { SubCategoryService } from '../../service/subCategory.service';
import { SubCategory } from '../../../common/model/subCategory';

@Controller('api/v1/sub-category')
@ApiExtraModels(BaseRes, SubCategory)
@ApiBearerAuth()
export class SubCategoryController {
    private readonly logger = new Logger('SubCategoryController', {
        timestamp: true,
    });

    constructor(private readonly subCategoryService: SubCategoryService) {}

    @ApiOperation({ summary: 'List SubCategories' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(SubCategory),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/list')
    public async listSubCategories(
        @Query('roomId') roomId: string,
        @Query('categoryId') categoryId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.subCategoryService.listSubCategories(
                roomId,
                categoryId,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
