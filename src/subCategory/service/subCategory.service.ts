import { Injectable, Logger } from '@nestjs/common';
import { SubCategoryRepository } from '../../common/repository/subCategory';

@Injectable()
export class SubCategoryService {
    private readonly logger = new Logger('SubCategoryService', {
        timestamp: true,
    });

    constructor(
        private readonly subCategoryRepository: SubCategoryRepository,
    ) {}

    async listSubCategories(roomId: string, categoryId: string) {
        const result =
            await this.subCategoryRepository.listByRoomIdAndCategoryId(
                roomId,
                categoryId,
            );
        return result.sort(function compare(a, b) {
            return a.index - b.index;
        });
    }
}
