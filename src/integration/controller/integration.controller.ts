import { <PERSON>, Get, Logger, Query, Req, Res } from '@nestjs/common';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from 'src/common/util/requestUtil';
import { ResponseCode } from 'src/common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { IntegrationService } from '../service/integration.service';
import { Response } from 'express';
import { XeroAccountsVO } from '../model/xeroAccounts';

@Controller('api/v1/integration')
@ApiExtraModels(BaseRes)
@ApiBearerAuth()
export class IntegrationController {
    private readonly logger = new Logger('IntegrationController', {
        timestamp: true,
    });

    constructor(private readonly integrationService: IntegrationService) {}

    @ApiOperation({ summary: 'Get integration status' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'object',
                        },
                    },
                },
            ],
        },
    })
    @Get('/getIntegrationStatus')
    public async getIntegrationStatus(
        @Query('type') type: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.integrationService.getIntegrationStatus(
                req.user['cognito:username'],
                req.user['custom:organisationId'],
                type,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('getIntegrationStatus error:', e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
    @ApiOperation({ summary: 'Get all integration status' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                        },
                    },
                },
            ],
        },
    })
    @Get('/getAllIntegrationStatus')
    public async getAllIntegrationStatus(
        @Query('userId') userId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.integrationService.getAllIntegrationStatus(
                    req.user['cognito:username'],
                    req.user['custom:organisationId'],
                    userId,
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('getAllIntegrationStatus error', e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'List XeroAccounts' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(XeroAccountsVO),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/xero-accounts')
    public async listXeroAccounts(@Req() req: LwRequest, @Res() res: Response) {
        try {
            const result = await this.integrationService.listXeroAccounts(
                req.user['custom:organisationId'],
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('list XeroAccounts error', e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
