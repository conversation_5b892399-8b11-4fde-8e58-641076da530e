import { Injectable, Logger } from '@nestjs/common';
import { checkOrganisation } from 'src/common/util/authorization';
import { UserRepository } from 'src/common/repository/user';
import { IntegrationRepository } from 'src/common/repository/integration';
import { AccountRepository } from '../../common/repository/account';
const lodash = require('lodash');

@Injectable()
export class IntegrationService {
    private readonly logger = new Logger('IntegrationService', {
        timestamp: true,
    });

    constructor(
        private readonly userRepository: UserRepository,
        private readonly integrationRepository: IntegrationRepository,
        private readonly accountRepository: AccountRepository,
    ) {}

    async getIntegrationStatus(
        cognitoId: string,
        organisationId: string,
        type: string,
    ) {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        organisationId = user?.currentOrganisation;
        try {
            const result: any = {
                status: 'NOT_CONNECTED',
            };
            const integrations =
                await this.integrationRepository.findIntegrationByOrganisationId(
                    organisationId,
                );
            const integration = integrations.find((item) => item.type === type);

            if (integration) {
                const {
                    status,
                    tenantName,
                    startDate,
                    remainingCallAmount,
                    updatedAt,
                    checkbookMetadata,
                } = integration;
                const invoiceMetadata = this.findMetadata(
                    integration,
                    'INVOICE',
                );
                result.status = status;
                result.tenantName = tenantName;
                result.startDate = startDate;
                result.remainingCalls = remainingCallAmount;
                result.invoiceLastUpdatedDate = invoiceMetadata
                    ? invoiceMetadata.lastUpdatedDate
                    : null;
                result.updatedAt = updatedAt;
                result.plaidRecordId = checkbookMetadata?.plaidRecordId;
            }
            return result;
        } catch (e) {
            this.logger.error('getIntegrationStatus error:', e.stack);
            return null;
        }
    }

    private findMetadata(integration: any, type: string) {
        const { metadata } = integration;
        this.logger.log(`metadata: ${JSON.stringify(metadata)}`);
        return metadata
            ? metadata.find((meta: any) => meta.recourseType === type)
            : null;
    }

    async getAllIntegrationStatus(
        cognitoId: string,
        organisationId: string,
        userId: string,
    ) {
        const that = this;
        const user = await this.userRepository.findByCognitoId(cognitoId);
        organisationId = user?.currentOrganisation;
        try {
            let res = [];
            const integrations =
                await this.integrationRepository.findIntegrationByOrganisationId(
                    organisationId,
                );
            const integrationStatusFunc = function (
                integrations: any,
                type: string,
                authorityType: string,
            ) {
                res = res.concat(
                    integrations.map((integration: any) => {
                        const result: any = {
                            status: 'NOT_CONNECTED',
                            type,
                            authorityType,
                        };
                        if (integration) {
                            const {
                                status,
                                tenantName,
                                startDate,
                                remainingCallAmount,
                                updatedAt,
                                emailAddress,
                                checkbookMetadata,
                                userId,
                            } = integration;
                            const invoiceMetadata = that.findMetadata(
                                integration,
                                'INVOICE',
                            );
                            result.status = status;
                            result.tenantName = tenantName;
                            result.startDate = startDate;
                            result.remainingCalls = remainingCallAmount;
                            result.invoiceLastUpdatedDate = invoiceMetadata
                                ? invoiceMetadata.lastUpdatedDate
                                : null;
                            result.updatedAt = updatedAt;
                            result.emailAddress = emailAddress;
                            result.plaidRecordId =
                                checkbookMetadata?.plaidRecordId;
                            result.userId = userId;
                        }
                        return result;
                    }),
                );
            };
            const integrationTypes = [
                'XERO',
                'QUICK_BOOKS',
                'CHIME',
                'GOOGLE',
                'OUTLOOK',
                'STRIPE',
                'CHECKBOOK',
            ];
            integrationTypes.forEach((type) => {
                integrationStatusFunc(
                    [integrations.find((item) => item.type === type)],
                    type,
                    null,
                );
            });
            integrationStatusFunc(
                this.filterIntegrations(integrations, 'GOOGLE_GMAIL', 'SHARED'),
                'GOOGLE_GMAIL',
                'SHARED',
            );
            integrationStatusFunc(
                this.filterIntegrations(
                    integrations,
                    'GOOGLE_GMAIL',
                    'PERSONAL',
                ).filter((integration) => integration.userId === userId),
                'GOOGLE_GMAIL',
                'PERSONAL',
            );
            integrationStatusFunc(
                this.filterIntegrations(
                    integrations,
                    'OUTLOOK_EMAIL',
                    'SHARED',
                ),
                'OUTLOOK_EMAIL',
                'SHARED',
            );
            integrationStatusFunc(
                this.filterIntegrations(
                    integrations,
                    'OUTLOOK_EMAIL',
                    'PERSONAL',
                ).filter((integration) => integration.userId === userId),
                'OUTLOOK_EMAIL',
                'PERSONAL',
            );
            return res;
        } catch (e) {
            this.logger.error('getAllIntegrationStatus error:', e);
            return [];
        }
    }

    private filterIntegrations(
        integrations: any,
        emailType: string,
        authorityType: string,
    ) {
        return integrations.filter(
            (item: any) =>
                item.type === emailType &&
                item.emailAddress &&
                item.authorityType === authorityType,
        );
    }

    async listXeroAccounts(organisationId: string) {
        const response = [];
        const integration =
            await this.integrationRepository.findXeroIntegration(
                organisationId,
            );
        if (!integration) return response;
        const ledgerCodes = await this.accountRepository.getXeroLedgerCodes(
            organisationId,
            integration.tenantId,
        );
        const byType = lodash.groupBy(ledgerCodes, 'type');

        const expenses = (byType['EXPENSE'] ? byType['EXPENSE'] : [])
            .concat(byType['DIRECTCOSTS'] ? byType['DIRECTCOSTS'] : [])
            .filter((a) => a.code)
            .sort((a1, a2) => a1.code.localeCompare(a2.code));

        response.push({
            type: 'EXPENSE',
            accounts: expenses,
        });

        Object.keys(byType)
            .filter((type) => type !== 'EXPENSE' && type !== 'DIRECTCOSTS')
            .forEach((type) => {
                response.push({
                    type: type,
                    accounts: byType[type]
                        .filter((a) => a.code)
                        .sort((a1, a2) => a1.code.localeCompare(a2.code)),
                });
            });

        return response;
    }
}
