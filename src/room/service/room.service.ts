import { Injectable, Logger } from '@nestjs/common';
import { RoomRepository } from '../../common/repository/room';
import { Room } from '../../common/model/room';

@Injectable()
export class RoomService {
    private readonly logger = new Logger('RoomService', {
        timestamp: true,
    });

    constructor(private readonly roomRepository: RoomRepository) {}

    async listRooms(): Promise<Room[]> {
        const res = await this.roomRepository.list();
        return res.sort(function compare(a, b) {
            return a.index - b.index;
        });
    }
}
