import { <PERSON>, <PERSON>, Lo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { RoomService } from '../../service/room.service';
import { Room } from '../../../common/model/room';

@Controller('api/v1/room')
@ApiExtraModels(BaseRes, Room)
@ApiBearerAuth()
export class RoomController {
    private readonly logger = new Logger('RoomController', {
        timestamp: true,
    });

    constructor(private readonly roomService: RoomService) {}

    @ApiOperation({ summary: 'list rooms' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Room),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/list')
    public async listRooms(@Req() req: LwRequest, @Res() res: Response) {
        try {
            const result = await this.roomService.listRooms();
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
