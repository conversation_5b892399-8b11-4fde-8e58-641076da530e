import {
    Injectable,
    CanActivate,
    ExecutionContext,
    UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class ApiKeyGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest<Request>();
        const requestApiKey = request.headers['x-api-key'];
        const apiKey = process.env.AI_API_KEY;
        if (!requestApiKey || requestApiKey !== apiKey) {
            throw new UnauthorizedException('Invalid API Key');
        }

        return true;
    }
}
