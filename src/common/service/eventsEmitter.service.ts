import { Injectable } from '@nestjs/common';
import EventsEmitter, { EventType } from '@rentancy-com/loftyworks-events';

export enum EventEmitterSource {
    PROPERTY_MANAGEMENT = 'lw.property-management',
    DOCUMENTS = 'lw.documents',
}

@Injectable()
export class EventsEmitterService {
    private eventsEmitterFactory(eventEmitterSource?: string) {
        switch (eventEmitterSource) {
            case EventEmitterSource.DOCUMENTS:
                return new EventsEmitter({
                    eventBusName: process.env.EVENT_BUS_NAME as string,
                    eventSource: EventEmitterSource.DOCUMENTS,
                    region: process.env.REGION as string,
                });
            case EventEmitterSource.PROPERTY_MANAGEMENT:
                return new EventsEmitter({
                    eventBusName: process.env.EVENT_BUS_NAME as string,
                    eventSource: EventEmitterSource.PROPERTY_MANAGEMENT,
                    region: process.env.REGION as string,
                });
        }
    }

    public async putEvents<T extends EventType>(events: T[], eventEmitterSource = EventEmitterSource.PROPERTY_MANAGEMENT): Promise<any[]> {
        return this.eventsEmitterFactory(eventEmitterSource).putEvents(events);
    }
}
