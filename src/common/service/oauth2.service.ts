import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import 'dotenv/config';

export interface OAuth2TokenResponse {
    access_token: string;
    token_type: string;
    expires_in: number;
    scope?: string;
}

export interface OAuth2Config {
    tokenUrl: string;
    clientId: string;
    clientSecret: string;
    grantType: string;
}

@Injectable()
export class OAuth2Service {
    private readonly logger = new Logger('OAuth2Service', { timestamp: true });
    private tokenCache: Map<string, { token: string; expiresAt: number }> =
        new Map();

    constructor(private readonly httpService: HttpService) {}

    async getAccessToken(): Promise<string> {
        const clientId = process.env.AUTH_SERVER_CLIENT_CREDENTIAL_ID;
        const clientSecret = process.env.AUTH_SERVER_CLIENT_CREDENTIAL_SECRET;

        if (this.tokenCache.has(clientId)) {
            const cached = this.tokenCache.get(clientId);
            if (cached && cached.expiresAt > Date.now()) {
                this.logger.debug(`Using cached token for ${clientId}`);
                return cached.token;
            } else {
                this.tokenCache.delete(clientId);
            }
        }

        try {
            this.logger.log(
                `Requesting new OAuth2 token for client: ${clientId}`,
            );

            const response = await firstValueFrom(
                this.httpService.post<OAuth2TokenResponse>(
                    process.env.AUTH_SERVER_TOKEN_URL,
                    null,
                    {
                        params: {
                            client_id:
                                process.env.AUTH_SERVER_CLIENT_CREDENTIAL_ID,
                            client_secret: clientSecret,
                            grant_type: 'client_credentials',
                        },
                    },
                ),
            );

            const tokenData = response.data;

            if (!tokenData.access_token) {
                throw new Error('No access token received from OAuth2 server');
            }

            if (tokenData.expires_in) {
                const expiresAt =
                    Date.now() + (tokenData.expires_in - 300) * 1000;
                this.tokenCache.set(clientId, {
                    token: tokenData.access_token,
                    expiresAt,
                });
                this.logger.debug(
                    `Token cached for ${clientId}, expires at: ${new Date(expiresAt)}`,
                );
            }

            return tokenData.access_token;
        } catch (error) {
            this.logger.error(
                `Failed to get OAuth2 token for ${clientId}: ${error.message}`,
                error.stack,
            );
            throw new Error(`OAuth2 token request failed: ${error.message}`);
        }
    }
}
