import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

export interface OAuth2TokenResponse {
    access_token: string;
    token_type: string;
    expires_in: number;
    scope?: string;
}

export interface OAuth2Config {
    tokenUrl: string;
    clientId: string;
    clientSecret: string;
    grantType: string;
}

@Injectable()
export class OAuth2Service {
    private readonly logger = new Logger('OAuth2Service', { timestamp: true });
    private tokenCache: Map<string, { token: string; expiresAt: number }> =
        new Map();

    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService,
    ) {}

    /**
     * 获取OAuth2访问令牌
     * @param config OAuth2配置
     * @param useCache 是否使用缓存，默认为true
     * @returns 访问令牌
     */
    async getAccessToken(
        config: OAuth2Config,
        useCache: boolean = true,
    ): Promise<string> {
        const cacheKey = `${config.clientId}_${config.tokenUrl}`;

        // 检查缓存
        if (useCache && this.tokenCache.has(cacheKey)) {
            const cached = this.tokenCache.get(cacheKey);
            if (cached && cached.expiresAt > Date.now()) {
                this.logger.debug(`Using cached token for ${config.clientId}`);
                return cached.token;
            } else {
                // 清除过期的缓存
                this.tokenCache.delete(cacheKey);
            }
        }

        try {
            this.logger.log(
                `Requesting new OAuth2 token for client: ${config.clientId}`,
            );

            const response = await firstValueFrom(
                this.httpService.post<OAuth2TokenResponse>(
                    config.tokenUrl,
                    null,
                    {
                        params: {
                            client_id: config.clientId,
                            client_secret: config.clientSecret,
                            grant_type: config.grantType,
                        },
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        timeout: 10000, // 10秒超时
                    },
                ),
            );

            const tokenData = response.data;

            if (!tokenData.access_token) {
                throw new Error('No access token received from OAuth2 server');
            }

            // 缓存令牌（提前5分钟过期以避免边界情况）
            if (useCache && tokenData.expires_in) {
                const expiresAt =
                    Date.now() + (tokenData.expires_in - 300) * 1000;
                this.tokenCache.set(cacheKey, {
                    token: tokenData.access_token,
                    expiresAt,
                });
                this.logger.debug(
                    `Token cached for ${config.clientId}, expires at: ${new Date(expiresAt)}`,
                );
            }

            return tokenData.access_token;
        } catch (error) {
            this.logger.error(
                `Failed to get OAuth2 token for ${config.clientId}: ${error.message}`,
                error.stack,
            );
            throw new Error(`OAuth2 token request failed: ${error.message}`);
        }
    }

    /**
     * 获取Loftyworks认证中心的访问令牌
     * @param environment 环境 (stage.eu, prod.eu, etc.)
     * @param useCache 是否使用缓存
     * @returns 访问令牌
     */
    async getLoftyworksToken(
        environment: string = 'stage.eu',
        useCache: boolean = true,
    ): Promise<string> {
        const config: OAuth2Config = {
            tokenUrl: `https://auth.${environment}.loftyworks.com/api/authcenter/oauth2/token`,
            clientId: this.configService.get<string>(
                'LOFTYWORKS_CLIENT_ID',
                'lettings',
            ),
            clientSecret: this.configService.get<string>(
                'LOFTYWORKS_CLIENT_SECRET',
                'key',
            ),
            grantType: 'client_credentials',
        };

        return this.getAccessToken(config, useCache);
    }

    /**
     * 使用访问令牌发起HTTP请求
     * @param url 请求URL
     * @param token 访问令牌
     * @param method HTTP方法
     * @param data 请求数据
     * @returns 响应数据
     */
    async makeAuthenticatedRequest<T = any>(
        url: string,
        token: string,
        method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
        data?: any,
    ): Promise<T> {
        try {
            const config = {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                timeout: 30000, // 30秒超时
            };

            let response;
            switch (method) {
                case 'GET':
                    response = await firstValueFrom(
                        this.httpService.get<T>(url, config),
                    );
                    break;
                case 'POST':
                    response = await firstValueFrom(
                        this.httpService.post<T>(url, data, config),
                    );
                    break;
                case 'PUT':
                    response = await firstValueFrom(
                        this.httpService.put<T>(url, data, config),
                    );
                    break;
                case 'DELETE':
                    response = await firstValueFrom(
                        this.httpService.delete<T>(url, config),
                    );
                    break;
                default:
                    throw new Error(`Unsupported HTTP method: ${method}`);
            }

            return response.data;
        } catch (error) {
            this.logger.error(
                `Authenticated request failed: ${error.message}`,
                error.stack,
            );
            throw new Error(`Authenticated request failed: ${error.message}`);
        }
    }

    /**
     * 清除指定客户端的令牌缓存
     * @param clientId 客户端ID
     * @param tokenUrl 令牌URL
     */
    clearTokenCache(clientId?: string, tokenUrl?: string): void {
        if (clientId && tokenUrl) {
            const cacheKey = `${clientId}_${tokenUrl}`;
            this.tokenCache.delete(cacheKey);
            this.logger.debug(`Cleared token cache for ${clientId}`);
        } else {
            // 清除所有缓存
            this.tokenCache.clear();
            this.logger.debug('Cleared all token cache');
        }
    }

    /**
     * 获取缓存状态
     * @returns 缓存信息
     */
    getCacheStatus(): Array<{
        key: string;
        expiresAt: Date;
        isExpired: boolean;
    }> {
        const status = [];
        for (const [key, value] of this.tokenCache.entries()) {
            status.push({
                key,
                expiresAt: new Date(value.expiresAt),
                isExpired: value.expiresAt <= Date.now(),
            });
        }
        return status;
    }
}
