import { Injectable, Logger } from '@nestjs/common';
import {
    DynamoDBDocument,
    BatchGetCommand,
    BatchWriteCommand,
} from '@aws-sdk/lib-dynamodb';
import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { v1 as uuidV1 } from 'uuid';
import { UpdateCommandInput } from '@aws-sdk/lib-dynamodb/dist-types/commands';
import { ENV_CONFIG } from '../constant/config.constant';

@Injectable()
export class DynamodbService {
    protected readonly ddb: DynamoDBDocument;
    protected readonly logger = new Logger(DynamodbService.name);

    constructor() {
        this.ddb = DynamoDBDocument.from(
            new DynamoDB({ region: ENV_CONFIG.REGION } as any),
        );
    }

    async create(tableName: string, item: any): Promise<any> {
        const params = {
            TableName: tableName,
            Item: this.addBaseFieldValue(item, false),
        };

        const result = await this.ddb.put(params);
        this.logger.debug(`Create success: ${JSON.stringify(result)}`);
        return result.Attributes;
    }

    async getOrganisationItems(
        tableName: string,
        index: string,
        fieldName: string,
        organisationId: string,
    ): Promise<any> {
        const params = {
            TableName: tableName,
            IndexName: index,
            KeyConditionExpression: '#fieldName = :fieldName',
            ExpressionAttributeNames: {
                '#fieldName': fieldName,
            },
            ExpressionAttributeValues: {
                ':fieldName': organisationId,
            },
        };
        return await this.query(params);
    }

    async update(tableName: string, id: string, item: any): Promise<any> {
        const updatedItem = this.addBaseFieldValue(item, true);
        const {
            updateExpression,
            expressionAttributeNames,
            expressionAttributeValues,
        } = this.buildUpdateExpression(updatedItem);

        const params = {
            TableName: tableName,
            Key: { id },
            UpdateExpression: updateExpression,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW' as const,
        } as UpdateCommandInput;

        const result = await this.ddb.update(params);
        this.logger.debug(`Update success: ${JSON.stringify(result)}`);
        return result.Attributes;
    }

    async delete(tableName: string, id: string): Promise<any> {
        const params = {
            TableName: tableName,
            Key: { id },
        };

        const result = await this.ddb.delete(params);
        this.logger.debug(`Delete success: ${JSON.stringify(result)}`);
        return result.Attributes;
    }

    async getItem(
        tableName: string,
        id: string,
        projectionExpression?: string,
        expressionAttributeNames?: Record<string, string>,
    ): Promise<any> {
        const params: any = {
            TableName: tableName,
            Key: { id },
        };

        if (projectionExpression) {
            params.ProjectionExpression = projectionExpression;
        }

        if (expressionAttributeNames) {
            params.ExpressionAttributeNames = expressionAttributeNames;
        }

        return (await this.ddb.get(params)).Item;
    }

    protected async batchGetItems(
        tableName: string,
        ids: string[],
    ): Promise<any[]> {
        if (!ids?.length) return [];

        const chunkedIds = this.chunkArray(ids, 100);
        const results: any[] = [];

        await Promise.all(
            chunkedIds.map(async (chunk) => {
                const keys = chunk.map((id) => ({ id }));
                const command = new BatchGetCommand({
                    RequestItems: {
                        [tableName]: { Keys: keys },
                    },
                });

                try {
                    const response = await this.ddb.send(command);
                    if (response.Responses?.[tableName]) {
                        results.push(...response.Responses[tableName]);
                    }
                } catch (error) {
                    this.logger.error(
                        `Error fetching batch items: ${chunk}`,
                        error,
                    );
                    throw error;
                }
            }),
        );

        return results;
    }

    async query(params: any): Promise<any[]> {
        this.logger.debug(`query: ${JSON.stringify(params)}`);
        const result = await this.ddb.query(params);
        return result.Items || [];
    }

    async scan(
        tableName: string,
        filterExpression?: string,
        expressionAttributeValues?: Record<string, any>,
        projectionExpression?: string,
        expressionAttributeNames?: Record<string, string>,
    ): Promise<any[]> {
        const items = [];
        let lastEvaluatedKey;

        do {
            const params: any = {
                TableName: tableName,
                ExclusiveStartKey: lastEvaluatedKey,
                ...(filterExpression && {
                    FilterExpression: filterExpression,
                    ExpressionAttributeValues: expressionAttributeValues,
                }),
            };
            if (projectionExpression) {
                params.ProjectionExpression = projectionExpression;
            }
            if (expressionAttributeNames) {
                params.ExpressionAttributeNames = expressionAttributeNames;
            }

            const result = await this.ddb.scan(params);
            items.push(...(result.Items || []));
            lastEvaluatedKey = result.LastEvaluatedKey;
        } while (lastEvaluatedKey);

        return items;
    }

    private addBaseFieldValue(item: any, isUpdate: boolean): any {
        const now = new Date().toISOString();
        const baseItem = { ...item };

        if (!isUpdate) {
            baseItem.id = baseItem.id || uuidV1();
            baseItem.createdAt = now;
        }
        baseItem.updatedAt = now;

        return baseItem;
    }

    private buildUpdateExpression(item: any): {
        updateExpression: string;
        expressionAttributeNames: Record<string, string>;
        expressionAttributeValues: Record<string, any>;
    } {
        const keys = Object.keys(item).filter((key) => key !== 'id');
        const updateExpression =
            'SET ' +
            keys.map((key) => `#${key} = :${key.toLowerCase()}`).join(', ');

        const expressionAttributeNames = keys.reduce((acc, key) => {
            acc[`#${key}`] = key;
            return acc;
        }, {});

        const expressionAttributeValues = keys.reduce((acc, key) => {
            acc[`:${key.toLowerCase()}`] = item[key];
            return acc;
        }, {});

        return {
            updateExpression,
            expressionAttributeNames,
            expressionAttributeValues,
        };
    }

    private chunkArray<T>(array: T[], size: number): T[][] {
        return Array.from({ length: Math.ceil(array.length / size) }, (_, i) =>
            array.slice(i * size, i * size + size),
        );
    }

    protected async batchUpdate(
        tableName: string,
        items: any[],
    ): Promise<void> {
        if (!items?.length) return;

        const CONCURRENT_LIMIT = 10;
        const chunks = this.chunkArray(items, CONCURRENT_LIMIT);

        for (const chunk of chunks) {
            await Promise.all(
                chunk.map(async (item) => {
                    const updatedItem = this.addBaseFieldValue(item, true);
                    const {
                        updateExpression,
                        expressionAttributeNames,
                        expressionAttributeValues,
                    } = this.buildUpdateExpression(updatedItem);

                    const params = {
                        TableName: tableName,
                        Key: { id: item.id },
                        UpdateExpression: updateExpression,
                        ExpressionAttributeNames: expressionAttributeNames,
                        ExpressionAttributeValues: expressionAttributeValues,
                        ReturnValues: 'ALL_NEW' as const,
                    } as UpdateCommandInput;

                    try {
                        await this.ddb.update(params);
                    } catch (error) {
                        this.logger.error(
                            `Failed to update item ${item.id}:`,
                            error,
                        );
                        throw error;
                    }
                }),
            );
        }
    }

    protected async batchWrite(
        tableName: string,
        items: { create?: any[]; delete?: { id: string }[] },
    ): Promise<void> {
        if (
            (!items.create || items.create.length === 0) &&
            (!items.delete || items.delete.length === 0)
        ) {
            return;
        }

        const BATCH_SIZE = 25;
        const createChunks = items.create
            ? this.chunkArray(items.create, BATCH_SIZE)
            : [];
        const deleteChunks = items.delete
            ? this.chunkArray(items.delete, BATCH_SIZE)
            : [];

        for (
            let i = 0;
            i < Math.max(createChunks.length, deleteChunks.length);
            i++
        ) {
            const writeRequests = [];

            if (createChunks[i]) {
                writeRequests.push(
                    ...createChunks[i].map((item) => ({
                        PutRequest: {
                            Item: this.addBaseFieldValue(item, false),
                        },
                    })),
                );
            }

            if (deleteChunks[i]) {
                writeRequests.push(
                    ...deleteChunks[i].map((item) => ({
                        DeleteRequest: {
                            Key: { id: item.id },
                        },
                    })),
                );
            }

            const command = new BatchWriteCommand({
                RequestItems: {
                    [tableName]: writeRequests,
                },
            });

            try {
                let unprocessedItems = {
                    RequestItems: { [tableName]: writeRequests },
                };
                let retries = 0;
                const MAX_RETRIES = 3;

                const response = await this.ddb.send(command);
                unprocessedItems = {
                    RequestItems: response.UnprocessedItems || {},
                };

                while (
                    unprocessedItems?.RequestItems?.[tableName]?.length > 0 &&
                    retries < MAX_RETRIES
                ) {
                    const retryCommand = new BatchWriteCommand({
                        RequestItems: unprocessedItems.RequestItems,
                    });

                    const response = await this.ddb.send(retryCommand);
                    unprocessedItems = {
                        RequestItems: response.UnprocessedItems || {},
                    };

                    if (unprocessedItems?.[tableName]?.length > 0) {
                        retries++;
                        const delay = Math.floor(
                            Math.random() * (1000 * Math.pow(2, retries)),
                        );
                        await new Promise((resolve) =>
                            setTimeout(resolve, delay),
                        );
                    }
                }

                if (unprocessedItems?.[tableName]?.length > 0) {
                    this.logger.warn(
                        `Failed to process all items after ${MAX_RETRIES} retries`,
                        unprocessedItems[tableName],
                    );
                    throw new Error(
                        'Failed to process all items in batch write',
                    );
                }
            } catch (error) {
                this.logger.error(
                    `Error in batch write operation for chunk ${i}:`,
                    error,
                );
                throw error;
            }
        }
    }

    protected async removeFields(
        tableName: string,
        id: string,
        attributes: string[],
    ) {
        const updateParts: string[] = [];
        const expressionAttributeNames: { [key: string]: string } = {};

        attributes.forEach((attr, index) => {
            const alias = `#attr${index}`;
            expressionAttributeNames[alias] = attr;
            updateParts.push(alias);
        });

        const params = {
            TableName: tableName,
            Key: { id },
            UpdateExpression: `remove ${updateParts.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
        };
        await this.ddb.update(params);
    }

    async transactWrite(items: any[]): Promise<any> {
        const params = {
            TransactItems: items,
        };

        await this.ddb.transactWrite(params);
    }
}
