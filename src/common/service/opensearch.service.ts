import { Logger } from '@nestjs/common';
import 'dotenv/config';

const { defaultProvider } = require('@aws-sdk/credential-provider-node'); // V3 SDK.
const { Client } = require('@opensearch-project/opensearch');
const { AwsSigv4Signer } = require('@opensearch-project/opensearch/aws');

const logger = new Logger('ESClient', { timestamp: true });

let client = null;

async function initESClient() {
    logger.log('initialize es client...');

    client = new Client({
        ...AwsSigv4Signer({
            region: process.env.REGION,
            service: 'es',
            getCredentials: () => {
                const credentialsProvider = defaultProvider();
                return credentialsProvider();
            },
        }),
        node: process.env.OPENSEARCH_ENDPOINT,
    });
}

initESClient();

export async function search(query: any): Promise<[any]> {
    const { body } = await client.search(query);
    return body.hits.hits.map((hit) => hit._source);
}

export async function searchWithTotal(
    query: any,
): Promise<{ total: number; hits: any[] }> {
    const { body } = await client.search({
        ...query,
        body: {
            ...query.body,
            track_total_hits: true,
        },
    });
    return {
        total: body.hits.total,
        hits: body.hits.hits.map((hit) => hit._source),
    };
}
