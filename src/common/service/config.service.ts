import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';
import 'dotenv/config';
import { Logger } from '@nestjs/common';

const logger = new Logger('DDBClient', { timestamp: true });
let ssmClient = null;

async function initConfig() {
    logger.log('initialize config...');
    ssmClient = new SSMClient({ region: process.env.REGION });
    const parameters = [
        {
            name: '/main-service/flow-chatbot-token',
            envKey: 'FLOW_ACCESS_KEY',
            withDecryption: false,
        },
        {
            name: '/main-service/ai-api-key',
            envKey: 'AI_API_KEY',
            withDecryption: true,
        },
    ];

    await Promise.all(
        parameters.map(async ({ name, envKey, withDecryption }) => {
            const value = await getParameter(name, withDecryption);
            if (value !== null) {
                process.env[envKey] = value;
                logger.log(`Loaded config: ${envKey}`);
            } else {
                logger.warn(`Failed to load config: ${envKey}`);
            }
        }),
    );

    logger.log('Config initialization completed.');
}

export async function getParameter(name: string, withDecryption = false) {
    try {
        const command = new GetParameterCommand({
            Name: name,
            WithDecryption: withDecryption,
        });
        const response = await ssmClient.send(command);
        return response.Parameter?.Value ?? null;
    } catch (error) {
        console.error(`Error fetching SSM parameter ${name}:`, error);
        return null;
    }
}
initConfig();
