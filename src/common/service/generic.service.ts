import { Injectable, Logger } from '@nestjs/common';
import { getTableName, TABLE } from 'src/common/constant/dynamodb.constants';
import { DynamodbService } from 'src/common/service/dynamodb.service';
import { v1 as uuidV1 } from 'uuid';

const CREATE_TABLE_SET = new Set([
    'OrganisationStripeCharges',
    'OrganisationTaskChecklist',
    'TaskChecklist',
    'Address',
    'TaskScheduler',
    'WorksOrder',
    'TaskComment',
    'ContactComment',
    'TaskLabel',
    'DocumentSuppliers',
    'DocumentTemplate',
    'DocumentTemplateTypes',
    'Document',
    'ProblemCard',
    'TemplateLetter',
    'WorksOrder',
    'File',
]) as Readonly<Set<string>>;

const UPDATE_TABLE_SET = new Set([
    'Organisation',
    'OrganisationTaskChecklist',
    'TaskChecklist',
    'Reminder',
    'TaskReminder',
    'Address',
    'OrganisationUser',
    'Account',
    'Board',
    'TaskScheduler',
    'AutomatedTasks',
    'WorksOrder',
    'TaskLabel',
    'DocumentTemplate',
    'Document',
    'ProblemCard',
    'TemplateLetter',
    'User',
    'File',
]) as Readonly<Set<string>>;

const DELETE_TABLE_SET = new Set([
    'TaskChecklist',
    'ApplicantScheduleViewing',
    'InterestedProperty',
    'TaskScheduler',
    'WorksOrder',
    'DocumentSuppliers',
    'DocumentTemplate',
    'Document',
    'ProblemCard',
    'TemplateLetter',
    'WorksOrder',
]) as Readonly<Set<string>>;

const GET_TABLE_SET = new Set([
    'Organisation',
    'OrganisationTaskChecklist',
    'TaskReminder',
    'OrganisationUserMetaData',
    'OrganisationUser',
    'Board',
    'Task',
    'TaskScheduler',
    'TaskLabel',
    'CategoryDocument',
    'ProblemCard',
]) as Readonly<Set<string>>;

@Injectable()
export class GenericService {
    private logger = new Logger('GenericService', { timestamp: true });

    constructor(private dynamodbService: DynamodbService) {}

    async create(info: any, modelType: string): Promise<any> {
        if (!CREATE_TABLE_SET.has(modelType)) {
            this.logger.log("create:CREATE_TABLE_SET hasn't " + modelType);
            return '';
        }
        const tableName = getTableName(modelType);
        this.logger.log(`create:tableName=${tableName}`);
        if (tableName != '') {
            if (!info.id) {
                info.id = uuidV1();
            }
            await this.dynamodbService.create(tableName, info);
            return info;
        }
        return '';
    }

    async update(info: any, modelType: string): Promise<any> {
        if (!UPDATE_TABLE_SET.has(modelType)) {
            this.logger.log("update:UPDATE_TABLE_SET hasn't " + modelType);
            return '';
        }
        const tableName = getTableName(modelType);
        this.logger.log(`update:tableName=${tableName}`);
        if (tableName != '' && info.id != '') {
            const res = await this.dynamodbService.update(
                tableName,
                info.id,
                info,
            );
            this.logger.log('item: ', JSON.stringify(res));
            if (tableName === TABLE.BOARD_TABLE) {
                /*await SubscriptionUtil.send(
                    SubscriptionUtil.SUBSCRIPTION.ON_UPDATE_BOARD(info.id),
                    res,
                );*/
            }
            return res;
        }
        return '';
    }

    async deleteItem(info: any, modelType: string): Promise<any> {
        if (!DELETE_TABLE_SET.has(modelType)) {
            this.logger.log("deleteItem:DELETE_TABLE_SET hasn't " + modelType);
            return '';
        }
        const tableName = getTableName(modelType);
        this.logger.log(`deleteItem:tableName=${tableName}`);
        if (tableName != '' && info.id != '') {
            return await this.dynamodbService.delete(tableName, info.id);
        }
        return '';
    }

    async getItem(
        info: any,
        modelType: string,
        checkModelType?: boolean,
    ): Promise<any> {
        if (checkModelType && !GET_TABLE_SET.has(modelType)) {
            this.logger.log("getItem:GET_TABLE_SET hasn't " + modelType);
            return '';
        }
        const tableName = getTableName(modelType);
        this.logger.log('getItem:tableName=' + tableName);
        const projectionExpression = info.fieldNames
            .filter((field: any) => typeof field === 'string')
            .map((field: string) => `#field_${field}`)
            .join(', ');
        const expressionAttributeNames = {};
        info.fieldNames
            .filter((field: any) => typeof field === 'string')
            .forEach((field: string) => {
                expressionAttributeNames[`#field_${field}`] = field;
            });
        if (tableName != '' && info.id != '') {
            return await this.dynamodbService.getItem(
                tableName,
                info.id,
                projectionExpression,
                expressionAttributeNames,
            );
        }
        return '';
    }
}
