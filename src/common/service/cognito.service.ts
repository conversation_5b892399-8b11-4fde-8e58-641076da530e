import {
    CognitoIdentityProvider,
    AuthFlowType,
    ChallengeNameType,
    InitiateAuthCommand,
    RespondToAuthChallengeCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { Injectable, Logger } from '@nestjs/common';
import { v1 as uuidV1 } from 'uuid';
import { decode } from 'jsonwebtoken';

@Injectable()
export class CognitoService {
    private readonly logger = new Logger(CognitoService.name);

    constructor() {}

    private readonly idp = new CognitoIdentityProvider({
        region: process.env.REGION,
    });

    async getCognitoUser(cognitoId: string) {
        try {
            const params = {
                Username: cognitoId,
                UserPoolId: process.env.COGNITO_USERPOOL_ID,
            };
            return await this.idp.adminGetUser(params);
        } catch (e) {
            this.logger.log(`getCognitoUser error:${e}`);
            return null;
        }
    }

    async initCognitoUser(email: string) {
        // create user in cognito
        const temporaryPassword = uuidV1().slice(-10);
        const params = {
            UserPoolId: process.env.COGNITO_USERPOOL_ID,
            Username: email,
            ForceAliasCreation: false,
            TemporaryPassword: temporaryPassword,
            UserAttributes: [
                {
                    Name: 'email',
                    Value: email,
                },
            ],
        };
        params.UserAttributes.push({
            Name: 'custom:createdIn',
            Value: 'Lofty',
        });
        console.log(`initCognitoUser:params=${JSON.stringify(params)}`);
        const result = await this.idp.adminCreateUser(params);
        console.log(`initCognitoUser:result=${JSON.stringify(result)}`);
        const cognitoId = result.User.Username;

        // update email to verified
        const updateParams = {
            UserPoolId: process.env.COGNITO_USERPOOL_ID,
            Username: cognitoId,
            UserAttributes: [
                {
                    Name: 'email_verified',
                    Value: 'true',
                },
            ],
        };
        await this.idp.adminUpdateUserAttributes(updateParams);

        // process forceChangePassword
        const authParams = {
            AuthFlow: AuthFlowType.USER_PASSWORD_AUTH,
            ClientId: process.env.COGNITO_CLIENT_ID,
            AuthParameters: {
                USERNAME: email,
                PASSWORD: temporaryPassword,
            },
        };
        console.log(`initCognitoUser:authParams=${JSON.stringify(authParams)}`);
        const authResult = await this.idp.initiateAuth(authParams);
        console.log(`initCognitoUser:authResult=${JSON.stringify(authResult)}`);

        const cParams = {
            ChallengeName: ChallengeNameType.NEW_PASSWORD_REQUIRED,
            ClientId: process.env.COGNITO_CLIENT_ID,
            ChallengeResponses: {
                USERNAME: email,
                NEW_PASSWORD: temporaryPassword,
            },
            Session: authResult.Session,
        };
        console.log(`initCognitoUser:cParams=${JSON.stringify(cParams)}`);
        const cResult = await this.idp.respondToAuthChallenge(cParams);
        console.log(`initCognitoUser:cResult=${JSON.stringify(cResult)}`);
        return cognitoId;
    }

    async addOrganisationToCognitoUser(
        cognitoId: string,
        organisationId: string,
    ) {
        const user = await this.getCognitoUser(cognitoId);
        if (!user) {
            return;
        }
        const attribute = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:eligibleWorkspaces',
        );
        const eligibleWorkspaces = attribute ? JSON.parse(attribute.Value) : {};
        let finalEligibleWorkspaces = JSON.stringify({
            [organisationId]: true,
        });
        const eligibleWorkspaceIds = Object.keys(eligibleWorkspaces);
        if (!attribute || !eligibleWorkspaceIds.includes(organisationId)) {
            finalEligibleWorkspaces = JSON.stringify(
                Object.assign({ [organisationId]: true }, eligibleWorkspaces),
            );
            const params = {
                UserPoolId: process.env.COGNITO_USERPOOL_ID,
                Username: cognitoId,
                UserAttributes: [
                    {
                        Name: 'custom:organisationId',
                        Value: organisationId,
                    },
                    {
                        Name: 'custom:eligibleWorkspaces',
                        Value: finalEligibleWorkspaces,
                    },
                ],
            };
            await this.idp.adminUpdateUserAttributes(params);
        }
    }

    async authenticateWithCustomChallenge(
        userEmail: string,
        simulatedToken: string,
    ) {
        try {
            // 1. init auth request
            const initAuthRequest = new InitiateAuthCommand({
                AuthFlow: AuthFlowType.CUSTOM_AUTH,
                ClientId: process.env.COGNITO_CLIENT_ID,
                AuthParameters: {
                    USERNAME: userEmail,
                },
            });
            const initAuthResult = await this.idp.send(initAuthRequest);

            if (!initAuthResult) {
                throw new Error(
                    'Authentication failed: null response from initiate auth',
                );
            }

            // 2. confirm challenge
            const challengeName = initAuthResult.ChallengeName;
            console.log(`[auth] Received challenge name: ${challengeName}`);

            if (challengeName !== 'CUSTOM_CHALLENGE') {
                throw new Error(
                    `Unexpected challenge name: ${challengeName}, expected: CUSTOM_CHALLENGE`,
                );
            }

            const session = initAuthResult.Session;
            if (!session) {
                throw new Error('Authentication failed: invalid session');
            }

            const challengeParams = initAuthResult.ChallengeParameters;
            if (!challengeParams || Object.keys(challengeParams).length === 0) {
                throw new Error(
                    'Authentication failed: missing challenge parameters',
                );
            }

            const username = challengeParams['USERNAME'];
            if (!username) {
                throw new Error('Authentication failed: invalid username');
            }

            console.log(
                `[auth] Successfully initiated auth for user: ${username}`,
            );

            // 3. response challenge
            const challengeRequest = new RespondToAuthChallengeCommand({
                ClientId: process.env.COGNITO_CLIENT_ID,
                ChallengeName: 'CUSTOM_CHALLENGE',
                ChallengeResponses: {
                    USERNAME: userEmail,
                    ANSWER: simulatedToken,
                },
                Session: session,
            });

            console.log(
                `[auth] Sending challenge response for user: ${username}`,
            );
            const challengeResult = await this.idp.send(challengeRequest);

            if (!challengeResult?.AuthenticationResult) {
                throw new Error(
                    'Authentication failed: invalid challenge response',
                );
            }

            const decodedToken = decode(
                challengeResult.AuthenticationResult.IdToken,
                { complete: true },
            );
            if (
                !decodedToken ||
                typeof decodedToken === 'string' ||
                typeof decodedToken.payload === 'string'
            ) {
                throw new Error('Invalid token format');
            }

            const jti = decodedToken.payload.jti as string | undefined;

            return {
                accessToken: challengeResult.AuthenticationResult.AccessToken,
                idToken: challengeResult.AuthenticationResult.IdToken,
                refreshToken: challengeResult.AuthenticationResult.RefreshToken,
                jti,
            };
        } catch (error) {
            console.error(
                '[auth] Error:',
                error instanceof Error ? error.message : String(error),
            );
            throw error;
        }
    }
}
