// kafka-consumer.service.ts
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Kafka, Consumer } from 'kafkajs';

@Injectable()
export class KafkaConsumerService implements OnModuleInit {
  private readonly logger = new Logger(KafkaConsumerService.name);
  private consumers: Record<string, Consumer> = {};

  constructor() {}

  async onModuleInit() {
    //await this.setupConsumer('CreateUser');
    //await this.setupConsumer('CreateTeam');
  }

  private readonly topicConfigs = {
    CreateUser: {
      brokers: ['10.0.0.1:9092', '10.0.0.2:9092'],
      groupId: 'user-consumer-group'
    },
    CreateTeam: {
      brokers: ['10.0.1.1:9093', '10.0.1.2:9093'],
      groupId: 'team-consumer-group'
    }
  };

  getConfig(topic: keyof typeof this.topicConfigs) {
    return this.topicConfigs[topic];
  }

  private async setupConsumer(topic: 'CreateUser' | 'CreateTeam') {
    const config = this.getConfig(topic);

    const kafka = new Kafka({
      clientId: `nestjs-${topic}-consumer`,
      brokers: config.brokers
    });

    const consumer = kafka.consumer({
      groupId: config.groupId,
      maxWaitTimeInMs: 5000,
      maxBytesPerPartition: 1048576 // 1MB
    });

    await consumer.connect();
    await consumer.subscribe({ topic, fromBeginning: false });

    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        this.logger.log(`[${topic}] 分区 ${partition} 收到消息`);
        try {
          const value = JSON.parse(message.value.toString());
          switch(topic) {
            case 'CreateUser':
              await this.handleUserCreation(value);
              break;
            case 'CreateTeam':
              await this.handleTeamCreation(value);
              break;
          }
        } catch (error) {
          this.logger.error(`消息处理失败: ${error.message}`, error.stack);
        }
      }
    });

    this.consumers[topic] = consumer;
  }

  private async handleUserCreation(data: any) {
    // 用户创建业务逻辑
    this.logger.log(`处理用户创建: ${JSON.stringify(data)}`);
  }

  private async handleTeamCreation(data: any) {
    // 团队创建业务逻辑
    this.logger.log(`处理团队创建: ${JSON.stringify(data)}`);
  }

  async onModuleDestroy() {
    for (const [topic, consumer] of Object.entries(this.consumers)) {
      await consumer.disconnect();
      this.logger.log(`已断开 ${topic} 消费者连接`);
    }
  }
}
