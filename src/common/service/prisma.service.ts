import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

BigInt.prototype["toJSON"] = function () {
  return this.toString();
};

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    await this.$connect();
  }

  async acquireLock(lockKey: number): Promise<boolean> {
    try {
      const result = await this.$queryRaw`SELECT pg_try_advisory_lock(${lockKey}) AS acquired`;
      const lockAcquired = (result as any)[0]?.acquired || false;
      return lockAcquired;
    } catch (error) {
      console.error('Error acquiring advisory lock:', error);
      return false;
    }
  }

  async releaseLock(lockKey: number) {
    try {
      await this.$queryRaw`SELECT pg_advisory_unlock(${lockKey})`;
    } catch (error) {
      console.error('Error releasing advisory lock:', error);
    }
  }

  
}