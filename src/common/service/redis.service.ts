import { Redis } from 'ioredis';
import { Injectable, Logger } from '@nestjs/common';
import 'dotenv/config';

@Injectable()
export class RedisService {
    protected readonly redis: Redis;
    protected readonly logger = new Logger(RedisService.name);

    constructor() {
        this.logger.log(
            `constructor:process.env.OPENCACHE_ENDPOINT=${process.env.OPENCACHE_ENDPOINT}`,
        );
        this.redis = new Redis({
            host: process.env.OPENCACHE_ENDPOINT,
            port: process.env.OPENCACHE_PORT
                ? parseInt(process.env.OPENCACHE_PORT)
                : 6379,
            connectTimeout: 10000,
            commandTimeout: 5000,
        });
        this.logger.log(`constructor:redis init finished`);
    }

    async set(key: string, value: any, ttl?: number): Promise<void> {
        if (ttl !== undefined && ttl > 0) {
            await this.redis.set(key, value, 'EX', ttl);
        } else {
            await this.redis.set(key, value);
        }
    }

    async setIfNotExist(key: string, value: any, ttl?: number): Promise<any> {
        let result: string;
        if (ttl !== undefined && ttl > 0) {
            result = await this.redis.set(key, value, 'EX', ttl, 'NX');
        } else {
            result = await this.redis.set(key, value, 'NX');
        }
        return result === 'OK';
      }

    async get(key: string): Promise<any> {
        return this.redis.get(key);
    }

    async del(key: string): Promise<number> {
        return await this.redis.del(key);
    }
}
