import { v1 as uuidV1 } from 'uuid';
import { BatchGetCommand, DynamoDBDocument } from '@aws-sdk/lib-dynamodb';
import { DynamoDB } from '@aws-sdk/client-dynamodb';
import { Logger } from '@nestjs/common';
import { Property } from 'src/document/model/document.model';
import { DocumentTemplateUser } from 'src/document/model/documentTemplate.model';
import 'dotenv/config';

const logger = new Logger('DDBClient', { timestamp: true });

export let ddb = null;

export const TABLE = Object.freeze({
    ACTIVITY_TABLE: `Activity-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    ADDRESS_TABLE: `Address-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    APPLICANT_SCHEDULE_VIEWING_TABLE: `ApplicantScheduleViewing-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    BOARD_TABLE: `Board-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    COLUMN_TABLE: `Column-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    CONVERSATION_TABLE: `Conversation-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    CONVERSATION_LINK_TABLE: `ConvoLink-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    FILE_TABLE: `File-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    INVITATION_TABLE: `Invitation-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    INTEGRATION_TABLE: `Integration-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    INVOICE_TABLE: `Invoice-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    INVOICE_LINE_ITEM_TABLE: `InvoiceLineItem-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    INVOICE_TENANCY_TABLE: `InvoiceTenancy-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    JOURNAL_TABLE: `Journal-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    ORGANISATION_TABLE: `Organisation-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    ORGANISATION_USER_TABLE: `OrganisationUser-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    PARENT_PROPERTY_ENTITY_TABLE: `ParentPropertyEntity-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    PERMISSIONS_TABLE: `Permissions-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    PROBLEM_CARD_TABLE: `ProblemCard-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    PROPERTY_ASSET_TABLE: `PropertyAsset-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    PROPERTY_TABLE: `Property-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    REMINDER_TABLE: `Reminder-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    SESSION_TABLE: `Session-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    SUPPLIER_PROPERTY_TABLE: `SupplierProperty-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    TASK_TABLE: `Task-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    TASK_LABEL_TABLE: `TaskLabel-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    TEMPLATE_LETTER_TABLE: `TemplateLetter-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    TENANCY_TABLE: `Tenancy-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    TENANCY_SETTINGS_TABLE: `TenancySettings-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    USER_TABLE: `User-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    USER_PREFERENCE_TABLE: `UserPreferences-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    USER_LOGIN_RECORD_TABLE: `UserLoginRecord-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    CATEGORY_TABLE: `Category-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    ROOM_TABLE: `Room-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
});

export const INDEX = Object.freeze({
    APPLICANT_SCHEDULE_VIEWING_ORGANISATION_INDEX: 'gsi-ByOrganisationId',
    BOARD_ORGANISATION_INDEX: 'gsi-OrgranisationBoards',
    COLUMN_BOARD_INDEX: 'gsi-BaordColumns',
    INVITATION_EMAIL_INDEX: 'gsi-ByEmail',
    INVOICE_LINE_ITEM_ORGANISATION_INDEX: 'gsi-OrganisationLineItems',
    PROBLEM_CARD_PROPERTY_INDEX: 'gsi-PropertyProblemCards',
    PROPERTY_ORGANISATION_INDEX: 'gsi-OrganisationProperties',
    REMINDER_ORGANISATION_INDEX: 'gsi-OrganisationReminders',
    SUPPLIER_PROPERTY_PROPERTY_INDEX: 'gsi-ByProperty',
    TASK_BOARD_INDEX: 'gsi-BoardTasks',
    TEMPLATE_ORGANISATION_INDEX: 'gsi-OrganisationTemplateLetters',
    TENANCY_INVOICE_INDEX: 'gsi-TenancyInvoices',
    TENANCY_ORGANISATION_INDEX: 'gsi-OrganisationTenancies',
    TENANCY_PROPERTY_INDEX: 'gsi-PropertyTenancies',
    TENANCY_SETTING_ORGANISATION_INDEX: 'gsi-OrganisationSettings',
    USER_COGNITO_ID_INDEX: 'gsi-ByCognitoId',
    USER_ORGANISATION_INDEX: 'gsi-UserOrganisations',
    TASK_LABEL_ORGANISATION_INDEX: 'gsi-OrganisationTaskLabels',
    ORGANISATION_INTEGRATIONS_INDEX: 'gsi-OrganisationIntegrations',
});

export function getTableName(modelType: string): string {
    if (!modelType) return '';
    const withSpaces = modelType.replace(/([A-Z])/g, ' $1').trim();
    const underscoreCase = withSpaces.toUpperCase().replace(/\s+/g, '_');
    const tableName = `${underscoreCase}_TABLE`;
    logger.log(
        'getTableName:modelType=' + modelType + ',tableName=' + tableName,
    );
    return TABLE[tableName];
}

export async function deleteItem(
    tableName: string,
    key: Record<string, string>,
): Promise<any> {
    const params = {
        TableName: tableName,
        Key: key,
    };

    const result = await ddb.delete(params);
    console.log(`delete success, result: ${JSON.stringify(result)}`);
    return true;
}

export async function create(tableName: string, itemInfo: any): Promise<any> {
    itemInfo = addBaseFieldValue(tableName, itemInfo, false);
    const params = {
        TableName: tableName,
        Item: itemInfo,
    };

    const result = await ddb.put(params);
    console.log(`create success, result ${JSON.stringify(result)}`);
    return result.Item;
}

export async function update(
    tableName: string,
    id: string,
    itemInfo: any,
): Promise<any> {
    itemInfo = addBaseFieldValue(tableName, itemInfo, true);
    const keys = Object.keys(itemInfo);
    const length = keys.length;
    let updateExpression: string = 'set ';
    const expressionAttributeNames: any = {};
    const expressionAttributeValues: any = {};
    for (let i = 0; i < length; i++) {
        const key = keys[i];
        if (key !== 'id') {
            const value = itemInfo[key];
            const attributeKey = `#${key}`;
            const attributeValue = `:${key.toLocaleLowerCase()}`;
            updateExpression = updateExpression.concat(
                `${attributeKey} = ${attributeValue},`,
            );
            expressionAttributeNames[attributeKey] = key;
            expressionAttributeValues[attributeValue] = value;
        }
    }
    updateExpression = updateExpression.slice(0, -1);
    const params = {
        TableName: tableName,
        Key: {
            id,
        },
        UpdateExpression: updateExpression,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
    };

    const result = await ddb.update(params);
    console.log(`update success, result: ${JSON.stringify(result)}`);
    return result.Attributes.Item;
}

function addBaseFieldValue(
    tableName: string,
    itemInfo: any,
    isUpdate: boolean,
    isDeleteTypeName: boolean = true,
    needModifyUpdateTimeWhenUpdate: boolean = true,
): any {
    if (isUpdate) {
        if (needModifyUpdateTimeWhenUpdate) {
            const currentTime: Date = new Date();
            itemInfo.updatedAt = currentTime.toISOString();
        }
        if (isDeleteTypeName) {
            delete itemInfo.__typename;
        }
    } else {
        if (
            itemInfo.id == undefined ||
            itemInfo.id == null ||
            itemInfo.id == ''
        ) {
            itemInfo.id = uuidV1();
        }
        const currentTime: Date = new Date();
        if (
            itemInfo.createdAt == undefined ||
            itemInfo.createdAt == null ||
            itemInfo.createdAt == ''
        ) {
            itemInfo.createdAt = currentTime.toISOString();
        }
        if (
            itemInfo.updatedAt == undefined ||
            itemInfo.updatedAt == null ||
            itemInfo.updatedAt == ''
        ) {
            itemInfo.updatedAt = currentTime.toISOString();
        }
        const suffix = `-${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
        itemInfo.__typename = tableName.replace(suffix, '');
    }
    return itemInfo;
}

export async function getItem(tableName: string, id: string): Promise<any>;
export async function getItem(
    tableName: string,
    id: string,
    projectionExpression: string,
    expressionAttributeNames: any,
): Promise<any>;

export async function getItem(
    tableName: string,
    id: string,
    projectionExpression?: string,
    expressionAttributeNames?: any,
): Promise<any> {
    const queryParams: any = {
        TableName: tableName,
        Key: {
            id,
        },
    };

    if (projectionExpression) {
        queryParams.ProjectionExpression = projectionExpression;
    }

    if (expressionAttributeNames) {
        queryParams.ExpressionAttributeNames = expressionAttributeNames;
    }

    return (await ddb.get(queryParams)).Item;
}

export async function scanTable(
    tableName: string,
    filterExpression?: string,
    expressionAttributeValues?: Record<string, any>,
) {
    const items = [];
    let lastEvaluatedKey = undefined;

    do {
        const scanParams: any = {
            TableName: tableName,
            ExclusiveStartKey: lastEvaluatedKey,
            ...(filterExpression && {
                FilterExpression: filterExpression,
                ExpressionAttributeValues: expressionAttributeValues || {},
            }),
        };

        const result = await ddb.scan(scanParams);
        items.push(...(result.Items || []));
        lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    return items;
}

export async function getItemsBatch(tableName: string, keys: string[]) {
    if (keys && keys.length > 0) {
        const chunkedIds = chunkArray(keys, 100);
        const results: any[] = [];
        await Promise.all(
            chunkedIds.map(async (chunk) => {
                const keys = chunk.map((id) => ({ id }));
                const requestItems: any = {
                    [tableName]: {
                        Keys: keys,
                    },
                };

                const command = new BatchGetCommand({
                    RequestItems: requestItems,
                });

                try {
                    const response = await ddb.send(command);

                    if (response.Responses && response.Responses[tableName]) {
                        results.push(...response.Responses[tableName]);
                    }
                } catch (error) {
                    console.error(
                        `Error fetching data for ids chunk: ${chunk}`,
                        error,
                    );
                }
            }),
        );
        return results;
    }
    return [];
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
        chunks.push(array.slice(i, Math.min(i + chunkSize, array.length)));
    }
    return chunks;
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/activity#createActivity } instead.
 */
export async function createActivity(itemInfo: any) {
    return await create(TABLE.ACTIVITY_TABLE, itemInfo);
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/tenancy#createTenancy } instead.
 */
export async function createTenancy(itemInfo: any) {
    return await create(TABLE.TENANCY_TABLE, itemInfo);
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/tenancy#getTenancyById } instead.
 */
export async function getTenancy(id: string) {
    return await getItem(TABLE.TENANCY_TABLE, id);
}

export async function createTenancySetting(itemInfo: any) {
    return await create(TABLE.TENANCY_SETTINGS_TABLE, itemInfo);
}

export async function getTenancySetting(id: string) {
    return await getItem(TABLE.TENANCY_SETTINGS_TABLE, id);
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/property#getPropertyById } instead.
 */
export async function getProperty(id: string) {
    return await getItem(TABLE.PROPERTY_TABLE, id);
}

export async function getUser(id: string): Promise<any>;
export async function getUser(
    id: string,
    attributes: string,
    expressionAttributeNames: any,
): Promise<any>;

// Single implementation handling both cases
export async function getUser(
    id: string,
    attributes?: string,
    expressionAttributeNames?: any,
): Promise<any> {
    if (attributes && expressionAttributeNames) {
        return await getItem(
            TABLE.USER_TABLE,
            id,
            attributes,
            expressionAttributeNames,
        );
    }

    return await getItem(TABLE.USER_TABLE, id);
}

export async function getUserByIds(ids: string[]) {
    const item = await getItemsBatch(TABLE.USER_TABLE, ids);
    return item;
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/task#getTasksByIds } instead.
 */
export async function getTaskByIds(ids: string[]) {
    const item = await getItemsBatch(TABLE.TASK_TABLE, ids);
    return item;
}

export async function getUserByCognitoId(cognitoId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':cognitoId': cognitoId,
        },
        IndexName: INDEX.USER_COGNITO_ID_INDEX,
        KeyConditionExpression: 'cognitoId = :cognitoId',
        TableName: TABLE.USER_TABLE,
    };

    return (await ddb.query(params)).Items[0];
}

export async function getDocTempUser(id: string) {
    const item = await getItem(TABLE.USER_TABLE, id);
    return convertDocTempUser(item);
}

export async function getDocTempUserList(ids: string[]) {
    const items = await getItemsBatch(TABLE.USER_TABLE, ids);
    return items.map((item) => convertDocTempUser(item));
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/organisation#getOrganisationList } instead.
 */
export async function listOrganisationList() {
    const items = await scanTable(TABLE.ORGANISATION_TABLE);
    return items;
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/organisation#getOrganisationById } instead.
 */
export async function getOrganisation(id: string) {
    return await getItem(TABLE.ORGANISATION_TABLE, id);
}

function convertDocTempUser(item: any) {
    const docTempUser = {
        id: item.id,
        cognitoEmail: item.cognitoEmail,
        fname: item.fname,
        sname: item.sname,
        emails: [],
    } as DocumentTemplateUser;
    for (const userEmail in item.emails) {
        docTempUser.emails.push({ email: item.emails[userEmail].email });
    }
    return docTempUser;
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/property#getPropertiesByIds } instead.
 */
export async function getPropertiesBatch(keys: string[]): Promise<Property[]> {
    return await getItemsBatch(TABLE.PROPERTY_TABLE, keys);
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/reminder#getRemindersByOrganisationId } instead.
 */
export async function getReminder(orgId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':reminderOrganisationId': orgId,
        },
        IndexName: INDEX.REMINDER_ORGANISATION_INDEX,
        KeyConditionExpression:
            'reminderOrganisationId = :reminderOrganisationId',
        TableName: TABLE.REMINDER_TABLE,
    };

    return (await ddb.query(params)).Items[0];
}

export async function getEotLetter(
    orgId: string,
    contactType,
    category,
    letterName,
) {
    const params = {
        ExpressionAttributeValues: {
            ':templateLetterOrganisationId': orgId,
            ':contactType': contactType,
            ':category': category,
            ':letterName': letterName,
        },
        IndexName: INDEX.TEMPLATE_ORGANISATION_INDEX,
        KeyConditionExpression:
            'templateLetterOrganisationId = :templateLetterOrganisationId',
        FilterExpression:
            'contactType = :contactType AND category = :category AND letterName = :letterName',
        TableName: TABLE.TEMPLATE_LETTER_TABLE,
    };

    return (await ddb.query(params)).Items[0];
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/tenancy#updateTenancy } instead.
 */
export async function updateTenancy(id: string, itemInfo: any) {
    return await update(TABLE.TENANCY_TABLE, id, itemInfo);
}

export async function getBoardsByOrganisationId(organisationId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':organisationId': organisationId,
        },
        IndexName: INDEX.BOARD_ORGANISATION_INDEX,
        KeyConditionExpression: 'boardOrganisationId = :organisationId',
        TableName: TABLE.BOARD_TABLE,
    };

    return (await ddb.query(params)).Items;
}

export async function getTaskLabelsByOrganisationId(organisationId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':organisationId': organisationId,
        },
        IndexName: INDEX.TASK_LABEL_ORGANISATION_INDEX,
        KeyConditionExpression: 'taskLabelOrganisationId = :organisationId',
        TableName: TABLE.TASK_LABEL_TABLE,
    };

    return (await ddb.query(params)).Items;
}

export async function getIntegrationsByOrganisationId(organisationId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':organisationId': organisationId,
        },
        IndexName: INDEX.ORGANISATION_INTEGRATIONS_INDEX,
        KeyConditionExpression: 'organisationId = :organisationId',
        TableName: TABLE.INTEGRATION_TABLE,
    };

    return (await ddb.query(params)).Items;
}

export async function findBoardTasks(
    boardId: string,
    status?: string,
    assignee?: string,
    columnId?: string,
    labelId?: string,
    parentId?: string,
) {
    const params: any = {
        ExpressionAttributeValues: {
            ':taskBoardId': boardId,
            ':status': 'DELETED',
            ':status1': 'ARCHIVED',
            ':status2': 'DRAFT',
        },
        ExpressionAttributeNames: {
            '#status': 'status',
        },
        IndexName: INDEX.TASK_BOARD_INDEX,
        KeyConditionExpression: 'taskBoardId = :taskBoardId',
        FilterExpression:
            '#status <> :status AND #status <> :status1 AND #status <> :status2',
        ScanIndexForward: false,
        TableName: TABLE.TASK_TABLE,
    };

    if (status) {
        params.ExpressionAttributeValues[':status'] = status;
        params.FilterExpression = '#status = :status';
    }

    if (assignee) {
        params.ExpressionAttributeValues[':assignee'] = assignee;
        params.FilterExpression = `${params.FilterExpression} AND taskUserId = :assignee`;
    }

    if (columnId) {
        params.ExpressionAttributeValues[':columnId'] = columnId;
        params.FilterExpression = `${params.FilterExpression} AND taskColumnId = :columnId`;
    }

    if (labelId) {
        params.ExpressionAttributeValues[':labelId'] = labelId;
        params.FilterExpression = `${params.FilterExpression} AND taskLabelId = :labelId`;
    }

    if (parentId) {
        params.ExpressionAttributeValues[':parentId'] = parentId;
        params.FilterExpression = `${params.FilterExpression} AND parentId = :parentId`;
    }

    const result = await ddb.query(params);
    return result.Items;
}

export async function findColumns(boardId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':columnBoardId': boardId,
        },
        IndexName: INDEX.COLUMN_BOARD_INDEX,
        KeyConditionExpression: 'columnBoardId = :columnBoardId',
        TableName: TABLE.COLUMN_TABLE,
    };

    const result = await ddb.query(params);
    return result.Items;
}

export async function findPropertyTenancies(propertyId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':tenancyPropertyId': propertyId,
            ':archived': true,
        },
        IndexName: INDEX.TENANCY_PROPERTY_INDEX,
        KeyConditionExpression: 'tenancyPropertyId = :tenancyPropertyId',
        FilterExpression: 'archived <> :archived',
        TableName: TABLE.TENANCY_TABLE,
    };

    const result = await ddb.query(params);
    return result.Items;
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/invoiceTenancy#getInvoicesByTenancyId } instead.
 */
export async function getInvoiceIdsByTenancyId(tenancyId: string) {
    const params = {
        ExpressionAttributeValues: {
            ':invoiceTenancyTenancyId': tenancyId,
        },
        IndexName: INDEX.TENANCY_INVOICE_INDEX,
        KeyConditionExpression:
            'invoiceTenancyTenancyId = :invoiceTenancyTenancyId',
        TableName: TABLE.INVOICE_TENANCY_TABLE,
    };

    const result = await ddb.query(params);
    return result.Items;
}

/**
 * @deprecated Will be removed in the future. Use {@link /src/common/repository/invoice#getInvoicesByIds } instead.
 */
export async function getInvoicesByIds(ids: string[]) {
    const item = await getItemsBatch(TABLE.INVOICE_TABLE, ids);
    return item;
}

export async function getTaskLabel(id: string) {
    return await getItem(TABLE.TASK_LABEL_TABLE, id);
}

export async function listHistoryUserLoginRecord() {
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    const oneMonthAgoISO = oneMonthAgo.toISOString();

    const items = await scanTable(
        TABLE.USER_LOGIN_RECORD_TABLE,
        'createdAt < :dateLimit',
        { ':dateLimit': oneMonthAgoISO },
    );
    return items.map((item) => ({
        id: item.id,
    }));
}

export async function listAllUserLoginRecord() {
    return await scanTable(TABLE.USER_LOGIN_RECORD_TABLE);
}

export async function deleteHistoryUserLoginRecord(id: string) {
    return await deleteItem(TABLE.USER_LOGIN_RECORD_TABLE, { id });
}

export async function listCategories() {
    const items = await scanTable(TABLE.CATEGORY_TABLE);
    return items.map((item) => ({
        id: item.id,
        name: item.name,
        icon: item.icon,
        rooms: item.rooms,
    }));
}

export async function listRooms() {
    const items = await scanTable(TABLE.ROOM_TABLE);
    return items.map((item) => ({
        id: item.id,
        name: item.name,
    }));
}
async function initDDB() {
    logger.log('initialize ddb...');
    ddb = DynamoDBDocument.from(new DynamoDB({ region: process.env.REGION }));
}

initDDB();
