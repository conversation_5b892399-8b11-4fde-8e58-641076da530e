import { Injectable, Logger } from '@nestjs/common';
import { OAuth2Service } from './oauth2.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import 'dotenv/config';

export interface UserRoleResponse {
    roleName: string;
    userId: number;
}

export interface LoftyBridgeConfig {
    serviceId: string;
    url: string;
    environment: string;
}

@Injectable()
export class LoftyBridgeService {
    private readonly logger = new Logger('LoftyBridgeService', {
        timestamp: true,
    });
    private readonly config: LoftyBridgeConfig;

    constructor(
        private readonly oauth2Service: OAuth2Service,
        private readonly httpService: HttpService,
    ) {}

    async getRoleNameByUserId(userId: string): Promise<string> {
        try {
            this.logger.log(`Getting role name for user ID: ${userId}`);
            const loftyBridgeUrl = process.env.LOFTY_BRIDGE_URL;

            const accessToken = await this.oauth2Service.getAccessToken();

            const url = `${loftyBridgeUrl}/userRole/getRoleNameByUserId`;

            const response = await firstValueFrom(
                this.httpService.get<string>(url, {
                    params: {
                        userId: userId,
                    },
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    },
                    timeout: 10000,
                }),
            );

            const roleName = response.data;
            this.logger.log(
                `Successfully retrieved role name for user ${userId}: ${roleName}`,
            );

            return roleName;
        } catch (error) {
            this.logger.error(
                `Failed to get role name for user ${userId}: ${error.message}`,
                error.stack,
            );
            throw new Error(
                `Failed to get role name for user ${userId}: ${error.message}`,
            );
        }
    }
}
