import { registerAs } from '@nestjs/config';

export default registerAs('oauth2', () => ({
  loftyworks: {
    clientId: process.env.LOFTYWORKS_CLIENT_ID || 'lettings',
    clientSecret: process.env.LOFTYWORKS_CLIENT_SECRET || 'key',
    environments: {
      'dev.uk': 'https://auth.dev.uk.loftyworks.com/api/authcenter/oauth2/token',
      'stage.uk': 'https://auth.stage.uk.loftyworks.com/api/authcenter/oauth2/token',
      'prod.uk': 'https://auth.prod.uk.loftyworks.com/api/authcenter/oauth2/token',
      'stage.eu': 'https://auth.stage.eu.loftyworks.com/api/authcenter/oauth2/token',
      'prod.eu': 'https://auth.prod.eu.loftyworks.com/api/authcenter/oauth2/token',
    },
  },
  cache: {
    enabled: process.env.OAUTH2_CACHE_ENABLED !== 'false',
    defaultTtl: parseInt(process.env.OAUTH2_CACHE_TTL || '3600', 10), // 1 hour
  },
  timeout: {
    request: parseInt(process.env.OAUTH2_REQUEST_TIMEOUT || '10000', 10), // 10 seconds
    authenticated: parseInt(process.env.OAUTH2_AUTHENTICATED_TIMEOUT || '30000', 10), // 30 seconds
  },
}));
