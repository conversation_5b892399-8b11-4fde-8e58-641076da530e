import { ApiProperty } from '@nestjs/swagger';

import { ParentType } from 'src/common/enum';
import { TaskStatus, TaskType } from 'src/common/enum/task';

export class Task {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    type: TaskType;

    @ApiProperty()
    index: number;

    @ApiProperty()
    deadline: Date;

    @ApiProperty()
    description: string;

    @ApiProperty()
    taskBoardId: string;

    @ApiProperty()
    taskColumnId: string;

    @ApiProperty()
    taskLabelId: string;

    @ApiProperty()
    taskProblemCardId: string;

    @ApiProperty()
    taskOrganisationId: string;

    @ApiProperty()
    taskUserId: string;

    @ApiProperty()
    referenceId: string;

    @ApiProperty()
    status: TaskStatus;

    @ApiProperty()
    parentType: ParentType;

    @ApiProperty()
    parentId: string;

    @ApiProperty()
    parentName: string;

    @ApiProperty()
    owner: string;

    @ApiProperty()
    time: boolean;

    @ApiProperty()
    note: string;

    @ApiProperty()
    images: string[];

    @ApiProperty()
    shareToExternalCalendar: boolean;

    @ApiProperty()
    mutator: string;

    @ApiProperty()
    linkedContractor: string;

    @ApiProperty()
    disabled: boolean;
}

export class PageTaskDto {
    @ApiProperty()
    nextToken?: string;
    @ApiProperty({ type: () => [Task] })
    items: Task[];
}
