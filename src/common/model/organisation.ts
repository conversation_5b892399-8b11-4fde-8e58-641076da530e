import { ApiProperty } from '@nestjs/swagger';

export class NPMetadata {
    @ApiProperty()
    apiKey: string;
    @ApiProperty()
    merchantId: string;
    @ApiProperty()
    siteId: string;
    @ApiProperty()
    rechargePercentage: number;
    @ApiProperty()
    maxCharge: number;
    @ApiProperty()
    ACHSwitch: boolean;
    @ApiProperty()
    CreditCardSwitch: boolean;
}

export enum OrganisationLedgerType {
    XERO = 'XERO',
    LOFTYWORKS = 'LOFTYWORKS',
}

export enum OrganisationSubscriptionType {
    LOFTYWORKS = 'LOFTYWORKS',
    LOFTYPAY = 'LOFTYPAY',
    FULL = 'FULL',
}

export enum JournalPeriod {
    PRIOR_MONTH = 'PRIOR_MONTH',
    CURRENT_MONTH = 'CURRENT_MONTH',
}

export class Organisation {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    emailId: string;

    @ApiProperty({ required: true })
    name: string;

    @ApiProperty()
    email: string;

    @ApiProperty()
    phone: string;

    @ApiProperty()
    whatsAppAddress: string;

    @ApiProperty()
    description: string;

    @ApiProperty()
    logo: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    customMail: string;

    @ApiProperty()
    emailStatus: string;

    @ApiProperty({ type: () => [LedgerCode] })
    ledgerCodes: LedgerCode[];

    @ApiProperty()
    expiredFlag: string;
    @ApiProperty()
    country: string;
    @ApiProperty()
    adminUser: string;
    @ApiProperty()
    dailyNotificationsEnabled: boolean;

    @ApiProperty()
    docusignAccountId: string;

    @ApiProperty()
    stripeFreeTrialCompleted: boolean;

    @ApiProperty()
    stripeChargeDayOfTheMonth: number;

    @ApiProperty()
    payer: boolean;

    @ApiProperty()
    rightmoveBranchID: string;

    @ApiProperty()
    stripeHasOutstandingPaymentIntent: boolean;

    @ApiProperty()
    stripePaymentMethodId: string;

    @ApiProperty()
    type: OrganisationType;

    @ApiProperty()
    zillowIntegration: boolean;

    @ApiProperty()
    zooplaBranchID: string;

    @ApiProperty()
    currency: string;
    @ApiProperty()
    botUser: string;

    @ApiProperty()
    landlordStatementTemplateType: string;
    @ApiProperty()
    conversationTags: ConversationTag[];

    @ApiProperty()
    website: string;

    @ApiProperty()
    clientBatchPayments: number[];

    @ApiProperty()
    clientBatchPaymentsOnDemand: boolean;
    @ApiProperty()
    addressLine1: string;
    @ApiProperty()
    addressLine2: string;
    @ApiProperty()
    addressLine3: string;
    @ApiProperty()
    city: string;
    @ApiProperty()
    state: string;
    @ApiProperty()
    postcode: string;
    @ApiProperty()
    invoiceTemplates: InvoiceTemplate[];
    @ApiProperty()
    trackingCategories: RentancyTrackingCategory[];
    @ApiProperty()
    rentancyApiKey: string;
    @ApiProperty()
    addCommissionBillVatInclusive: boolean;
    @ApiProperty()
    addCommissionBillVat: boolean;
    @ApiProperty()
    commissionBillVatNumber: string;
    @ApiProperty()
    transUnionConnectionEnabled: boolean;
    @ApiProperty()
    transUnionLandlordId: string;
    @ApiProperty()
    receiveMarketingEmailConsent: boolean;
    @ApiProperty()
    hideImportExportPage: boolean;
    @ApiProperty()
    nationalProcessingMetadata: NPMetadata;
    @ApiProperty()
    ledgerType: OrganisationLedgerType;
    @ApiProperty()
    subscriptionType: OrganisationSubscriptionType;
    @ApiProperty()
    rentancyMail: string;
    @ApiProperty()
    utcMinuteOffset: number;
    @ApiProperty()
    timeZoneName: string;
    @ApiProperty()
    enableJournal: boolean;
    @ApiProperty()
    journalDate: number;
    @ApiProperty()
    journalPeriod: JournalPeriod;
    @ApiProperty()
    payoutVersion: PayoutVersion;
}

export class RentancyTrackingCategory {
    @ApiProperty()
    name: string;
    @ApiProperty()
    xeroName: string;
}

export class InvoiceTemplate {
    @ApiProperty()
    type: string;
    @ApiProperty()
    templateName: string;
    @ApiProperty()
    rentDescription: string;
}

export enum OrganisationType {
    LANDLORD = 'LANDLORD',
    AGENT = 'AGENT',
    OCCUPIER = 'OCCUPIER',
}

export enum PayoutVersion {
    PROPERTY = 'PROPERTY',
    LANDLORD = 'LANDLORD',
}

export class LedgerCode {
    @ApiProperty()
    name: string;
    @ApiProperty()
    displayName: string;
    @ApiProperty()
    code: string;
    @ApiProperty()
    accounts: string[];
    @ApiProperty()
    includedInPayout: boolean;
    @ApiProperty()
    income: boolean;
}

export class ConversationTag {
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;
}
