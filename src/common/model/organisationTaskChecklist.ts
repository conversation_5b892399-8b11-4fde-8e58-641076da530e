import { ApiProperty } from '@nestjs/swagger';

export class OrganisationTaskChecklist {
    @ApiProperty()
    id: string;

    @ApiProperty()
    createdAt: string;

    @ApiProperty()
    defaultChecklists: Checklist[];

    @ApiProperty()
    name: string;

    @ApiProperty()
    organisationTaskChecklistOrganisationId: string;

    @ApiProperty()
    taskChecklistTaskId: string;

    @ApiProperty()
    updatedAt: string;
}

export class Checklist {
    @ApiProperty()
    name: string;
    @ApiProperty()
    status: boolean;
    @ApiProperty()
    updatedAt: string;
    @ApiProperty()
    userInitials: string;
}
