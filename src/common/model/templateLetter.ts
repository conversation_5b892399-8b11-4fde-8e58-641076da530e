import { ApiProperty } from '@nestjs/swagger';
import { UserType } from './user';

export class TemplateLetter {
    @ApiProperty({
        required: true,
    })
    id: string;
    @ApiProperty({
        required: true,
    })
    letterName: string;
    @ApiProperty({ required: true })
    contactType: UserType;
    @ApiProperty({ required: true })
    category: string;
    @ApiProperty()
    letterSubject: string;
    @ApiProperty()
    letterContent: string;
    @ApiProperty({ required: true })
    templateLetterOrganisationId: string;
}
