import { ApiProperty } from '@nestjs/swagger';

export enum BusinessType {
    PRIVATE = 'PRIVATE',
    PUBLIC = 'PUBLIC',
    UNIVERSITY = 'UNIVERSITY',
}

export class Business {
    @ApiProperty()
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    addressLine1: string;
    @ApiProperty()
    addressLine2: string;
    @ApiProperty()
    addressLine3: string;
    @ApiProperty()
    city: string;
    @ApiProperty()
    postcode: string;
    @ApiProperty()
    state: string;
    @ApiProperty()
    country: string;
    @ApiProperty()
    phone: string[];
    @ApiProperty()
    website: string;
    @ApiProperty()
    public: boolean;
    @ApiProperty()
    type: BusinessType;
}
