import { ApiProperty } from '@nestjs/swagger';

export class ParentPropertyEntity {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    addressLine1: string;

    @ApiProperty()
    city: string;

    @ApiProperty()
    landlords: string[];

    @ApiProperty()
    managers: string[];

    @ApiProperty()
    parentPropertyEntityOrganisationId: string;
}
