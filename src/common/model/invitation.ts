import { ApiProperty } from '@nestjs/swagger';

export class Invitation {
    @ApiProperty({ required: true })
    id: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    email: string;

    @ApiProperty()
    invitationOrganisationId: string;

    @ApiProperty()
    invitationUserId: string;

    @ApiProperty()
    inviterId: string;

    @ApiProperty()
    inviterName: string;

    @ApiProperty()
    tenancyId?: string;

    @ApiProperty()
    role: string;

    @ApiProperty()
    verified: boolean;

    @ApiProperty()
    token: string;

    @ApiProperty()
    updatedAt: Date;
}
