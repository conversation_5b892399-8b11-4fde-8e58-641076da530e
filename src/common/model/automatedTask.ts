import { ApiProperty } from '@nestjs/swagger';

import { Organisation } from './organisation';
import { TaskLabel } from './taskLabel';

export class AutomatedTask {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    assignee: string;

    @ApiProperty({ type: () => [Organisation] })
    organisation: Organisation;

    @ApiProperty()
    label: TaskLabel;

    @ApiProperty()
    automatedTasksLabelId: string;

    @ApiProperty()
    eventName: string;

    @ApiProperty()
    eventValue: string;

    @ApiProperty()
    active: boolean;

    @ApiProperty()
    description: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    updatedAt: Date;
}
