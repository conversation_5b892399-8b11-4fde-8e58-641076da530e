import { ApiProperty } from '@nestjs/swagger';

export class Board {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    columnCount: number;

    @ApiProperty()
    taskCount: number;

    @ApiProperty()
    taskRefId: string;

    @ApiProperty()
    referenceId: string;

    @ApiProperty()
    boardOrganisationId: string;
}
