import { ApiProperty } from '@nestjs/swagger';

export class Reminder {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    notifyStartDate: boolean;

    @ApiProperty()
    notifyEndDate: boolean;

    @ApiProperty()
    notifyTenancyRenewal: boolean;

    @ApiProperty()
    startDateNotificationDays: number[];

    @ApiProperty()
    endDateNotificationDays: number[];

    @ApiProperty()
    tenancyRenewalNotificationDays: number[];

    @ApiProperty()
    notifyBreakDate: boolean;

    @ApiProperty()
    breakDateNotificationDays: number[];

    @ApiProperty()
    notifyReviewDate: boolean;

    @ApiProperty()
    reviewDateNotificationDays: number[];

    @ApiProperty()
    notifyDocumentExpiry: boolean;

    @ApiProperty()
    documentExpiryNotificationDays: number[];

    @ApiProperty()
    notifyInventoryWarranty: boolean;

    @ApiProperty()
    inventoryWarrantyNotificationDays: number[];
}
