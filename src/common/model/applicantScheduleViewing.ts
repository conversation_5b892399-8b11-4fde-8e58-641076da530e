import { ApiProperty } from '@nestjs/swagger';

export class ScheduleViewingMetadata {
    @ApiProperty()
    gmailId: string;
    @ApiProperty()
    outlookId: string;
}

export class ApplicantScheduleViewing {
    @ApiProperty()
    id: string;
    @ApiProperty()
    organisationId: string;
    @ApiProperty()
    propertyId: string;
    @ApiProperty()
    applicantId: string;
    @ApiProperty()
    applicantPhoneNumber?: string;
    @ApiProperty()
    assignedToIds: string[];
    @ApiProperty()
    startDate: Date;
    @ApiProperty()
    endDate: Date;
    @ApiProperty()
    createdAt: Date;
    @ApiProperty()
    createdBy: string;
    @ApiProperty()
    updatedAt: Date;
    @ApiProperty()
    updatedBy: string;
    @ApiProperty()
    taskId: string;
    metadata: ScheduleViewingMetadata;
}
