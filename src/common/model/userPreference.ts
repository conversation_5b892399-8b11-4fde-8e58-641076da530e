import { ApiProperty } from '@nestjs/swagger';

export class UserPreference {
    @ApiProperty({
        required: true,
    })
    id: string;
    @ApiProperty()
    userPreferencesUserId: string;
    @ApiProperty()
    allowedNotifications: NotificationType[];
}

export enum NotificationType {
    EMAIL_CHAT_NOTIFICATIONS = 'EMAIL_CHAT_NOTIFICATIONS',
    SMS_CHAT_NOTIFICATIONS = 'SMS_CHAT_NOTIFICATIONS',
    EMAIL_PROPERTY_NOTIFICATIONS = 'EMAIL_PROPERTY_NOTIFICATIONS',
    SMS_PROPERTY_NOTIFICATIONS = 'SMS_PROPERTY_NOTIFICATIONS',
    EMAIL_TENANCY_NOTIFICATIONS = 'EMAIL_TENANCY_NOTIFICATIONS',
    SMS_TENANCY_NOTIFICATIONS = 'SMS_TENANCY_NOTIFICATIONS',
}
