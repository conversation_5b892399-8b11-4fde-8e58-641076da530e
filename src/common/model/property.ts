import { ApiProperty } from '@nestjs/swagger';

export class Property {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    addressLine1: string;

    @ApiProperty()
    city: string;

    @ApiProperty()
    country: string;

    @ApiProperty()
    landlords: string[];

    @ApiProperty()
    managers: string[];

    @ApiProperty()
    propertyOrganisationId: string;
}
