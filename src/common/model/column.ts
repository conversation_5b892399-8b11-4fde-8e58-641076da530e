import { ApiProperty } from '@nestjs/swagger';

export class Column {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    columnBoardId: string;

    @ApiProperty()
    columnOrganisationId: string;

    @ApiProperty()
    index: number;

    @ApiProperty()
    taskCount: number;

    @ApiProperty()
    owner: string;

    @ApiProperty()
    description: string;
}
