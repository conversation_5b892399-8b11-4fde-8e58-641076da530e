import { ApiProperty } from '@nestjs/swagger';

export class UserLoginSession {
    @ApiProperty({
        required: true,
    })
    id: string;
    @ApiProperty({
        required: true,
    })
    deviceId: string;
    @ApiProperty({
        required: true,
    })
    email: string;
    @ApiProperty({
        required: true,
    })
    jti: string;
    @ApiProperty({
        required: true,
    })
    accessToken: string;
    @ApiProperty({
        required: true,
    })
    refreshToken: string;
    @ApiProperty()
    createdAt?: Date;
}
