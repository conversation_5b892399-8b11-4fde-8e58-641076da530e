import { ApiProperty } from '@nestjs/swagger';

import { FrequencyPeriod } from 'src/common/enum/taskScheduler';

export class Frequency {
    @ApiProperty()
    count: number;

    @ApiProperty()
    frequencyPeriod: FrequencyPeriod;
}

export class TaskScheduler {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    taskSchedulerLabelId: string;

    @ApiProperty()
    taskSchedulerPropertyId: string;

    @ApiProperty({ type: () => [Frequency] })
    frequency: Frequency;

    @ApiProperty()
    active: boolean;
}
