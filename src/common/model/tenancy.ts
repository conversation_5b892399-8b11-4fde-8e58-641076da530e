import { ApiProperty } from '@nestjs/swagger';

import { Period, TenancyStatus, TenancyType } from 'src/common/enum/tenancy';

export class Tenancy {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty({
        required: true,
    })
    tenancyPropertyId: string;

    @ApiProperty()
    primaryTenant: string;

    @ApiProperty({
        required: true,
    })
    type: TenancyType;

    @ApiProperty({
        required: true,
    })
    status: TenancyStatus;

    @ApiProperty({
        required: true,
    })
    landlords: string[];

    @ApiProperty({
        required: false,
    })
    startDate: Date;

    @ApiProperty({
        required: false,
    })
    endDate: Date;

    @ApiProperty({
        required: false,
    })
    renewalDate: Date;

    @ApiProperty({
        required: false,
    })
    rentReviewDate: Date;

    @ApiProperty()
    rent: number;

    @ApiProperty()
    period: Period;

    @ApiProperty()
    address: string;

    @ApiProperty()
    reference: string;
}
