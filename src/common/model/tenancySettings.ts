import { ApiProperty } from '@nestjs/swagger';
import { LedgerCode } from './organisation';

export class RentDueAutoSettings {
    @ApiProperty()
    triggerOn: boolean;
    @ApiProperty()
    emailSender: string;
    @ApiProperty()
    emailContent: string;
    @ApiProperty()
    emailProvider: string;
}

export class LateRentAutoSettings {
    @ApiProperty()
    triggerOn: boolean;

    @ApiProperty()
    autoEmail: boolean;

    @ApiProperty()
    emailDelayDays: number;

    @ApiProperty()
    emailSender: string;

    @ApiProperty()
    emailContent: string;

    @ApiProperty()
    emailProvider: string;

    @ApiProperty()
    autoInvoice: boolean;

    @ApiProperty()
    invoiceDelayDays: number;

    @ApiProperty()
    amount: number;

    @ApiProperty()
    accountCode: LedgerCode;
}

export class FundDistribution {
    @ApiProperty()
    ledger: string;
    @ApiProperty()
    amount: number;
}

export class AutoJournalArrears {
    @ApiProperty()
    amount: number;

    @ApiProperty()
    ledgerCode: string;
    @ApiProperty()
    ledgerName: string;
}
export class TenancySettings {
    @ApiProperty({ required: true })
    id: string;

    @ApiProperty()
    addCommissionBillVat: boolean;

    @ApiProperty()
    rentCommission: number;

    @ApiProperty()
    percentageFee: number;

    @ApiProperty()
    fixedFee: number;

    @ApiProperty()
    feeType: string;

    @ApiProperty()
    currency: string;

    @ApiProperty()
    invoiceRentInAdvanceDays: number;

    @ApiProperty()
    autoInvoiceRent: boolean;

    @ApiProperty()
    createdAt: string;

    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    tenancyledgerCode: LedgerCode;

    @ApiProperty()
    accountCode: LedgerCode;

    @ApiProperty()
    feeLedgerCode: LedgerCode;

    @ApiProperty()
    updatedAt: string;

    @ApiProperty()
    nextInvoiceSendDate: string;

    @ApiProperty()
    nextInvoiceDueDate: string;

    @ApiProperty()
    sendInvoiceToTenant: boolean;

    @ApiProperty()
    stationary: string;

    @ApiProperty()
    contractValue: number;

    @ApiProperty()
    eRV: number;

    @ApiProperty()
    firstJournalRunDate: string;

    @ApiProperty()
    lastJournalRunDate: string;

    @ApiProperty()
    autoApprovalBill: boolean;

    @ApiProperty()
    rentDueAutoSettings: RentDueAutoSettings;

    @ApiProperty()
    lateRentAutoSettings: LateRentAutoSettings;

    @ApiProperty()
    fundDistribution: FundDistribution[];

    @ApiProperty()
    autoJournalArrears: AutoJournalArrears[];
}
