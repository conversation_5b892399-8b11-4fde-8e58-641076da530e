import { ApiProperty } from '@nestjs/swagger';

export class DocusignTemplate {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty({
        required: true,
    })
    docusignTemplateId: string;

    @ApiProperty({
        required: true,
    })
    docusignTemplatesOrganisationId: string;

    @ApiProperty({
        required: true,
    })
    name: string;

    @ApiProperty({})
    roles: string[];
}
