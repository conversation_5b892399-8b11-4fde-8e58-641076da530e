import { ApiProperty } from '@nestjs/swagger';

export class WebPushSubscriptionKeys {
    @ApiProperty()
    p256dh: string;
    @ApiProperty()
    auth: string;
}

export class WebPushSubscription {
    @ApiProperty()
    endpoint: string;
    @ApiProperty()
    expirationTime: string;

    @ApiProperty({ type: () => WebPushSubscriptionKeys })
    keys: WebPushSubscriptionKeys;
}

export class Subscriptions {
    @ApiProperty({ required: true })
    userId: string;
    @ApiProperty({ type: () => WebPushSubscription })
    subscription: WebPushSubscription;
}
