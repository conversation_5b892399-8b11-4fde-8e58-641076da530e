import { ApiProperty } from '@nestjs/swagger';
import { ParentType } from '../enum';

export class TaskReminder {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty({ type: () => [ReminderItem] })
    reminderItems: ReminderItem[];
    @ApiProperty()
    createdAt: Date;
    @ApiProperty()
    updatedAt: Date;
    @ApiProperty()
    taskReminderOrganisationId: string;
}
export class ReminderItem {
    @ApiProperty()
    name: string;
    @ApiProperty()
    enabled: boolean;
    @ApiProperty()
    firstReminder: number;
    @ApiProperty()
    secondReminder: number;
    @ApiProperty({
        enum: ParentType,
    })
    parentType: ParentType;
}
