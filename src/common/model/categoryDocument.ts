import { ApiProperty } from '@nestjs/swagger';

export class CategoryDocument {
    @ApiProperty({ required: true })
    categoryId: string;
    @ApiProperty({ required: true })
    orgnisationId: string;
    @ApiProperty({ required: true })
    key: string;
    @ApiProperty({ required: true })
    name: string;
    @ApiProperty({ required: true })
    mimeType: string;
    @ApiProperty()
    note?: string;
}
