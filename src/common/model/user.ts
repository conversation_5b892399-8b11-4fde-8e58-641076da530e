import { ApiProperty } from '@nestjs/swagger';
import { File } from './file';

export class User {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    companyName?: string;

    @ApiProperty()
    title?: string;

    @ApiProperty()
    fname?: string;

    @ApiProperty()
    sname?: string;

    @ApiProperty()
    cognitoId?: string;

    @ApiProperty()
    currentOrganisation?: string;

    @ApiProperty()
    cognitoEmail?: string;

    @ApiProperty()
    emails?: ContactEmail[];

    @ApiProperty()
    phones?: ContactPhone[];
    @ApiProperty()
    roles?: string[];

    @ApiProperty()
    userInvitationId?: string;

    @ApiProperty()
    type?: UserType;

    @ApiProperty()
    public: boolean;

    @ApiProperty()
    internalNotes?: string;

    @ApiProperty()
    createdIn?: string;
    @ApiProperty()
    createdAt?: Date;
    @ApiProperty()
    updatedAt?: Date;
    @ApiProperty()
    userImageId?: string;
    @ApiProperty()
    image?: File;
}

export class ContactEmail {
    @ApiProperty()
    email: string;
    @ApiProperty()
    type: string;
}

export class ContactPhone {
    @ApiProperty()
    cca2: string;
    @ApiProperty()
    phone: string;
    @ApiProperty()
    type: string;
    @ApiProperty()
    code: string;
}

export enum UserType {
    TENANT = 'TENANT',
    LANDLORD = 'LANDLORD',
    LAWYER = 'LAWYER',
    BROKER = 'BROKER',
    LEASING_AGENT = 'LEASING_AGENT',
    PROPERTY_MANAGER = 'PROPERTY_MANAGER',
    LANDLORD_GUARANTEED_RENT = 'LANDLORD_GUARANTEED_RENT',
    OWNER = 'OWNER',
    SUPPLIER = 'SUPPLIER',
    OWNER_GRO = 'OWNER_GRO',
    GUARANTOR = 'GUARANTOR',
    SOLICITOR = 'SOLICITOR',
    CONSULTANT = 'CONSULTANT',
    OTHER = 'OTHER',
    AGENT = 'AGENT',
    STAFF = 'STAFF',
    TEAM = 'TEAM',
    ADMIN = 'ADMIN',
    LOFTYPAY_ADMIN = 'LOFTYPAY_ADMIN',
    NEW = 'NEW',
    MANAGER = 'MANAGER',
    FINANCE = 'FINANCE',
    INBOX = 'INBOX',
    SUSPENDED = 'SUSPENDED',
    LEASEHOLDER = 'LEASEHOLDER',
    APPLICANT = 'APPLICANT',
    SYSTEM = 'SYSTEM',
    INFORMAL = 'INFORMAL',
    HELPDESK = 'HELPDESK',
    ADMIN_AGENT = 'ADMIN_AGENT',
    PROSPECT = 'PROSPECT',
    PERMITTED_OCCUPIER = 'PERMITTED_OCCUPIER',
    UNKNOWN_INBOUND = 'UNKNOWN_INBOUND',
}

export enum Role {
    TENANT = 'TENANT',
    COLLEGE = 'COLLEGE',
    THIRD_PARTY = 'THIRD_PARTY',
    LANDLORD = 'LANDLORD',
    AGENT = 'AGENT',
    GUARANTOR = 'GUARANTOR',
    HELPDESK = 'HELPDESK',
    ADMIN_AGENT = 'ADMIN_AGENT',
    UNASSIGNED = 'UNASSIGNED',
    MANAGER = 'MANAGER',
    FINANCE = 'FINANCE',
    INBOX = 'INBOX',
    LOFTYPAY_ADMIN = 'LOFTYPAY_ADMIN',
}
