import { ApiProperty } from '@nestjs/swagger';
import { File } from './file';

export enum Period {
    DAILY = 'DAILY',
    WEEKLY = 'WEEKLY',
    TWO_WEEKLY = 'TWO_WEEKLY',
    MONTHLY = 'MONTHLY',
    QUARTERLY = 'QUARTERLY',
    UK_QUARTERLY = 'UK_QUARTERLY',
    SIX_MONTHLY = 'SIX_MONTHLY',
    ANNUALLY = 'ANNUALLY',
    BI_ANNUALLY = 'BI_ANNUALLY',
    FIVE_YEAR = 'FIVE_YEAR',
    TEN_YEAR = 'TEN_YEAR',
    FIFTEEN_YEAR = 'FIFTEEN_YEAR',
    TWENTY_YEAR = 'TWENTY_YEAR',
    TWENTY_FIVE_YEAR = 'TWENTY_FIVE_YEAR',
}

export enum ConversationCategory {
    SHARED = 'SHARED',
    PERSONAL = 'PERSONAL',
    ARCHIVED = 'ARCHIVED',
}

export class PinConversation {
    @ApiProperty()
    conversationCategory: ConversationCategory;
    @ApiProperty()
    pinConversationIds: string[];
}

export enum ApplicantStatus {
    NEW = 'NEW',
    CONTACT_MADE = 'CONTACT_MADE',
    APPLICANT_QUESTIONNAIRE_SENT = 'APPLICANT_QUESTIONNAIRE_SENT',
    VIEWING_SCHEDULED = 'VIEWING_SCHEDULED',
    OFFER_MADE = 'OFFER_MADE',
    OFFER_ACCEPTED = 'OFFER_ACCEPTED',
    START_REFERENCING = 'START_REFERENCING',
    LOST_LEAD = 'LOST_LEAD',
    NEW_LEADS = 'NEW_LEADS',
    ATTEMPTING_CONTACT = 'ATTEMPTING_CONTACT',
    NURTURING_COLD = 'NURTURING_COLD',
    WARM = 'WARM',
    HOT = 'HOT',
    APPOINTMENT_SET = 'APPOINTMENT_SET',
    SHOWING = 'SHOWING',
    PENDING = 'PENDING',
    CLOSED = 'CLOSED',
    BAD_LEADS = 'BAD_LEADS',
    DO_NOT_CONTACT = 'DO_NOT_CONTACT',
    NULL = 'NULL',
}

export class ApplicantMetaData {
    @ApiProperty()
    status: ApplicantStatus;
    @ApiProperty()
    lettingsNegotiators: string[];
    @ApiProperty()
    expectedMoveIn: Date;
    @ApiProperty()
    monthlyIncome: number;
    @ApiProperty()
    expectedMaxRent: number;
    @ApiProperty()
    expectedMinRent: number;
    @ApiProperty()
    expectedMinBedRooms: number;
    @ApiProperty()
    expectedMinBathRooms: number;
    @ApiProperty()
    expectedCity: string;
    @ApiProperty()
    expectedState: string;
    @ApiProperty()
    occupantCount: number;
    @ApiProperty()
    extraNotes: string;
    @ApiProperty()
    allowPets: string[];
    @ApiProperty()
    guarantorId: string;
    @ApiProperty()
    applicantType: string;
}

export class User {
    @ApiProperty({
        required: true,
    })
    id: string;

    @ApiProperty()
    companyName?: string;

    @ApiProperty()
    title?: string;

    @ApiProperty()
    fname?: string;

    @ApiProperty()
    sname?: string;

    @ApiProperty()
    cognitoId?: string;

    @ApiProperty()
    currentOrganisation?: string;

    @ApiProperty()
    email?: string;

    @ApiProperty()
    cognitoEmail?: string;

    @ApiProperty()
    emails?: ContactEmail[];

    @ApiProperty()
    nameConfirmed?: boolean;

    @ApiProperty()
    phones?: ContactPhone[];
    @ApiProperty()
    roles?: string[];

    @ApiProperty()
    userInvitationId?: string;

    @ApiProperty()
    type?: UserType;

    @ApiProperty()
    public: boolean;

    @ApiProperty()
    internalNotes?: string;

    @ApiProperty()
    createdIn?: string;
    @ApiProperty()
    createdAt?: Date;
    @ApiProperty()
    updatedAt?: Date;
    @ApiProperty()
    userImageId?: string;
    @ApiProperty()
    image?: File;
    @ApiProperty()
    incomeFrequency?: Period;
    @ApiProperty()
    pinConversations?: PinConversation[];
    @ApiProperty()
    doNotDisturbConversationIds?: string[];
    @ApiProperty()
    loftyAgentBaseId?: string;
    @ApiProperty()
    forceSignOut?: boolean;
    @ApiProperty()
    addressLine1?: string;
    @ApiProperty()
    addressLine2?: string;
    @ApiProperty()
    addressLine3?: string;
    @ApiProperty()
    locality?: string;
    @ApiProperty()
    region?: string;
    @ApiProperty()
    socialSecurityNumber?: string;
    @ApiProperty()
    employmentStatus?: string;
    @ApiProperty()
    dateOfBrithday?: Date;
    @ApiProperty()
    income?: string;
    @ApiProperty()
    whatsAppNumber?: string;
    @ApiProperty()
    onboardingStep?: number;
    @ApiProperty()
    onboardingSeen?: boolean;
    @ApiProperty()
    userBusinessId?: string;
    @ApiProperty()
    applicantMetaData?: ApplicantMetaData;
}

export class ContactEmail {
    @ApiProperty()
    email: string;
    @ApiProperty()
    type: string;
}

export class ContactPhone {
    @ApiProperty()
    cca2: string;
    @ApiProperty()
    phone: string;
    @ApiProperty()
    type: string;
    @ApiProperty()
    code: string;
}

export enum UserType {
    TENANT = 'TENANT',
    LANDLORD = 'LANDLORD',
    LAWYER = 'LAWYER',
    BROKER = 'BROKER',
    LEASING_AGENT = 'LEASING_AGENT',
    PROPERTY_MANAGER = 'PROPERTY_MANAGER',
    LANDLORD_GUARANTEED_RENT = 'LANDLORD_GUARANTEED_RENT',
    OWNER = 'OWNER',
    SUPPLIER = 'SUPPLIER',
    OWNER_GRO = 'OWNER_GRO',
    GUARANTOR = 'GUARANTOR',
    SOLICITOR = 'SOLICITOR',
    CONSULTANT = 'CONSULTANT',
    OTHER = 'OTHER',
    AGENT = 'AGENT',
    STAFF = 'STAFF',
    TEAM = 'TEAM',
    ADMIN = 'ADMIN',
    LOFTYPAY_ADMIN = 'LOFTYPAY_ADMIN',
    NEW = 'NEW',
    MANAGER = 'MANAGER',
    FINANCE = 'FINANCE',
    INBOX = 'INBOX',
    SUSPENDED = 'SUSPENDED',
    LEASEHOLDER = 'LEASEHOLDER',
    APPLICANT = 'APPLICANT',
    SYSTEM = 'SYSTEM',
    INFORMAL = 'INFORMAL',
    HELPDESK = 'HELPDESK',
    ADMIN_AGENT = 'ADMIN_AGENT',
    PROSPECT = 'PROSPECT',
    PERMITTED_OCCUPIER = 'PERMITTED_OCCUPIER',
    UNKNOWN_INBOUND = 'UNKNOWN_INBOUND',
}

export enum Role {
    TENANT = 'TENANT',
    COLLEGE = 'COLLEGE',
    THIRD_PARTY = 'THIRD_PARTY',
    LANDLORD = 'LANDLORD',
    AGENT = 'AGENT',
    GUARANTOR = 'GUARANTOR',
    HELPDESK = 'HELPDESK',
    ADMIN_AGENT = 'ADMIN_AGENT',
    UNASSIGNED = 'UNASSIGNED',
    MANAGER = 'MANAGER',
    FINANCE = 'FINANCE',
    INBOX = 'INBOX',
    LOFTYPAY_ADMIN = 'LOFTYPAY_ADMIN',
}
