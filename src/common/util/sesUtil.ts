import { SES } from '@aws-sdk/client-ses';
import { ENV_CONFIG } from '../constant/config.constant';
import { SendTemplatedEmailCommandInput } from '@aws-sdk/client-ses/dist-types/commands/SendTemplatedEmailCommand';

const sesClient = new SES({ region: ENV_CONFIG.SES_REGION } as any);

export class SesUtil {
    static async verifyEmailIdentity(email: string) {
        await sesClient.verifyEmailIdentity({ EmailAddress: email });
    }

    static async sendTemplatedEmail(request: SendTemplatedEmailCommandInput) {
        await sesClient.sendTemplatedEmail(request);
    }
}
