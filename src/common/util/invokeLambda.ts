const { LambdaClient, InvokeCommand } = require("@aws-sdk/client-lambda");

const client = new LambdaClient({ region: process.env.REGION });

export async function invokeLambda(functionName, payload): Promise<any> {
    try {
        const command = new InvokeCommand({
            FunctionName: functionName,
            InvocationType: "RequestResponse",
            Payload: Buffer.from(JSON.stringify(payload)),
        });
        const response = await client.send(command);
        if (response.Payload) {
            const result = JSON.parse(Buffer.from(response.Payload).toString());
            console.log("Lambda Response:", result.body);
            return result.body;
        } else {
            console.log("Lambda invoked successfully, but no payload was returned.");
            return {};
        }
    } catch (error) {
        console.error("Error invoking Lambda:", error);
        return undefined;
    }
}
async function test() {
    const result = await invokeLambda('rentancy-core-devuk-end-of-tenancy-letter-content', {
        queryStringParameters: {
            organisationId: "test",
            userId: "67ccdac8-5e05-461d-86d5-736509968172",
            propertyId: "48bf9330-e378-11ef-8f96-1794890f4ddd",
            tenancyId: "02953ef0-e440-11ef-8f9c-0fd1f7d05408",
            landlordId: "026390bb-d1c2-41cd-9d87-d063119169b8",
            eotId: "61e9d454-ed9c-11ef-a51f-cf2df99c82fa",
            EndOfTenancy_Status: "Active",
            Rent_Increase_Amount: "2100",
            Contract_Renewal_Duration: "3m",
            Contract_Renewal_Pending: "true",
            Contract_Renewal_False: "true",
            templateContent: `Dear $Landlord_FName$, 

    I hope you are well. 

    We are writing to advise that your tenant $Primary_Tenant_First_Name$ occupying $Property_Address_Line1$ is expiring on $Contract_End_Date$. 

    To confirm how you’d like to proceed, please confirm next steps, and we will contact your tenant(s) accordingly.  

    $ConfirmNextStepsURL$



    Very best,  

    $Property_Manager_First_Name$ $Property_Manager_Last_Name$ 
    $Property_Manager_Email$  
    $Property_Manager_Phone_Number$  
    $Workspace_Name$ `
        }
    });
    const {letter, missingVariables} =  JSON.parse(result);
    // console.log("======\n", letter);
    console.log(missingVariables);
}

// test()