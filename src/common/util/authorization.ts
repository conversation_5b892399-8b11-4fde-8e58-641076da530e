class AuthorizationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AuthorizationError';
    }
}

const AuthorizationErrorMessages = Object.freeze({
    UNABLE_TO_GET_IDENTITY_CLAIMS:
        'Unable to get identity claims. Function called from non-authorized context.',
    USER_DOES_NOT_HAVE_PERMISSION:
        'The user does not have permission to access this resource.',
});

export function checkOrganisation(
    userOrganisationId: string,
    organisationId: string,
) {
    if (userOrganisationId !== organisationId) {
        throw new AuthorizationError(
            AuthorizationErrorMessages.USER_DOES_NOT_HAVE_PERMISSION,
        );
    }
}
