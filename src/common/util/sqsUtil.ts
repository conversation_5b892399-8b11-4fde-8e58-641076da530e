import { SQS } from '@aws-sdk/client-sqs';
import { ENV_CONFIG } from '../constant/config.constant';
import { SendMessageCommandInput } from '@aws-sdk/client-sqs/dist-types/commands/SendMessageCommand';

const sqs = new SQS({ region: ENV_CONFIG.SQS_REGION });

export class SqsUtil {
    static readonly QUEUE_NAME = {
        UPDATE_TENANCY_SETTINGS_FEE_LEDGER_CODE: `update-tenancy-settings-fee-code-queue-${ENV_CONFIG.STAGE}`,
    };

    static async sendMessage(params: SendMessageCommandInput) {
        return await sqs.sendMessage(params);
    }
}
