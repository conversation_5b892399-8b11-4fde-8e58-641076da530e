import { sign, verify } from 'jsonwebtoken';
import { UnauthorizedException } from '@nestjs/common';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

/*export function generateFeedbackToken(payload: Record<string, any>, expiresIn = '1h'): string {
  return sign(payload, JWT_SECRET, { expiresIn });
}*/

export function verifyFeedbackToken(token: string): Record<string, any> {
  try {
    return verify(token, JWT_SECRET) as Record<string, any>;
  } catch (error) {
    throw new UnauthorizedException('Token expired or invalid.');
  }
}
