import { Lamb<PERSON> } from '@aws-sdk/client-lambda';
import { ENV_CONFIG } from '../constant/config.constant';
const lambdaClient = new Lambda({ region: ENV_CONFIG.REGION });
export class LambdaUtil {
    static readonly FUNCTION_NAME = {
        DOCUSIGN_REMOVE_CONNECTION: `rentancy-core-${ENV_CONFIG.ENV}-docusign-remove-connection`,
    };

    static parsePayload(payload) {
        return payload ? Buffer.from(payload).toString('utf-8') : undefined;
    }

    static async invoke(args, options = undefined) {
        const response = await lambdaClient.invoke(args, options);
        return {
            ExecutedVersion: response.ExecutedVersion,
            LogResult: response.LogResult,
            StatusCode: response.StatusCode,
            $metadata: response.$metadata,
            Payload: LambdaUtil.parsePayload(response.Payload),
        };
    }

    static async invokeAsync(params) {
        await lambdaClient.invokeAsync(params);
    }
}
