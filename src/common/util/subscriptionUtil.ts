import { Amplify } from 'aws-amplify';
import { events } from 'aws-amplify/data';
import { ENV_CONFIG } from '../constant/config.constant';
import { Logger } from '@nestjs/common';

Amplify.configure({
    API: {
        Events: {
            endpoint: `${ENV_CONFIG.SUB_ENDPOINT}`,
            region: `${ENV_CONFIG.REGION}`,
            defaultAuthMode: 'apiKey',
            apiKey: `${ENV_CONFIG.SUB_API_KEY}`,
        },
    },
});

export class SubscriptionUtil {
    private static readonly logger = new Logger('SubscriptionUtil', {
        timestamp: true,
    });
    static readonly SUBSCRIPTION = {
        ON_INTEGRATION_STATUS_CHANGED: (organisationId: string) =>
            `onIntegrationStatusChanged/${organisationId}`,
        ON_UPDATE_BOARD: (boardId: string) => `onUpdateBoard/${boardId}`,
        ON_CHANGE_COLUMN: (columnBoardId: string) =>
            `onChangeColumn/${columnBoardId}`,
        ON_CHANGE_TASK: (taskBoardId: string) => `onChangeTask/${taskBoardId}`,
    };

    static async send(subName: string, payload: any) {
        try {
            await events.post(
                `/${ENV_CONFIG.SUB_NAMESPACE}/${subName}`,
                payload,
            );
        } catch (e) {
            this.logger.error(e.stack);
        }
    }
}
