import { Response } from 'express';
import { ResponseCode } from '../constant/responseCode';
import { ApiProperty } from '@nestjs/swagger';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

export function responseOk(res: Response, data?: any) {
    res.status(200).json(
        new BaseRes(
            ResponseCode.SUCCESS.code,
            ResponseCode.SUCCESS.message,
            data,
        ),
    );
}

export function responseError(
    status: number,
    res: Response,
    responseCode: ResponseCode,
) {
    res.status(status).json(
        new BaseRes(responseCode.code, responseCode.message),
    );
}

export class Page {
    total: number;
    data: any[];

    constructor(total: number, data: any[]) {
        this.total = total;
        this.data = data;
    }
}

export class LwRequest extends Request {
    user: User;
}

interface User {
    sub: string;
    'cognito:groups': string[];
    email_verified: boolean;
    'cognito:preferred_role': string;
    iss: string;
    'cognito:username': string;
    'custom:eligibleWorkspaces': string;
    'custom:organisationId': string;
    'cognito:roles': string[];
    aud: string;
    event_id: string;
    token_use: string;
    auth_time: number;
    exp: number;
    iat: number;
    email: string;
}

export class BaseRes<T> {
    @ApiProperty()
    code: number;
    @ApiProperty()
    message: string;

    data?: T;

    constructor(code: number, message: string, data?: T) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
}

export async function sendEmail(from, to, subject, content) {
    const SES_REGION = 'eu-west-2';
    const sesClient = new SESClient({ region: SES_REGION });
    console.log(`SES region: ${SES_REGION}, from: <EMAIL>`);
    const params = {
        Destination: {
            ToAddresses: [to],
        },
        Message: {
            Body: {
                Html: {
                    Data: content,
                },
            },
            Subject: {
                Data: subject,
            },
        },
        Source: `<EMAIL>`,
    };
    try {
        const command = new SendEmailCommand(params);
        const response = await sesClient.send(command);
        console.log('Email sent successfully:', response);
    } catch (error) {
        console.error('Error sending email:', error);
    }
}
