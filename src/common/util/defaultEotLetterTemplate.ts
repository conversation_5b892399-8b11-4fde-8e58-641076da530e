const UserType = {
    LANDLORD: "LANDLORD",
    AGENT: "AGENT",
    TENANT: "TENANT"
};

const Category = {
    EOT: "End of Tenancy"
};

export const LandlordConfirmationLetter = {
    letterName: "Landlord Confirmation Letter",
    letterSubject: "End of tenancy: $Property_Address_Line1$",
    contactType: UserType.LANDLORD,
    category: Category.EOT,
    letterContent: `<p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="390" data-relingo-block="true" data-relin-paragraph="2980">Dear <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Landlord_FName$</span>,&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="415" data-relingo-block="true" data-relin-paragraph="2981">I hope you are well.&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="438" data-relingo-block="true" data-relin-paragraph="2982">We are writing to advise that your tenant <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Primary_Tenant_First_Name$</span> occupying <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Address_Line1$</span> is expiring on <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Contract_End_Date$</span>.&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="581" data-relingo-block="true" data-relin-paragraph="2983">To confirm how you’d like to proceed, please confirm next steps, and we will contact your tenant(s) accordingly.&nbsp;&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="697" data-relingo-block="true" data-relin-paragraph="2984"><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$ConfirmNextStepsURL$</span></p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="719" data-relingo-block="true">&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="722" data-relingo-block="true" data-relin-paragraph="2985">Very best,&nbsp;&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="736" data-relingo-block="true" data-relin-paragraph="2986"><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_First_Name$ $Property_Manager_Last_Name$&nbsp;</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_Email$&nbsp;&nbsp;</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_Phone_Number$&nbsp;&nbsp;</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Workspace_Name$&nbsp;&nbsp;</span></p>`
};

export const TenantConfirmationLetter = {
    letterName: "Tenant Confirmation Letter",
    letterSubject: "Renewal update for $Property_Address_Line1$",
    contactType: UserType.TENANT,
    category: Category.EOT,
    letterContent: `<p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="1133" data-relingo-block="true">Dear <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Primary_Tenant_FName$</span>,</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="1163" data-relingo-block="true">I hope this email finds you well.</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="1198" data-relingo-block="true">I am writing to inform you that your current tenancy is scheduled to end on $Contract_End_Date$. To confirm how you’d like to proceed, please confirm next steps below, and we will contact your landlord accordingly.&nbsp;&nbsp;</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="1416" data-relingo-block="true"><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$ConfirmNextStepsURL$</span></p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="1438" data-relingo-block="true">Should you have any questions or require any further information please do not hesitate to contact us.<code class="code cc-1tbex3z" style="--ds--code--bg-color:var(--ds-background-neutral, #091E420F);-webkit-box-decoration-break:clone;background-color:var(--ds--code--bg-color,var(--ds-background-neutral, #F4F5F7));border-radius:var(--ds-border-radius, 3px);border-style:none;box-decoration-break:clone;color:var(--ds-text, #172B4D);display:inline;font-family:var(--ds-font-family-code, ui-monospace, Menlo, &quot;Segoe UI Mono&quot;, &quot;Ubuntu Mono&quot;, monospace);font-feature-settings:;font-kerning:;font-optical-sizing:;font-size-adjust:;font-size:0.875em;font-stretch:;font-style:;font-variant-alternates:;font-variant-caps:;font-variant-east-asian:;font-variant-emoji:;font-variant-ligatures:;font-variant-numeric:;font-variant-position:;font-variation-settings:;font-weight:var(--ds-font-weight-regular, 400);line-height:inherit;overflow-wrap:break-word;overflow:auto;padding:2px 0.5ch;white-space:pre-wrap;" data-renderer-mark="true">&nbsp;</code></p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="1543" data-relingo-block="true">Thank you for your attention to this matter, and we look forward to hearing from you soon. <code class="code cc-1tbex3z" style="--ds--code--bg-color:var(--ds-background-neutral, #091E420F);-webkit-box-decoration-break:clone;background-color:var(--ds--code--bg-color,var(--ds-background-neutral, #F4F5F7));border-radius:var(--ds-border-radius, 3px);border-style:none;box-decoration-break:clone;color:var(--ds-text, #172B4D);display:inline;font-family:var(--ds-font-family-code, ui-monospace, Menlo, &quot;Segoe UI Mono&quot;, &quot;Ubuntu Mono&quot;, monospace);font-feature-settings:;font-kerning:;font-optical-sizing:;font-size-adjust:;font-size:0.875em;font-stretch:;font-style:;font-variant-alternates:;font-variant-caps:;font-variant-east-asian:;font-variant-emoji:;font-variant-ligatures:;font-variant-numeric:;font-variant-position:;font-variation-settings:;font-weight:var(--ds-font-weight-regular, 400);line-height:inherit;overflow-wrap:break-word;overflow:auto;padding:2px 0.5ch;white-space:pre-wrap;" data-renderer-mark="true">&nbsp;</code><br>&nbsp;<br>Very best,&nbsp; <br><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_First_Name$ $Property_Manager_Last_Name$&nbsp;  </span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_Email$&nbsp;&nbsp;  &nbsp;</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_Phone_Number$</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Workspace_Name$&nbsp;&nbsp;&nbsp;</span></p>`
};

export const TerminationLetterforTenant = {
    letterName: "Termination Letter for Tenant",
    letterSubject: "Renewal update for $Property_Address_Line1$",
    contactType: UserType.TENANT,
    category: Category.EOT,  
    letterContent: `Dear $Primary_Tenant_FName$,

Your tenancy for $Property_Address_Line1$ will end on $Contract_End_Date$.  In line with your tenancy agreement, you are liable to pay rent until $Contract_End_Date$.  

Please ensure that you remove all personal items, furniture, appliances etc from the property, including the garden. Any items remaining at the end of the tenancy, not belonging to the landlord, will be removed, and disposed of and the costs will be deducted from your Deposit.  

If the property benefited from a professional clean at the start of the tenancy, please ensure it is left in the same condition. If you would like a recommendation of a cleaner, please do feel free to ask.  

 If you have any questions, please feel free to contact us.


Very best, 


$Property_Manager_First_Name$ $Property_Manager_Last_Name$
$Property_Manager_Email$    
$Property_Manager_Phone_Number$    
$Workspace_Name$
`
};

export const TerminationLetterforLandlord = {
    letterName: "Termination Letter for Landlord",
    letterSubject: "End of tenancy for $Property_Address_Line1$",
    contactType: UserType.LANDLORD,
    category: Category.EOT,
    letterContent: `<p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3172" data-relingo-block="true">Dear <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Landlord_FName$</span>,</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3196" data-relingo-block="true">I hope this email finds you well.</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3231" data-relingo-block="true">We are writing to notify you that the tenancy for <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Address_Line1$</span>, currently occupied by <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Primary_Tenant_First_Name$</span>, is scheduled to end on <span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Contract_End_Date$</span>.</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3403" data-relingo-block="true">Please note that we will ensure the tenant fulfills all obligations under the tenancy agreement, including vacating the property and leaving it in the required condition.</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3575" data-relingo-block="true">If you have any specific instructions or require further assistance regarding the end-of-tenancy process, please feel free to contact us.</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3714" data-relingo-block="true">Very best regards,</p><p style="-webkit-text-stroke-width:0px;background-color:rgb(255, 255, 255);color:rgb(41, 42, 46);font:400 16px / 1.714 ui-sans-serif, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Ubuntu, system-ui, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing:normal;margin:0.75rem 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:pre-wrap;widows:2;word-spacing:0px;" data-renderer-start-pos="3734" data-relingo-block="true"><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_First_Name$ $Property_Manager_Last_Name$</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_Email$</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Property_Manager_Phone_Number$</span><br><span class="fabric-text-color-mark" style="--custom-palette-color:var(--ds-text-accent-purple, #403294);color:var(--custom-palette-color, inherit);" data-renderer-mark="true" data-text-custom-color="#403294">$Workspace_Name$</span></p>`
};

export const ExtensionLetterforTenant = {
    letterName: "Extension Letter for Tenant",
    letterSubject: "Tenancy Extension Confirmation for $Property_Address_Line1$",
    contactType: UserType.TENANT,
    category: Category.EOT,
    letterContent: `Dear $Primary_Tenant_FName$,

We are pleased to inform you that your tenancy for $Property_Address_Line1$ has been successfully extended.

New Details of Your Tenancy:

Start Date: $New_Start_Date$

End Date: $New_End_Date$

Rent Amount: $New_Rent_Amount$

If you have any questions or need further clarification regarding your tenancy, please don’t hesitate to reach out to us.

Thank you for continuing to choose $Workspace_Name$.

Very best regards,
$Property_Manager_First_Name$ $Property_Manager_Last_Name$
$Property_Manager_Email$
$Property_Manager_Phone_Number$
$Workspace_Name$
`
};

export const ExtensionLetterforLandlord = {
    letterName: "Extension Letter for Landlord",
    letterSubject: "Tenancy Extension Confirmed for $Property_Address_Line1$",
    contactType: UserType.LANDLORD,
    category: Category.EOT,
    letterContent: `Dear $Landlord_FName$,


We are writing to inform you that the tenancy for your property at $Property_Address_Line1$ has been successfully extended with the current tenant, $Primary_Tenant_First_Name$.

New Tenancy Details:

Start Date: $New_Start_Date$

End Date: $New_End_Date$

Rent Amount: $New_Rent_Amount$

Should you have any questions or need further assistance, please don’t hesitate to contact us.

Thank you for entrusting us with the management of your property.

Very best regards,
$Property_Manager_First_Name$ $Property_Manager_Last_Name$
$Property_Manager_Email$
$Property_Manager_Phone_Number$
$Workspace_Name$

`
};

// module.exports = {
//     LandlordConfirmationLetter,
//     TenantConfirmationLetter,
//     TerminationLetterforTenant,
//     TerminationLetterforLandlord,
//     ExtensionLetterforTenant,
//     ExtensionLetterforLandlord
// };