export class InvitationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'InvitationError';
    }
}

export const InvitationErrorMessages = Object.freeze({
    E0004U: 'Sorry, we are unable to invite this user into this organisation(E0004U). Please contact support quoting the email address of the invitee.',
    INVITATION_ALREADY_VERIFIED: (email: string) =>
        `Invitation is already verified for ${email}`,
    INVITATION_ALREADY_EXISTS: (email: string) =>
        `Invitation is already existed for ${email}, you can resent the invitation by clicking resent button.`,
});
