import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { TenancySettings } from '../model/tenancySettings';

@Injectable()
export class TenancySettingRepository extends BaseRepository<TenancySettings> {
    constructor() {
        super(TABLE.TENANCY_SETTINGS_TABLE);
    }

    async findByOrganisationId(
        organisationId: string,
    ): Promise<TenancySettings> {
        const items = await this.getItemsByOrganisationId(
            INDEX.TENANCY_SETTINGS_ORGANISATION_INDEX,
            'organisationId',
            organisationId,
        );
        return items[0];
    }
}
