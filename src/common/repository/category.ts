import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { Category } from '../model/category';

@Injectable()
export class CateRepository extends BaseRepository<Category> {
    constructor() {
        super(TABLE.CATEGORY_TABLE);
    }

    async list(): Promise<Category[]> {
        return await this.scan(this.tableName);
    }

    async listByRoomId(roomId: string): Promise<Category[]> {
        return await this.scan(
            this.tableName,
            'contains(#rooms, :roomId)',
            {
                ':roomId': roomId,
            },
            null,
            {
                '#rooms': 'rooms',
            },
        );
    }
}
