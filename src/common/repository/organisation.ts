import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { Organisation } from '../model/organisation';

@Injectable()
export class OrganisationRepository extends BaseRepository<Organisation> {
    constructor() {
        super(TABLE.ORGANISATION_TABLE);
    }

    async findAll(): Promise<Organisation[]> {
        return await this.scan(this.tableName);
    }

    async findByIds(ids: string[]): Promise<Organisation[]> {
        return await this.batchGetItems(this.tableName, ids);
    }

    async updateOrganisationMailStatus(id, customMail, emailStatus) {
        await this.updateItem(id, { customMail, emailStatus });
    }
}
