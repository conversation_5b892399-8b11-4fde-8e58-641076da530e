import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class InvoiceTenancyRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.INVOICE_TENANCY_TABLE);
    }

    async findByTenancyId(tenancyId: string): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TENANCY_INVOICE_INDEX,
            KeyConditionExpression:
                'invoiceTenancyTenancyId = :invoiceTenancyTenancyId',
            ExpressionAttributeValues: {
                ':invoiceTenancyTenancyId': tenancyId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
