import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { INDEX } from '../../common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class IntegrationRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.INTEGRATION_TABLE);
    }

    async findIntegrationByOrganisationId(
        organisationId: string,
    ): Promise<any[]> {
        const params = {
            ExpressionAttributeValues: {
                ':organisationId': organisationId,
            },
            IndexName: INDEX.INTEGRATION_ORGANISATION_INDEX,
            KeyConditionExpression: 'organisationId = :organisationId',
            TableName: this.tableName,
        };
        return await this.query(params);
    }

    async findXeroIntegration(organisationId: string) {
        const params = {
            ExpressionAttributeValues: {
                ':organisationId': organisationId,
                ':type': 'XERO',
            },
            ExpressionAttributeNames: {
                '#type': 'type',
            },
            IndexName: INDEX.INTEGRATION_ORGANISATION_INDEX,
            KeyConditionExpression: 'organisationId = :organisationId',
            FilterExpression: '#type = :type',
            TableName: this.tableName,
        };

        const items = await this.query(params);

        return items ? items[0] : null;
    }
}
