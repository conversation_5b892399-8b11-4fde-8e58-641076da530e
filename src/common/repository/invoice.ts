import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class InvoiceRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.INVOICE_TABLE);
    }

    async findByIds(ids: string[]): Promise<any[]> {
        return await this.batchGetItems(this.tableName, ids);
    }
}
