import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { TaskStatus } from 'src/common/enum/task';
import { PageTaskDto, Task } from 'src/common/model/task';
import { BaseRepository } from './base.repository';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';

@Injectable()
export class TaskRepository extends BaseRepository<Task> {
    constructor() {
        super(TABLE.TASK_TABLE);
    }

    async findByBoardId(
        boardId: string,
        status?: TaskStatus,
        assignee?: string,
    ): Promise<Task[]> {
        const params: any = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_BOARD_INDEX,
            KeyConditionExpression: 'taskBoardId = :taskBoardId',
            ExpressionAttributeNames: {
                '#status': 'status',
            },
            ExpressionAttributeValues: {
                ':taskBoardId': boardId,
            },
            ScanIndexForward: false,
        };

        if (status) {
            params.FilterExpression = '#status = :status';
            params.ExpressionAttributeValues[':status'] = status;
        } else {
            params.FilterExpression =
                '#status <> :status1 AND #status <> :status2 AND #status <> :status3';
            params.ExpressionAttributeValues[':status1'] = TaskStatus.ARCHIVED;
            params.ExpressionAttributeValues[':status2'] = TaskStatus.DELETED;
            params.ExpressionAttributeValues[':status3'] = TaskStatus.DRAFT;
        }

        if (assignee) {
            params.FilterExpression = `${params.FilterExpression} AND taskUserId = :assignee`;
            params.ExpressionAttributeValues[':assignee'] = assignee;
        }

        const result = await this.query(params);
        return result;
    }

    async findByColumnId(columnId: string): Promise<Task[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_COLUMN_INDEX,
            KeyConditionExpression: 'taskColumnId = :taskColumnId',
            FilterExpression:
                '#status <> :status1 AND #status <> :status2 AND #status <> :status3',
            ExpressionAttributeNames: {
                '#status': 'status',
            },
            ExpressionAttributeValues: {
                ':taskColumnId': columnId,
                ':status1': TaskStatus.ARCHIVED,
                ':status2': TaskStatus.DELETED,
                ':status3': TaskStatus.DRAFT,
            },
            ScanIndexForward: false,
        };

        const result = await this.query(params);
        return result;
    }

    async findByOrganisationId(organisationId: string): Promise<Task[]> {
        const params: any = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_ORGANISATION_INDEX,
            KeyConditionExpression: 'taskOrganisationId = :taskOrganisationId',

            ExpressionAttributeValues: {
                ':taskOrganisationId': organisationId,
            },
        };

        return await this.query(params);
    }

    async findByOrganisationIdAndStatus(
        organisationId: string,
        status: TaskStatus,
    ): Promise<Task[]> {
        const params: any = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_ORGANISATION_INDEX,
            KeyConditionExpression: 'taskOrganisationId = :taskOrganisationId',
            FilterExpression: '#status = :status',
            ExpressionAttributeNames: {
                '#status': 'status',
            },
            ExpressionAttributeValues: {
                ':taskOrganisationId': organisationId,
                ':status': status,
            },
        };

        return await this.query(params);
    }

    async findByOrganisationIdWithDeadline(
        organisationId: string,
        abandoned?: boolean,
    ): Promise<Task[]> {
        const params: any = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_ORGANISATION_INDEX,
            KeyConditionExpression: 'taskOrganisationId = :taskOrganisationId',
            ExpressionAttributeNames: {
                '#deadline': 'deadline',
            },
            ExpressionAttributeValues: {
                ':taskOrganisationId': organisationId,
            },
        };

        if (abandoned) {
            params.FilterExpression = 'attribute_not_exists(#deadline)';
        } else {
            params.FilterExpression = 'attribute_exists(#deadline)';
        }

        const result = await this.query(params);
        return result;
    }

    async findByOrganisationIdWithStatus(
        organisationId: string,
        abandoned?: boolean,
    ): Promise<Task[]> {
        const params: any = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_ORGANISATION_INDEX,
            KeyConditionExpression: 'taskOrganisationId = :taskOrganisationId',
            ExpressionAttributeNames: {
                '#status': 'status',
            },
            ExpressionAttributeValues: {
                ':taskOrganisationId': organisationId,
            },
        };

        if (abandoned) {
            params.FilterExpression = 'attribute_not_exists(status)';
        } else {
            params.FilterExpression = 'attribute_exists(status)';
        }

        return await this.query(params);
    }

    async pageTaskByOrganisationId(
        organisationId: string,
        nextToken: string,
        limit: number,
    ): Promise<PageTaskDto> {
        try {
            limit = Math.min(limit, 100);
            const params = {
                TableName: this.tableName,
                IndexName: INDEX.TASK_ORGANISATION_INDEX,
                KeyConditionExpression: '#taskOrganisationId = :orgId',
                ExpressionAttributeNames: {
                    '#taskOrganisationId': 'taskOrganisationId',
                },
                ExpressionAttributeValues: {
                    ':orgId': organisationId,
                },
                Limit: limit,
                ExclusiveStartKey: nextToken
                    ? JSON.parse(Buffer.from(nextToken, 'base64').toString())
                    : undefined,
                ScanIndexForward: false,
            };
            const result = await this.ddb.send(new QueryCommand(params));
            const newNextToken = result.LastEvaluatedKey
                ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString(
                      'base64',
                  )
                : undefined;
            return {
                items: (result.Items || []) as Task[],
                nextToken: newNextToken,
            };
        } catch (error) {
            console.error('DynamoDB query error:', error.stack);
            throw new Error('Failed to list tasks');
        }
    }
}
