import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { SubCategory } from '../model/subCategory';

@Injectable()
export class SubCategoryRepository extends BaseRepository<SubCategory> {
    constructor() {
        super(TABLE.SUB_CATEGORY_TABLE);
    }

    async listByRoomIdAndCategoryId(
        roomId: string,
        categoryId: string,
    ): Promise<SubCategory[]> {
        return await this.scan(
            this.tableName,
            '#roomId = :roomId AND #categoryId = :categoryId',
            {
                ':roomId': roomId,
                ':categoryId': categoryId,
            },
            null,
            {
                '#roomId': 'roomId',
                '#categoryId': 'categoryId',
            },
        );
    }
}
