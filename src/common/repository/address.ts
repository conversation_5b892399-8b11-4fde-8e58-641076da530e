import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class AddressRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.ADDRESS_TABLE);
    }

    async findUserAddresses(userId: string) {
      const params = {
        ExpressionAttributeValues: {
          ':parentId': userId,
          ':parentType': 'USER',
        },
        IndexName: INDEX.ADDRESS_INDEX,
        KeyConditionExpression: 'parentId = :parentId AND parentType = :parentType',
        TableName: TABLE.ADDRESS_TABLE,
      };

      return (await super.query(params));
    }
}
