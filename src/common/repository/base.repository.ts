import { DynamodbService } from '../service/dynamodb.service';

export abstract class BaseRepository<T> extends DynamodbService {
    protected constructor(protected readonly tableName: string) {
        super();
    }

    async findById(
        id: string,
        projectionExpression?: string,
        expressionAttributeNames?: Record<string, string>,
    ): Promise<any> {
        return await this.getItem(
            this.tableName,
            id,
            projectionExpression,
            expressionAttributeNames,
        );
    }

    async createItem(item: T): Promise<T> {
        return await this.create(this.tableName, item);
    }

    async updateItem(id: string, item: Partial<T>): Promise<T> {
        return await this.update(this.tableName, id, item);
    }

    async deleteById(id: string): Promise<void> {
        await this.delete(this.tableName, id);
    }

    async removeFieldsById(id: string, fields: string[]) {
        return await this.removeFields(this.tableName, id, fields);
    }

    async getItemsByOrganisationId(
        index: string,
        fieldName: string,
        organisationId: string,
    ): Promise<T[]> {
        return await this.getOrganisationItems(
            this.tableName,
            index,
            fieldName,
            organisationId,
        );
    }

    async scanAllTable(): Promise<T[]> {
        return await this.scan(this.tableName);
    }
}
