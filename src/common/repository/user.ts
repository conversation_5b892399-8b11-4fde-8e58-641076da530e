import { Injectable } from '@nestjs/common';
import {
    AdminAddUserToGroupCommand,
    AdminDeleteUserAttributesCommandInput,
    AdminRemoveUserFromGroupCommand,
    AdminUpdateUserAttributesCommandInput,
    AdminUserGlobalSignOutCommand,
    CognitoIdentityProvider,
    CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';
import { ENV_CONFIG } from 'src/common/constant/config.constant';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { User } from 'src/common/model/user';
import { BaseRepository } from './base.repository';
import { FileRepository } from './file';

@Injectable()
export class UserRepository extends BaseRepository<User> {
    private readonly idp: CognitoIdentityProvider;
    private readonly USER_POOL: string;
    private readonly cognitoClient: CognitoIdentityProviderClient;

    constructor(private readonly fileRepository: FileRepository) {
        super(TABLE.USER_TABLE);
        this.idp = new CognitoIdentityProvider({
            region: ENV_CONFIG.REGION,
        } as any);
        this.USER_POOL = ENV_CONFIG.COGNITO_USERPOOL_ID;
        this.cognitoClient = new CognitoIdentityProviderClient({
            region: ENV_CONFIG.REGION,
        } as any);
    }

    async findByIds(userIds: string[]): Promise<User[]> {
        return await this.batchGetItems(this.tableName, userIds);
    }

    async findUserWithImage(userId: string): Promise<User> {
        const user = await this.findById(userId);
        if (user?.userImageId) {
            user.image = await this.fileRepository.findById(user.userImageId);
        }
        return user;
    }

    async findByCognitoId(cognitoId: string): Promise<User> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.USER_COGNITO_ID_INDEX,
            KeyConditionExpression: 'cognitoId = :cognitoId',
            ExpressionAttributeValues: {
                ':cognitoId': cognitoId,
            },
        };

        const result = await this.query(params);
        return result[0];
    }

    async getCognitoUser(cognitoId: string) {
        const params = {
            Username: cognitoId,
            UserPoolId: this.USER_POOL,
        };
        return await this.idp.adminGetUser(params);
    }

    async changeCognitoOrganisation(
        cognitoId: string,
        newOrganisationId: string,
    ) {
        const user = await this.getCognitoUser(cognitoId);
        const attribute = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:eligibleWorkspaces',
        );
        const eligibleOrganisations = Object.keys(JSON.parse(attribute.Value));
        if (!attribute || !eligibleOrganisations.includes(newOrganisationId)) {
            throw new Error('No organisation attribute');
        }
        const updateWorkspaceParams = {
            UserAttributes: [
                {
                    Name: 'custom:organisationId',
                    Value: newOrganisationId,
                },
            ],
            UserPoolId: this.USER_POOL,
            Username: user.Username,
        };
        await this.idp.adminUpdateUserAttributes(updateWorkspaceParams);
    }

    async updateUserOrganisation(userId: string, newOrganisationId: string) {
        const updateFields = { currentOrganisation: newOrganisationId };
        await this.updateItem(userId, updateFields);
    }

    async updateCognitoUserAttributes(
        updateWorkspaceParams: AdminUpdateUserAttributesCommandInput,
    ) {
        updateWorkspaceParams.UserPoolId = this.USER_POOL;
        await this.idp.adminUpdateUserAttributes(updateWorkspaceParams);
    }

    async deleteCognitoUserAttributes(
        deleteWorkspaceParams: AdminDeleteUserAttributesCommandInput,
    ) {
        deleteWorkspaceParams.UserPoolId = this.USER_POOL;
        await this.idp.adminDeleteUserAttributes(deleteWorkspaceParams);
    }

    async removeCurrentOrganisationFromUser(id) {
        await this.removeFieldsById(id, ['currentOrganisation']);
    }

    async updateCognitoUserOrganisations(
        newCurrentOrganisation,
        cognitoId,
        organisationObject,
    ) {
        const params = {
            UserAttributes: [
                {
                    Name: 'custom:organisationId',
                    Value: newCurrentOrganisation,
                },
                {
                    Name: 'custom:eligibleWorkspaces',
                    Value: JSON.stringify(organisationObject),
                },
            ],
            UserPoolId: '',
            Username: cognitoId,
        };
        await this.updateCognitoUserAttributes(params);
    }

    async findByCognitoEmail(email: string) {
        const params = {
            ExpressionAttributeValues: {
                ':cognitoEmail': email,
            },
            IndexName: INDEX.USER_COGNITO_EMAIL_INDEX,
            KeyConditionExpression: 'cognitoEmail = :cognitoEmail',
            TableName: this.tableName,
        };

        const items = await this.query(params);
        return items ? items[0] : null;
    }

    async updateUserInvitationId(id: string, invitationId: string) {
        await this.updateItem(id, { userInvitationId: invitationId });
    }

    async revokeToken(userName) {
        const params = {
            UserPoolId: this.USER_POOL,
            Username: userName,
        };

        const command = new AdminUserGlobalSignOutCommand(params);
        await this.cognitoClient.send(command as any);
    }

    async removeCognitoRole(userName: string, role: string) {
        const params = {
            UserPoolId: this.USER_POOL,
            Username: userName,
            GroupName: role,
        };

        const adminRemoveUserFromGroupCommand =
            new AdminRemoveUserFromGroupCommand(params);
        await this.cognitoClient.send(adminRemoveUserFromGroupCommand as any);
    }

    async addCognitoRole(userName: string, role: string) {
        const params = {
            UserPoolId: this.USER_POOL,
            Username: userName,
            GroupName: role,
        };

        const adminAddUserToGroupCommand = new AdminAddUserToGroupCommand(
            params,
        );
        await this.cognitoClient.send(adminAddUserToGroupCommand as any);
    }

    async createCognitoUser(organisationId, email) {
        const params = {
            UserPoolId: this.USER_POOL,
            Username: email,
            ForceAliasCreation: false,
            TemporaryPassword: '1234567890',
            UserAttributes: [
                {
                    Name: 'email',
                    Value: email,
                },
                {
                    Name: 'custom:organisationId',
                    Value: organisationId,
                },
                {
                    Name: 'custom:eligibleWorkspaces',
                    Value: JSON.stringify({ [organisationId]: true }),
                },
            ],
        };

        const result = await this.idp.adminCreateUser(params);
        this.logger.log(`User created:${JSON.stringify(result)}`);

        const updateParams = {
            UserAttributes: [
                {
                    Name: 'email_verified',
                    Value: 'true',
                },
            ],
            UserPoolId: this.USER_POOL,
            Username: result.User.Username,
        };
        await this.idp.adminUpdateUserAttributes(updateParams);

        return result.User.Username;
    }

    async findUserWorkspaces(cognitoId) {
        const user = await this.getCognitoUser(cognitoId);
        const attribute = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:eligibleWorkspaces',
        );
        return attribute ? JSON.parse(attribute.Value) : {};
    }

    async *findAllCognitoUsersWithWorkspace(workspaceId) {
        let paginationToken = null;

        try {
            do {
                const params = {
                    UserPoolId: this.USER_POOL,
                    PaginationToken: paginationToken,
                    Limit: 60, // Maximum number of users per request
                };

                const response = await this.idp.listUsers(params);
                for (const user of response.Users.filter((user) =>
                    user.Attributes.some(
                        (attr) =>
                            attr.Name === 'custom:eligibleWorkspaces' &&
                            Object.hasOwn(JSON.parse(attr.Value), workspaceId),
                    ),
                )) {
                    yield user;
                }

                paginationToken = response.PaginationToken;
            } while (paginationToken);
        } catch (error) {
            this.logger.error('Failed to retrieve users', error);
            throw error;
        }
    }

    async adminDeleteUser(cognitoId: string) {
        return this.idp.adminDeleteUser({
            UserPoolId: this.USER_POOL,
            Username: cognitoId,
        });
    }

    async findApplicantsByOrganisation(currentOrganisation): Promise<User[]> {
        const params = {
            ExpressionAttributeValues: {
                ':currentOrganisation': currentOrganisation,
                ':type': 'APPLICANT',
            },
            IndexName: INDEX.USER_ORGANISATION_INDEX,
            KeyConditionExpression:
                'currentOrganisation = :currentOrganisation',
            FilterExpression: '#dynobase_type = :type',
            ExpressionAttributeNames: { '#dynobase_type': 'type' },
            TableName: this.tableName,
        };
        return await this.query(params);
    }
}
