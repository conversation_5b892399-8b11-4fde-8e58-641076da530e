import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { User } from 'src/common/model/user';
import { BaseRepository } from './base.repository';
import { FileRepository } from './file';

@Injectable()
export class UserRepository extends BaseRepository<User> {
    constructor(private readonly fileRepository: FileRepository) {
        super(TABLE.USER_TABLE);
    }

    async findByIds(userIds: string[]): Promise<User[]> {
        return await this.batchGetItems(this.tableName, userIds);
    }

    async findUserWithImage(userId: string): Promise<User> {
        const user = await this.findById(userId);
        if (user?.userImageId) {
            user.image = await this.fileRepository.findById(user.userImageId);
        }
        return user;
    }

    async findByCognitoId(cognitoId: string): Promise<User> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.USER_COGNITO_ID_INDEX,
            KeyConditionExpression: 'cognitoId = :cognitoId',
            ExpressionAttributeValues: {
                ':cognitoId': cognitoId,
            },
        };

        const result = await this.query(params);
        return result[0];
    }

    async updateUserOrganisation(userId: string, newOrganisationId: string) {
        const updateFields = { currentOrganisation: newOrganisationId };
        await this.updateItem(userId, updateFields);
    }

    async removeCurrentOrganisationFromUser(id) {
        await this.removeFieldsById(id, ['currentOrganisation']);
    }

    async findByCognitoEmail(email: string) {
        const params = {
            ExpressionAttributeValues: {
                ':cognitoEmail': email,
            },
            IndexName: INDEX.USER_COGNITO_EMAIL_INDEX,
            KeyConditionExpression: 'cognitoEmail = :cognitoEmail',
            TableName: this.tableName,
        };

        const items = await this.query(params);
        return items ? items[0] : null;
    }

    async updateUserInvitationId(id: string, invitationId: string) {
        await this.updateItem(id, { userInvitationId: invitationId });
    }

    async findApplicantsByOrganisation(currentOrganisation): Promise<User[]> {
        const params = {
            ExpressionAttributeValues: {
                ':currentOrganisation': currentOrganisation,
                ':type': 'APPLICANT',
            },
            IndexName: INDEX.USER_ORGANISATION_INDEX,
            KeyConditionExpression:
                'currentOrganisation = :currentOrganisation',
            FilterExpression: '#dynobase_type = :type',
            ExpressionAttributeNames: { '#dynobase_type': 'type' },
            TableName: this.tableName,
        };
        return await this.query(params);
    }
}
