import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { UserPreference } from '../model/userPreference';

@Injectable()
export class UserPreferenceRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.USER_PREFERENCE_TABLE);
    }

    async findByUserId(userId: string): Promise<any> {
        return await this.findById(userId);
    }

    async updatePreference(
        userId: string,
        preference: Partial<any>,
    ): Promise<any> {
        return await this.updateItem(userId, preference);
    }

    async listByUserId(userId: string): Promise<UserPreference[]> {
        const params = {
            ExpressionAttributeValues: {
                ':userPreferencesUserId': userId,
            },
            IndexName: INDEX.USER_PREFERENCES_USER_INDEX,
            KeyConditionExpression:
                'userPreferencesUserId = :userPreferencesUserId',
            TableName: this.tableName,
        };
        return await this.query(params);
    }

    async updateAllowedNotifications(
        id: string,
        notifications: string[],
    ): Promise<UserPreference> {
        await this.updateItem(id, {
            allowedNotifications: notifications,
        });
        return this.findById(id);
    }
}
