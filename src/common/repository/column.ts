import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { Column } from 'src/common/model/column';
import { BaseRepository } from './base.repository';

@Injectable()
export class ColumnRepository extends BaseRepository<Column> {
    constructor() {
        super(TABLE.COLUMN_TABLE);
    }

    async findByBoardId(boardId: string): Promise<Column[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.COLUMN_BOARD_INDEX,
            KeyConditionExpression: 'columnBoardId = :columnBoardId',
            ExpressionAttributeValues: {
                ':columnBoardId': boardId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
