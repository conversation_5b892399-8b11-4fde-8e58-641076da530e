import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { INDEX, TABLE } from '../constant/dynamodb.constants';
import { TaskReminder } from '../model/taskReminder';

@Injectable()
export class TaskReminderRepository extends BaseRepository<TaskReminder> {
    constructor() {
        super(TABLE.TASK_REMINDER_TABLE);
    }

    async findByOrganisationId(
        organisationId: string,
    ): Promise<TaskReminder[]> {
        return await this.getOrganisationItems(this.tableName, INDEX.TASK_REMINDER_ORGANISATION_INDEX, 'taskReminderOrganisationId', organisationId);
    }
}