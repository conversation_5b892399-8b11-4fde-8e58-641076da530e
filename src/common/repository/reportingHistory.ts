import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class ReportingHistoryRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.REPORTING_HISTORY_TABLE);
    }

    async getSortedOrganisationReportHistories(
        organisationId: string,
        pastLabelDateQuery: string,
        futureLabelDateQuery: string,
    ) {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.REPORTING_HISTORY_ORGANISATION_INDEX,
            ScanIndexForward: true,
            KeyConditionExpression:
                'reportingHistoryOrganisationId = :reportingHistoryOrganisationId AND labelDateQuery BETWEEN :pastLabelDateQuery AND :futureLabelDateQuery',
            ExpressionAttributeValues: {
                ':reportingHistoryOrganisationId': organisationId,
                ':pastLabelDateQuery': pastLabelDateQuery,
                ':futureLabelDateQuery': futureLabelDateQuery,
            },
        };

        return this.query(params);
    }
}
