import { BaseRepository } from './base.repository';
import { DocusignTemplate } from '../model/docusignTemplates';
import { INDEX, TABLE } from '../constant/dynamodb.constants';

export class DocusignTemplateRepository extends BaseRepository<DocusignTemplate> {
    constructor() {
        super(TABLE.DOCUSIGN_TEMPLATES_TABLE);
    }

    async getDocusignTemplatesByOrgId(
        organisationId: string,
    ): Promise<DocusignTemplate[]> {
        return this.getItemsByOrganisationId(
            INDEX.DOCUSIGN_TEMPLATES_ORGANISATION_INDEX,
            'docusignTemplatesOrganisationId',
            organisationId,
        );
    }
}
