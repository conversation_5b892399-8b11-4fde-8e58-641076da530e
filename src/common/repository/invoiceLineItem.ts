import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class InvoiceLineItemRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.INVOICE_LINE_ITEM_TABLE);
    }

    async findByOrganisationId(organisationId: string): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.INVOICE_LINE_ITEM_ORGANISATION_INDEX,
            KeyConditionExpression:
                'invoiceLineItemOrganisationId = :invoiceLineItemOrganisationId',
            ExpressionAttributeValues: {
                ':invoiceLineItemOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
