import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { TemplateLetter } from '../model/templateLetter';
import { UserType } from '../model/user';
import { QueryCommandInput } from '@aws-sdk/lib-dynamodb/dist-types/commands/QueryCommand';

@Injectable()
export class TemplateLetterRepository extends BaseRepository<TemplateLetter> {
    constructor() {
        super(TABLE.TEMPLATE_LETTER_TABLE);
    }
    async findOrganisationTemplateLetters(
        organisationId: string,
        contactType: UserType,
        category: string,
    ): Promise<TemplateLetter[]> {
        const params = {
            ExpressionAttributeValues: {
                ':organisationId': organisationId,
            },
            IndexName: INDEX.TEMPLATE_ORGANISATION_INDEX,
            KeyConditionExpression:
                'templateLetterOrganisationId = :organisationId',
            TableName: TABLE.TEMPLATE_LETTER_TABLE,
        } as QueryCommandInput;

        if (contactType) {
            params.ExpressionAttributeValues[':contactType'] = contactType;
            params.FilterExpression = 'contactType = :contactType';
        }

        if (category) {
            params.ExpressionAttributeValues[':category'] = category;
            params.FilterExpression = `${params.FilterExpression ? `${params.FilterExpression} AND ` : ''}category = :category`;
        }
        return await this.query(params);
    }
}
