import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class TaskCommentRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.TASK_COMMENT_TABLE);
    }

    async findByTaskId(taskId: string) {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.COMMENT_TASK_INDEX,
            KeyConditionExpression: 'taskCommentTaskId = :taskCommentTaskId',
            ExpressionAttributeValues: {
                ':taskCommentTaskId': taskId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
