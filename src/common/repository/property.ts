import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { Property } from 'src/common/model/property';
import { BaseRepository } from './base.repository';

@Injectable()
export class PropertyRepository extends BaseRepository<Property> {
    constructor() {
        super(TABLE.PROPERTY_TABLE);
    }

    async findByIds(ids: string[]): Promise<Property[]> {
        return await this.batchGetItems(this.tableName, ids);
    }

    async findByOrganisationId(organisationId: string): Promise<Property[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.PROPERTY_ORGANISATION_INDEX,
            KeyConditionExpression:
                'propertyOrganisationId = :propertyOrganisationId',
            ExpressionAttributeValues: {
                ':propertyOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
