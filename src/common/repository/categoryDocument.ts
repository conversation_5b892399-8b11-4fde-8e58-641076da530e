import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Column } from '../model/column';
import { TABLE } from '../constant/dynamodb.constants';

@Injectable()
export class CategoryDocumentRepository extends BaseRepository<Column> {
    constructor() {
        super(TABLE.CATEGORY_DOCUMENT_TABLE);
    }

    async createCategoryDocuments(organisationId) {
        const items = [];
        const params = {
            TableName: TABLE.CATEGORY_TABLE,
        };
        const categories = await this.ddb.scan(params);

        for (const category of categories.Items) {
            const id = category.id;
            const putItemParams = {
                TableName: this.tableName,
                Item: {
                    categoryId: id,
                    orgnisationId: organisationId,
                    key: 'conversations/tenancy_channels/property-guide.pdf',
                    name: 'Property Guide',
                    mimeType: 'application/pdf',
                    note: 'Property Guide',
                },
            };

            items.push(this.ddb.put(putItemParams));
        }

        await Promise.all(items);
    }
}
