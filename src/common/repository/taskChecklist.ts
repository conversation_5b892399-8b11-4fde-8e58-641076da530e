import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { TaskChecklist } from 'src/common/model/taskChecklist';
import { BaseRepository } from './base.repository';

@Injectable()
export class TaskChecklistRepository extends BaseRepository<TaskChecklist> {
    constructor() {
        super(TABLE.TASK_CHECKLIST_TABLE);
    }

    async findByTaskId(taskId: string) {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.CHECKLIST_TASK_INDEX,
            KeyConditionExpression:
                'taskChecklistTaskId = :taskChecklistTaskId',
            ExpressionAttributeValues: {
                ':taskChecklistTaskId': taskId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
