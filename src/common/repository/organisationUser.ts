import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { OrganisationUser } from 'src/common/model/organisationUser';
import { BaseRepository } from './base.repository';
import { v1 as uuid } from 'uuid';

@Injectable()
export class OrganisationUserRepository extends BaseRepository<OrganisationUser> {
    constructor() {
        super(TABLE.ORGANISATION_USER_TABLE);
    }

    async findByOrganisationId(
        organisationId: string,
    ): Promise<OrganisationUser[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.ORGANISATION_USER_ORGANISATION_INDEX,
            KeyConditionExpression:
                'organisationUserOrganisationId = :organisationUserOrganisationId',
            ExpressionAttributeValues: {
                ':organisationUserOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }

    async findByUserId(userId: string): Promise<OrganisationUser[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.ORGANISATION_USER_USER_INDEX,
            KeyConditionExpression:
                'organisationUserUserId = :organisationUserUserId',
            ExpressionAttributeValues: {
                ':organisationUserUserId': userId,
            },
        };

        const result = await this.query(params);
        return result;
    }

    async findByUserIdAndOrganisationId(
        userId: string,
        organisationId: string,
    ) {
        const params = {
            TableName: TABLE.ORGANISATION_USER_TABLE,
            IndexName: INDEX.ORGANISATION_USER_USER_INDEX,
            KeyConditionExpression:
                'organisationUserUserId = :organisationUserUserId',
            FilterExpression:
                'organisationUserOrganisationId = :organisationUserOrganisationId',
            ExpressionAttributeValues: {
                ':organisationUserUserId': userId,
                ':organisationUserOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result[0];
    }

    async findUserOrganisations(userId, organisationId) {
        const params = {
            ExpressionAttributeValues: {
                ':organisationUserUserId': userId,
                ':organisationUserOrganisationId': organisationId,
            },
            IndexName: INDEX.ORGANISATION_USER_USER_INDEX,
            KeyConditionExpression:
                'organisationUserUserId = :organisationUserUserId',
            FilterExpression:
                'organisationUserOrganisationId = :organisationUserOrganisationId',
            TableName: this.tableName,
        };

        return this.query(params);
    }

    async createOrganisationUser(userId: string, organisationId: string) {
        const params = {
            id: uuid(),
            organisationUserOrganisationId: organisationId,
            organisationUserUserId: userId,
        };
        await this.createItem(params);
    }
}
