import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { Tenancy } from 'src/common/model/tenancy';
import { BaseRepository } from './base.repository';

@Injectable()
export class TenancyRepository extends BaseRepository<Tenancy> {
    constructor() {
        super(TABLE.TENANCY_TABLE);
    }

    async findByOrganisationId(
        organisationId: string,
        archived?: boolean,
    ): Promise<Tenancy[]> {
        const params: any = {
            TableName: this.tableName,
            IndexName: INDEX.TENANCY_ORGANISATION_INDEX,
            KeyConditionExpression:
                'tenancyOrganisationId = :tenancyOrganisationId',
            ExpressionAttributeValues: {
                ':tenancyOrganisationId': organisationId,
            },
        };

        if (archived) {
            params.FilterExpression = 'archived = :archived';
            params.ExpressionAttributeValues[':archived'] = true;
        }

        const result = await this.query(params);
        return result;
    }

    async findByPropertyId(propertyId: string): Promise<Tenancy[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TENANCY_PROPERTY_INDEX,
            KeyConditionExpression: 'tenancyPropertyId = :tenancyPropertyId',
            FilterExpression: 'archived <> :archived',
            ExpressionAttributeValues: {
                ':tenancyPropertyId': propertyId,
                ':archived': true,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
