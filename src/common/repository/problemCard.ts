import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class ProblemCardRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.PROBLEM_CARD_TABLE);
    }

    async findByPropertyId(propertyId: string, asc?: boolean): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.PROBLEM_CARD_PROPERTY_INDEX,
            KeyConditionExpression:
                'problemCardPropertyId = :problemCardPropertyId',
            ExpressionAttributeValues: {
                ':problemCardPropertyId': propertyId,
            },
            ScanIndexForward: asc,
        };

        const result = await this.query(params);
        return result;
    }
}
