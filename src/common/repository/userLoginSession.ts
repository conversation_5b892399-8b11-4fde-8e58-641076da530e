import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { UserLoginSession } from '../model/userLoginSession';

@Injectable()
export class UserLoginSessionRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.USER_LOGIN_SESSION_TABLE);
    }

    async listByJti(jti: string): Promise<UserLoginSession[]> {
        const params = {
            ExpressionAttributeValues: {
                ':jti': jti,
            },
            IndexName: INDEX.USER_LOGIN_SESSION_JTI_INDEX,
            KeyConditionExpression:
                'jti = :jti',
            TableName: this.tableName,
        };
        return await this.query(params);
    }

    async listByDeviceIdAndEmail(deviceId: string, email: string): Promise<UserLoginSession[]> {
        const params = {
            ExpressionAttributeValues: {
                ':deviceId': deviceId,
                ':email': email,
            },
            IndexName: INDEX.USER_LOGIN_SESSION_INDEX,
            KeyConditionExpression:
                'deviceId = :deviceId AND email = :email',
            TableName: this.tableName,
        };
        return await this.query(params);
    }

    async listHistoryUserLoginSession() {
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
        const oneMonthAgoISO = oneMonthAgo.toISOString();

        const items = await this.scan(
            this.tableName,
            'createdAt < :dateLimit',
            { ':dateLimit': oneMonthAgoISO },
        );
        return items.map((item) => ({
            id: item.id,
        }));
    }
}
