import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { TaskScheduler } from 'src/common/model/taskScheduler';
import { BaseRepository } from './base.repository';

@Injectable()
export class TaskSchedulerRepository extends BaseRepository<TaskScheduler> {
    constructor() {
        super(TABLE.TASK_SCHEDULER_TABLE);
    }

    async findByOrganisationId(
        organisationId: string,
    ): Promise<TaskScheduler[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_SCHEDULER_ORGANISATION_INDEX,
            KeyConditionExpression:
                'taskSchedulerOrganisationId = :taskSchedulerOrganisationId',
            ExpressionAttributeValues: {
                ':taskSchedulerOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }

    async findByPropertyId(propertyId: string): Promise<TaskScheduler[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_SCHEDULER_PROPERTY_INDEX,
            KeyConditionExpression:
                'taskSchedulerPropertyId = :taskSchedulerPropertyId',
            ExpressionAttributeValues: {
                ':taskSchedulerPropertyId': propertyId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
