import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { Reminder } from 'src/common/model/reminder';
import { BaseRepository } from './base.repository';

@Injectable()
export class ReminderRepository extends BaseRepository<Reminder> {
    constructor() {
        super(TABLE.REMINDER_TABLE);
    }

    async findByOrganisationId(organisationId: string): Promise<Reminder[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.REMINDER_ORGANISATION_INDEX,
            KeyConditionExpression:
                'reminderOrganisationId = :reminderOrganisationId',
            ExpressionAttributeValues: {
                ':reminderOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
