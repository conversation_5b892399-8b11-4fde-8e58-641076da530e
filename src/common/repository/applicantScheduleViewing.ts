import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { ApplicantScheduleViewing } from '../model/applicantScheduleViewing';

@Injectable()
export class ApplicantScheduleViewingRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.APPLICANT_SCHEDULE_VIEWING_TABLE);
    }

    async findByOrganisationId(organisationId: string): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.APPLICANT_SCHEDULE_VIEWING_ORGANISATION_INDEX,
            KeyConditionExpression: 'organisationId = :organisationId',
            ExpressionAttributeValues: {
                ':organisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }

    async getApplicantScheduleViewingByApplicantId(
        applicantId: string,
    ): Promise<ApplicantScheduleViewing[]> {
        const params = {
            ExpressionAttributeValues: {
                ':applicantId': applicantId,
            },
            IndexName: INDEX.APPLICANT_SCHEDULE_VIEWING_APPLICANT_INDEX,
            KeyConditionExpression: 'applicantId = :applicantId',
            TableName: this.tableName,
        };
        return await this.query(params);
    }
}
