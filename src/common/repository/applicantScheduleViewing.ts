import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class ApplicantScheduleViewingRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.APPLICANT_SCHEDULE_VIEWING_TABLE);
    }

    async findByOrganisationId(organisationId: string): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.APPLICANT_SCHEDULE_VIEWING_ORGANISATION_INDEX,
            KeyConditionExpression: 'organisationId = :organisationId',
            ExpressionAttributeValues: {
                ':organisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
