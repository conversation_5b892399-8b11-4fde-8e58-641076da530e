import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const moment = require('moment');

@Injectable()
export class OrganisationUserMetaDataRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.ORGANISATION_USER_META_DATA_TABLE);
    }
    async createOrganisationUserMetaData(
        organisationId: string,
        userId: string,
    ) {
        const item = {
            id: `${organisationId}|${userId}`,
            watchHistory: [],
            updateAt: moment().utc().format(),
        };
        await this.createItem(item);
    }

    async deleteOrganisationUserMetaData(
        organisationId: string,
        userId: string,
    ) {
        await this.deleteById(`${organisationId}|${userId}`);
    }
}
