import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { Board } from 'src/common/model/board';
import { Column } from 'src/common/model/column';
import { Task } from 'src/common/model/task';
import { BaseRepository } from './base.repository';

@Injectable()
export class BoardRepository extends BaseRepository<Board> {
    constructor() {
        super(TABLE.BOARD_TABLE);
    }

    async findByOrganisationId(organisationId: string): Promise<Board[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.BOARD_ORGANISATION_INDEX,
            KeyConditionExpression:
                'boardOrganisationId = :boardOrganisationId',
            ExpressionAttributeValues: {
                ':boardOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }

    async transactCreateColumn(
        createColumnDTO: Column,
        updateBoardDTO: {
            id: string;
            columnCount: number;
        },
    ) {
        const putColumnItem = {
            TableName: TABLE.COLUMN_TABLE,
            Item: createColumnDTO,
        };

        const updateBoardItem = {
            TableName: TABLE.BOARD_TABLE,
            Key: {
                id: updateBoardDTO.id,
            },
            UpdateExpression: 'SET #columnCount = :columnCount',
            ExpressionAttributeNames: {
                '#columnCount': 'columnCount',
            },
            ExpressionAttributeValues: {
                ':columnCount': updateBoardDTO.columnCount,
            },
        };

        return await this.transactWrite([
            { Put: putColumnItem },
            { Update: updateBoardItem },
        ]);
    }

    async transactCreateTask(
        createTaskDTO: Task,
        updateBoardDTO: {
            id: string;
            taskCount: number;
            taskRefId: string;
        },
        updateColumnDTO: {
            id: string;
            taskCount: number;
        },
    ) {
        const putTaskItem = {
            TableName: TABLE.TASK_TABLE,
            Item: createTaskDTO,
        };

        const updateBoardItem = {
            TableName: TABLE.BOARD_TABLE,
            Key: {
                id: updateBoardDTO.id,
            },
            UpdateExpression:
                'SET #taskCount = :taskCount, #taskRefId = :taskRefId',
            ExpressionAttributeNames: {
                '#taskCount': 'taskCount',
                '#taskRefId': 'taskRefId',
            },
            ExpressionAttributeValues: {
                ':taskCount': updateBoardDTO.taskCount,
                ':taskRefId': updateBoardDTO.taskRefId,
            },
        };

        const updateColumnItem = {
            TableName: TABLE.COLUMN_TABLE,
            Key: {
                id: updateColumnDTO.id,
            },
            UpdateExpression: 'SET #taskCount = :taskCount',
            ExpressionAttributeNames: {
                '#taskCount': 'taskCount',
            },
            ExpressionAttributeValues: {
                ':taskCount': updateColumnDTO.taskCount,
            },
        };

        return await this.transactWrite([
            { Put: putTaskItem },
            { Update: updateBoardItem },
            { Update: updateColumnItem },
        ]);
    }

    async updateReferenceId(id: string, referenceId: string): Promise<Board> {
        return await this.updateItem(id, { referenceId });
    }
}
