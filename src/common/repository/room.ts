import { Injectable } from '@nestjs/common';
import { TABLE } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { Room } from '../model/room';

@Injectable()
export class RoomRepository extends BaseRepository<Room> {
    constructor() {
        super(TABLE.ROOM_TABLE);
    }

    async list(): Promise<Room[]> {
        return await this.scan(this.tableName);
    }
}
