import { Injectable } from '@nestjs/common';
import { INDEX, TABLE } from 'src/common/constant/dynamodb.constants';
import { OrganisationTaskChecklist } from 'src/common/model/organisationTaskChecklist';
import { BaseRepository } from './base.repository';

@Injectable()
export class OrganisationTaskChecklistRepository extends BaseRepository<OrganisationTaskChecklist> {
    constructor() {
        super(TABLE.ORGANISATION_TASK_CHECKLIST_TABLE);
    }

    async findByOrganisationIdAndSearch(
        organisationId: string,
        search: string,
    ): Promise<OrganisationTaskChecklist[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.ORGANISATION_TASK_CHECKLIST_ORGANISATION_INDEX,
            KeyConditionExpression:
                'organisationTaskChecklistOrganisationId = :organisationTaskChecklistOrganisationId',
            FilterExpression: 'contains(#name, :search)',
            ExpressionAttributeNames: {
                '#name': 'name',
            },
            ExpressionAttributeValues: {
                ':organisationTaskChecklistOrganisationId': organisationId,
                ':search': search,
            },
        };

        const result = await this.query(params);
        return result;
    }

    async getOrganisationTaskChecklistsByOrgId(
        orgId: string,
    ): Promise<OrganisationTaskChecklist[]> {
        return this.getOrganisationItems(
            this.tableName,
            INDEX.ORGANISATION_TASK_CHECKLIST_ORGANISATION_INDEX,
            'organisationTaskChecklistOrganisationId',
            orgId,
        );
    }
}
