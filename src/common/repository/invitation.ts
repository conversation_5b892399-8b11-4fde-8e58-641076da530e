import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';
import { Invitation } from '../model/invitation';

@Injectable()
export class InvitationRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.INVITATION_TABLE);
    }

    async findByEmail(email: string): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.INVITATION_EMAIL_INDEX,
            KeyConditionExpression: 'invitationEmail = :invitationEmail',
            ExpressionAttributeValues: {
                ':invitationEmail': email.toLowerCase(),
            },
        };

        const result = await this.query(params);
        return result;
    }

    async deleteInvitations(email, organisationId) {
        const params = {
            ExpressionAttributeValues: {
                ':email': email,
                ':organisationId': organisationId,
            },
            IndexName: INDEX.INVITATION_EMAIL_INDEX,
            KeyConditionExpression: 'email = :email',
            FilterExpression: 'invitationOrganisationId = :organisationId',
            TableName: this.tableName,
        };

        const items = await this.query(params);
        await Promise.all(items.map((item) => this.deleteById(item.id)));
    }

    async listByEmailAndOrganisation(
        email: string,
        organisationId: string,
    ): Promise<Invitation[]> {
        const params = {
            ExpressionAttributeValues: {
                ':email': email,
                ':organisationId': organisationId,
            },
            IndexName: INDEX.INVITATION_EMAIL_INDEX,
            KeyConditionExpression: 'email = :email',
            FilterExpression: 'invitationOrganisationId = :organisationId',
            TableName: this.tableName,
        };

        return await this.query(params);
    }

    async updateInvitation(invitation, tenancyId, conversationId, role, email) {
        const params = {
            ExpressionAttributeNames: {
                '#role': 'role',
            },
            ExpressionAttributeValues: {
                ':role': 'TENANT',
            },
            Key: {
                id: invitation.id,
            },
            TableName: this.tableName,
            UpdateExpression: 'SET #role = :role',
        };

        const tenancyIds = invitation.tenancyIds ? invitation.tenancyIds : [];
        const conversationIds = invitation.conversationIds
            ? invitation.conversationIds
            : [];

        if (tenancyId && !tenancyIds.includes(tenancyId)) {
            params.ExpressionAttributeNames['#tenancyIds'] = 'tenancyIds';
            params.ExpressionAttributeValues[':tenancyIds'] = tenancyIds.concat(
                [tenancyId],
            );
            params.UpdateExpression = `${params.UpdateExpression}, #tenancyIds = :tenancyIds`;
        }

        if (conversationId && !conversationIds.includes(conversationId)) {
            params.ExpressionAttributeNames['#conversationIds'] =
                'conversationIds';
            params.ExpressionAttributeValues[':conversationIds'] =
                conversationIds.concat([conversationId]);
            params.UpdateExpression = `${params.UpdateExpression}, #conversationIds = :conversationIds`;
        }

        if (email) {
            params.ExpressionAttributeNames['#email'] = 'email';
            params.ExpressionAttributeValues[':email'] = email;
            params.UpdateExpression = `${params.UpdateExpression}, #email = :email`;
        }

        if (role !== undefined && role !== '') {
            params.ExpressionAttributeValues[':role'] = role;
        }

        await this.ddb.update(params);
    }
}
