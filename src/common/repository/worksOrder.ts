import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class WorksOrderRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.WORKS_ORDER_TABLE);
    }

    async findByTaskId(taskId: string) {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_WORKS_ORDER_INDEX,
            KeyConditionExpression: 'worksOrderTaskId = :worksOrderTaskId',
            ExpressionAttributeValues: {
                ':worksOrderTaskId': taskId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
