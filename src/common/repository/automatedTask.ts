import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { AutomatedTask } from 'src/common/model/automatedTask';
import { BaseRepository } from './base.repository';

@Injectable()
export class AutomatedTaskRepository extends BaseRepository<AutomatedTask> {
    constructor() {
        super(TABLE.AUTOMATED_TASKS_TABLE);
    }

    async findByOrganisationId(
        organisationId: string,
    ): Promise<AutomatedTask[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.AUTOMATED_TASK_ORGANISATION_INDEX,
            KeyConditionExpression:
                'automatedTasksOrganisationId = :automatedTasksOrganisationId',
            ExpressionAttributeValues: {
                ':automatedTasksOrganisationId': organisationId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
