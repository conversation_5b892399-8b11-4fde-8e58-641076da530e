import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { INDEX, TABLE } from '../constant/dynamodb.constants';
import { Account } from '../model/account';

@Injectable()
export class AccountRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.ACTIVITY_TABLE);
    }
    async getXeroLedgerCodes(
        organisationId: string,
        tenantId: string,
    ): Promise<Account[]> {
        const params = {
            ExpressionAttributeValues: {
                ':accountOrganisationId': organisationId,
                ':tenantId': tenantId,
            },
            ExpressionAttributeNames: {
                '#code': 'code',
                '#name': 'name',
                '#tenantId': 'tenantId',
            },
            IndexName: INDEX.ACCOUNT_ORGANISATION_INDEX,
            KeyConditionExpression:
                'accountOrganisationId = :accountOrganisationId',
            FilterExpression:
                '#tenantId = :tenantId AND attribute_exists(#code) AND attribute_exists(#name)',
            TableName: this.tableName,
        };

        return await this.query(params);
    }
}
