import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { BaseRepository } from './base.repository';

@Injectable()
export class SupplierPropertyRepository extends BaseRepository<any> {
    constructor() {
        super(TABLE.SUPPLIER_PROPERTY_TABLE);
    }

    async findByPropertyId(propertyId: string): Promise<any[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.SUPPLIER_PROPERTY_PROPERTY_INDEX,
            KeyConditionExpression: 'supplierPropertyId = :supplierPropertyId',
            ExpressionAttributeValues: {
                ':supplierPropertyId': propertyId,
            },
        };

        const result = await this.query(params);
        return result;
    }
}
