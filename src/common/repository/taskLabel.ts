import { Injectable } from '@nestjs/common';
import { TABLE, INDEX } from 'src/common/constant/dynamodb.constants';
import { TaskLabel } from 'src/common/model/taskLabel';
import { BaseRepository } from './base.repository';

@Injectable()
export class TaskLabelRepository extends BaseRepository<TaskLabel> {
    constructor() {
        super(TABLE.TASK_LABEL_TABLE);
    }

    async findByOrganisationId(organisationId: string): Promise<TaskLabel[]> {
        const params = {
            TableName: this.tableName,
            IndexName: INDEX.TASK_LABEL_INDEX,
            KeyConditionExpression:
                'taskLabelOrganisationId = :taskLabelOrganisationId',
            ExpressionAttributeValues: {
                ':taskLabelOrganisationId': organisationId,
            },
            ScanIndexForward: true,
        };

        const result = await this.query(params);
        return result;
    }
}
