export enum ParentType {
    COMPOSITE = 'COMPOSITE',
    DOCUMENT = 'DOCUMENT',
    END_OF_TENANCY = 'END_OF_TENANCY',
    EMAIL = 'EMAIL',
    EVENT = 'EVENT',
    ORGANISATION = 'ORGANISATION',
    PARENTPROPERTYENTITY = 'PARENTPROPERTYENTITY',
    PROBLEM_CARD = 'PROBLEM_CARD',
    PROPERTY = 'PROPERTY',
    TASK = 'TASK',
    TENANCY = 'TENANCY',
    USER = 'USER',
}

export enum SortByDirection {
    ASC = 'ASC',
    DESC = 'DESC',
}
