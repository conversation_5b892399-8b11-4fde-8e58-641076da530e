import { <PERSON>, <PERSON>, Body, Param, Re<PERSON>, Logger } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, getSchemaPath } from '@nestjs/swagger';
import { GenericService } from '../service/generic.service';
import { BaseRes, responseError, responseOk } from '../util/requestUtil';
import { Response } from 'express';
import { ResponseCode } from '../constant/responseCode';

@Controller('/api/v1/generic')
export class GenericController {
    private logger = new Logger('GenericController', { timestamp: true });
    constructor(private genericService: GenericService) {}

    @ApiOperation({ summary: 'Create data in target table' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'object',
                        },
                    },
                },
            ],
        },
    })
    @Post('create/:modelType')
    async create(
        @Param('modelType') modelType: string,
        @Body() info: any,
        @Res() res: Response,
    ) {
        try {
            const result = await this.genericService.create(info, modelType);
            responseOk(res, result);
        } catch (e) {
            this.logger.error('/generic/create error:', e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update data in target table' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'object',
                        },
                    },
                },
            ],
        },
    })
    @Post('update/:modelType')
    async update(
        @Param('modelType') modelType: string,
        @Body() info: any,
        @Res() res: Response,
    ) {
        try {
            const result = await this.genericService.update(info, modelType);
            responseOk(res, result);
        } catch (e) {
            this.logger.error('/generic/update error:', e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete data in target table' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'object',
                        },
                    },
                },
            ],
        },
    })
    @Post('delete/:modelType')
    async deleteItem(
        @Param('modelType') modelType: string,
        @Body() info: any,
        @Res() res: Response,
    ) {
        try {
            const result = await this.genericService.deleteItem(
                info,
                modelType,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('/generic/deleteItem error:', e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get data in target table' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'object',
                        },
                    },
                },
            ],
        },
    })
    @Post('get/:modelType')
    async getItem(
        @Param('modelType') modelType: string,
        @Body() info: any,
        @Res() res: Response,
    ) {
        try {
            if (!info || !info.id || !info.fieldNames) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            const result = await this.genericService.getItem(
                info,
                modelType,
                true,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('/generic/getItem error:', e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
