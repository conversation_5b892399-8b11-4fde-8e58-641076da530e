import {
    ExceptionFilter,
    Catch,
    ArgumentsHost,
    HttpException,
    HttpStatus,
    Logger,
    NotFoundException,
} from '@nestjs/common';

@Catch()
export class LwExceptionsFilter implements ExceptionFilter {
    private logger = new Logger('LwExceptionsFilter', { timestamp: true });
    catch(exception: unknown, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const request = ctx.getRequest();
        const response = ctx.getResponse();

        const status =
            exception instanceof HttpException
                ? exception.getStatus()
                : HttpStatus.INTERNAL_SERVER_ERROR;

        const exceptionDetails =
            exception instanceof Error
                ? {
                      message: exception.message,
                      stack: exception.stack,
                  }
                : { message: String(exception) };

        if (!(exception instanceof NotFoundException)) {
            this.logger.error(
                `Exception occurred: ${exceptionDetails.message}\n` +
                    `Path: ${request.url}\n` +
                    `Stack: ${exceptionDetails.stack || 'No stack available'}`,
            );
        }

        response.status(status).json({
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
        });
    }
}
