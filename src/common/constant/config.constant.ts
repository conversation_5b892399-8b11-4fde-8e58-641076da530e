import * as dotenv from 'dotenv';

dotenv.config();

export const ENV_CONFIG = Object.freeze({
    REGION: `${process.env.REGION}`,
    STAGE: `${process.env.STAGE}`,
    ENV: `${process.env.ENV}`,
    COGNITO_USERPOOL_ID: `${process.env.COGNITO_USERPOOL_ID}`,
    CORS_DOMAINS: `${process.env.CORS_DOMAINS}`,
    API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT: `${process.env.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}`,
    COGNITO_CLIENT_ID: `${process.env.COGNITO_CLIENT_ID}`,
    OPENSEARCH_ENDPOINT: `${process.env.OPENSEARCH_ENDPOINT}`,
    EVENT_BUS_NAME: `${process.env.EVENT_BUS_NAME}`,
    EVENT_SOURCE: `${process.env.EVENT_SOURCE}`,
    JIRA_EMAIL: `${process.env.JIRA_EMAIL}`,
    JIRA_INSTANCE: `${process.env.JIRA_INSTANCE}`,
    JIRA_PROJECT_KEY: `${process.env.JIRA_PROJECT_KEY}`,
    DATABASE_URL: `${process.env.DATABASE_URL}`,
    SES_REGION: `${process.env.SES_REGION}`,
    SQS_REGION: `${process.env.SQS_REGION}`,
    PUBLIC_ORIGIN_DOMAIN: `${process.env.PUBLIC_ORIGIN_DOMAIN}`,
    MAIL_DOMAIN: `${process.env.MAIL_DOMAIN}`,
    CORE_API_URL: `${process.env.CORE_API_URL}`,
    INVITATION_LINK: `${process.env.INVITATION_LINK}`,
    SUB_NAMESPACE: `${process.env.SUB_NAMESPACE}`,
    SUB_ENDPOINT: `${process.env.SUB_ENDPOINT}`,
    SUB_API_KEY: `${process.env.SUB_API_KEY}`,
    APP_MARKET: `${process.env.APP_MARKET}`,
    VAPID_PUBLIC_KEY: `${process.env.VAPID_PUBLIC_KEY}`,
    GOOGLE_APPLICATION_CREDENTIALS: `${process.env.GOOGLE_APPLICATION_CREDENTIALS}`,
});
