export class ResponseCode {
    public static readonly SUCCESS = new ResponseCode(0, 'success');
    public static readonly SERVER_ERROR = new ResponseCode(
        1000,
        'server error',
    );
    public static readonly PARAM_INVALID = new ResponseCode(
        1001,
        'parameters invalid',
    );
    public static readonly FOR_BIDDEN = new ResponseCode(1003, 'forbidden');
    public static readonly NOT_FOUND = new ResponseCode(1002, 'item not found');
    public static readonly ILLEGAL_OPERATION = new ResponseCode(
        1004,
        'illegal operation',
    );

    constructor(
        public code: number,
        public message: string,
    ) {}
}
