import { ENV_CONFIG } from './config.constant';
const ddbSuffix = `${ENV_CONFIG.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${ENV_CONFIG.ENV}`;

export const TABLE = Object.freeze({
    ACTIVITY_TABLE: `Activity-${ddbSuffix}`,
    ACCOUNT_TABLE: `Account-${ddbSuffix}`,
    ADDRESS_TABLE: `Address-${ddbSuffix}`,
    APPLICANT_SCHEDULE_VIEWING_TABLE: `ApplicantScheduleViewing-${ddbSuffix}`,
    AUTOMATED_TASKS_TABLE: `AutomatedTasks-${ddbSuffix}`,
    BOARD_TABLE: `Board-${ddbSuffix}`,
    BUSINESS_TABLE: `Business-${ddbSuffix}`,
    CATEGORY_TABLE: `Category-${ddbSuffix}`,
    COLUMN_TABLE: `Column-${ddbSuffix}`,
    CONVERSATION_LINK_TABLE: `ConvoLink-${ddbSuffix}`,
    CONVERSATION_TABLE: `Conversation-${ddbSuffix}`,
    FILE_TABLE: `File-${ddbSuffix}`,
    INTEGRATION_TABLE: `Integration-${ddbSuffix}`,
    INVITATION_TABLE: `Invitation-${ddbSuffix}`,
    INVOICE_LINE_ITEM_TABLE: `InvoiceLineItem-${ddbSuffix}`,
    INVOICE_TABLE: `Invoice-${ddbSuffix}`,
    INVOICE_TENANCY_TABLE: `InvoiceTenancy-${ddbSuffix}`,
    JOURNAL_TABLE: `Journal-${ddbSuffix}`,
    ORGANISATION_TABLE: `Organisation-${ddbSuffix}`,
    ORGANISATION_TASK_CHECKLIST_TABLE: `OrganisationTaskChecklist-${ddbSuffix}`,
    ORGANISATION_USER_META_DATA_TABLE: `OrganisationUserMetaData-${ddbSuffix}`,
    ORGANISATION_USER_TABLE: `OrganisationUser-${ddbSuffix}`,
    PARENT_PROPERTY_ENTITY_TABLE: `ParentPropertyEntity-${ddbSuffix}`,
    PERMISSIONS_TABLE: `Permissions-${ddbSuffix}`,
    PROBLEM_CARD_TABLE: `ProblemCard-${ddbSuffix}`,
    PROPERTY_ASSET_TABLE: `PropertyAsset-${ddbSuffix}`,
    PROPERTY_TABLE: `Property-${ddbSuffix}`,
    REMINDER_TABLE: `Reminder-${ddbSuffix}`,
    ROOM_TABLE: `Room-${ddbSuffix}`,
    REPORTING_HISTORY_TABLE: `ReportingHistory-${ddbSuffix}`,
    SESSION_TABLE: `Session-${ddbSuffix}`,
    SUPPLIER_PROPERTY_TABLE: `SupplierProperty-${ddbSuffix}`,
    TASK_CHECKLIST_TABLE: `TaskChecklist-${ddbSuffix}`,
    TASK_COMMENT_TABLE: `TaskComment-${ddbSuffix}`,
    TASK_LABEL_TABLE: `TaskLabel-${ddbSuffix}`,
    TASK_SCHEDULER_TABLE: `TaskScheduler-${ddbSuffix}`,
    TASK_TABLE: `Task-${ddbSuffix}`,
    TEMPLATE_LETTER_TABLE: `TemplateLetter-${ddbSuffix}`,
    TENANCY_SETTINGS_TABLE: `TenancySettings-${ddbSuffix}`,
    TENANCY_TABLE: `Tenancy-${ddbSuffix}`,
    USER_LOGIN_SESSION_TABLE: `UserLoginSession-${ddbSuffix}`,
    USER_PREFERENCE_TABLE: `UserPreferences-${ddbSuffix}`,
    USER_TABLE: `User-${ddbSuffix}`,
    DOCUSIGN_TEMPLATES_TABLE: `DocusignTemplates-${ddbSuffix}`,
    TASK_REMINDER_TABLE: `TaskReminder-${ddbSuffix}`,
    WORKS_ORDER_TABLE: `WorksOrder-${ddbSuffix}`,
    CATEGORY_DOCUMENT_TABLE: `CategoryDocument-${ddbSuffix}`,
    DOCUMENT_TEMPLATE_TABLE: `DocumentTemplate-${ddbSuffix}`,
    SUBSCRIPTIONS_TABLE: `Subscriptions-${ddbSuffix}`,
    SUB_CATEGORY_TABLE: `SubCategory-${ddbSuffix}`,
});

export const INDEX = Object.freeze({
    ADDRESS_INDEX: 'gsi-ByParentIdAndParentType',
    ACCOUNT_ORGANISATION_INDEX: 'gsi-OrganisationAccounts',
    ACTIVITY_ORGANISATION_INDEX: 'gsi-OrganisationActivities',
    ADDRESS_ORGANISATION_INDEX: 'gsi-OrganisationAddresses',
    APPLICANT_SCHEDULE_VIEWING_ORGANISATION_INDEX: 'gsi-ByOrganisationId',
    APPLICANT_SCHEDULE_VIEWING_APPLICANT_INDEX: 'gsi-ByApplicantId',
    AUTOMATED_TASK_ORGANISATION_INDEX: 'gsi-OrganisationAutomatedTasks',
    BOARD_ORGANISATION_INDEX: 'gsi-OrgranisationBoards',
    CHECKLIST_TASK_INDEX: 'gsi-taskChecklists',
    COLUMN_BOARD_INDEX: 'gsi-BaordColumns',
    COLUMN_ORGANISATION_INDEX: 'gsi-OrgranisationColumns',
    COMMENT_TASK_INDEX: 'gsi-taskComment',
    CONTACT_COMMENT_ORGANISATION_INDEX: 'gsi-OrganisationContactComments',
    CONVERSATION_ORGANISATION_INDEX: 'gsi-OrganisationConversations',
    CONVO_LINK_ORGANISATION_INDEX: 'gsi-OrganisationLinks',
    DOCUSIGN_TEMPLATES_ORGANISATION_INDEX: 'gsi-OrganisationDocusignTemplates',
    DOCUMENT_ORGANISATION_UNSORTED_INDEX: 'gsi-OrganisationDocumentsUnsorted',
    EMAIL_MESSAGE_ORGANISATION_INDEX: 'gsi-OrganisationEmails',
    INVOICE_ALLOCATION_ORGANISATION_INDEX: 'gsi-OrganisationInvoiceAllocations',
    INVOICE_LINE_ITEM_ORGANISATION_INDEX: 'gsi-OrganisationLineItems',
    INVOICE_ORGANISATION_INDEX: 'gsi-OrganisationInvoices',
    INVOICE_PROPERTY_ORGANISATION_INDEX: 'gsi-OrganisationInvoiceProperties',
    INVOICE_WEBHOOK_EVENTS_ORGANISATION_INDEX: 'gsi-ByOrganisation',
    INVITATION_EMAIL_INDEX: 'gsi-ByEmail',
    INVITATION_ORGANISATION_INDEX: 'gsi-OrganisationInvitations',
    INVITATION_USER_INDEX: 'gsi-UserInvitation',
    INTEGRATION_ORGANISATION_INDEX: 'gsi-OrganisationIntegrations',
    JOURNAL_ORGANISATION_INDEX: 'gsi-OrganisationJournals',
    MESSAGE_ORGANISATION_INDEX: 'gsi-OrganisationMessages',
    ORGANISATION_USER_ORGANISATION_INDEX: 'gsi-OrgranisationUsers',
    ORGANISATION_USER_USER_INDEX: 'gsi-UserOrganisations',
    ORGANISATION_TASK_CHECKLIST_ORGANISATION_INDEX:
        'gsi-OrganisationChecklists',
    PAYMENT_ORGANISATION_INDEX: 'gsi-OrganisationPayments',
    PORTFOLIO_HISTORY_ORGANISATION_INDEX: 'gsi-OrganisationPortfolioHistory',
    PROBLEM_CARD_ORGANISATION_INDEX: 'gsi-OrganisationProblemCards',
    PROBLEM_CARD_PROPERTY_INDEX: 'gsi-PropertyProblemCards',
    PROPERTY_ORGANISATION_INDEX: 'gsi-OrganisationProperties',
    REMINDER_ORGANISATION_INDEX: 'gsi-OrganisationReminders',
    RENT_INVOICE_HISTORY_ORGANISATION_INDEX:
        'gsi-RentInvoiceHistoryOrganisation',
    REPORTING_HISTORY_ORGANISATION_INDEX: 'gsi-OrganisationReportingHistory',
    STATEMENT_ORGANISATION_INDEX: 'gsi-OrganisationStatements',
    SUPPLIER_ORGANISATION_ORGANISATION_INDEX:
        'gsi-OrganisationSupplierOrganisations',
    SUPPLIER_ORGANISATION_TAG_ORGANISATION_INDEX: 'gsi-OrganisationTags',
    SUPPLIER_PROPERTY_PROPERTY_INDEX: 'gsi-ByProperty',
    TASK_BOARD_INDEX: 'gsi-BoardTasks',
    TASK_COLUMN_INDEX: 'gsi-ColumnTasks',
    TASK_LABEL_INDEX: 'gsi-OrganisationTaskLabels',
    TASK_LABEL_ORGANISATION_INDEX: 'gsi-OrganisationTaskLabels',
    TASK_ORGANISATION_INDEX: 'gsi-OrgranisationTasks',
    TASK_REMINDER_ORGANISATION_INDEX: 'gsi-OrganisationTaskReminders',
    TASK_SCHEDULER_ORGANISATION_INDEX: 'gsi-organisationTaskSchedulers',
    TASK_SCHEDULER_PROPERTY_INDEX: 'gsi-propertyTaskSchedulers',
    TASK_WORKS_ORDER_INDEX: 'gsi-worksOrdersTask',
    TASK_TASK_LABEL_INDEX: 'gsi-TaskLabels',
    TEMPLATE_ORGANISATION_INDEX: 'gsi-OrganisationTemplateLetters',
    TENANCY_INVOICE_INDEX: 'gsi-TenancyInvoices',
    TENANCY_ORGANISATION_INDEX: 'gsi-OrganisationTenancies',
    TENANCY_PROPERTY_INDEX: 'gsi-PropertyTenancies',
    TENANCY_SETTINGS_ORGANISATION_INDEX: 'gsi-OrganisationSettings',
    TRANSACTION_ORGANISATION_INDEX: 'gsi-OrganisationTransactions',
    TRANSFER_ORGANISATION_INDEX: 'gsi-OrganisationTransfers',
    USER_COGNITO_ID_INDEX: 'gsi-ByCognitoId',
    USER_ORGANISATION_INDEX: 'gsi-ByOrganisation',
    USER_PREFERENCES_USER_INDEX: 'gsi-ByUserId',
    USER_COGNITO_EMAIL_INDEX: 'gsi-ByCognitoEmail',
    USER_LOGIN_SESSION_JTI_INDEX: 'gsi-Jti',
    USER_LOGIN_SESSION_INDEX: 'gsi-DeviceEmail',
    XERO_JOURNAL_ORGANISATION_INDEX: 'gsi-OrganisationXeroJournals',
});

export function getTableName(modelType: string): string {
    if (!modelType) {
        return '';
    }
    const withSpaces = modelType.replace(/([A-Z])/g, ' $1').trim();
    const underscoreCase = withSpaces.toUpperCase().replace(/\s+/g, '_');
    const tableName = `${underscoreCase}_TABLE`;
    return TABLE[tableName];
}
