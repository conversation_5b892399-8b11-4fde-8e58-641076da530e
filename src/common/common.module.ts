import { Global, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PrismaService } from './service/prisma.service';
import { RedisService } from './service/redis.service';
import { OAuth2Service } from './service/oauth2.service';
import { AppLoggerMiddleware } from './middleware/appLogger.middleware';
import { AuthorizationMiddleware } from './middleware/authorization.middleware';
import { HealthController } from './controller/health.controller';
import { GenericController } from './controller/generic.controller';
import { GenericService } from './service/generic.service';
import { DynamodbService } from './service/dynamodb.service';
import { EventsEmitterService } from './service/eventsEmitter.service';
import { ActivityRepository } from './repository/activity';
import { AddressRepository } from './repository/address';
import { ApplicantScheduleViewingRepository } from './repository/applicantScheduleViewing';
import { AutomatedTaskRepository } from './repository/automatedTask';
import { BoardRepository } from './repository/board';
import { CateRepository } from './repository/category';
import { ColumnRepository } from './repository/column';
import { ConversationLinkRepository } from './repository/conversationLink';
import { ConversationRepository } from './repository/conversation';
import { DocusignTemplateRepository } from './repository/docusignTemplate';
import { FileRepository } from './repository/file';
import { IntegrationRepository } from './repository/integration';
import { InvoiceLineItemRepository } from './repository/invoiceLineItem';
import { InvoiceRepository } from './repository/invoice';
import { InvoiceTenancyRepository } from './repository/invoiceTenancy';
import { InvitationRepository } from './repository/invitation';
import { JournalRepository } from './repository/journal';
import { OrganisationRepository } from './repository/organisation';
import { OrganisationTaskChecklistRepository } from './repository/organisationTaskChecklist';
import { OrganisationUserMetaDataRepository } from './repository/organisationUserMetaData';
import { OrganisationUserRepository } from './repository/organisationUser';
import { ParentPropertyEntityRepository } from './repository/parentPropertyEntity';
import { PermissionRepository } from './repository/permission';
import { ProblemCardRepository } from './repository/problemCard';
import { PropertyRepository } from './repository/property';
import { RoomRepository } from './repository/room';
import { ReminderRepository } from './repository/reminder';
import { ReportingHistoryRepository } from './repository/reportingHistory';
import { SessionRepository } from './repository/session';
import { SupplierPropertyRepository } from './repository/supplierProperty';
import { TaskChecklistRepository } from './repository/taskChecklist';
import { TaskCommentRepository } from './repository/taskComment';
import { TaskLabelRepository } from './repository/taskLabel';
import { TaskReminderRepository } from './repository/taskReminder';
import { TaskRepository } from './repository/task';
import { TaskSchedulerRepository } from './repository/taskScheduler';
import { TemplateLetterRepository } from './repository/templateLetter';
import { TenancyRepository } from './repository/tenancy';
import { TenancySettingRepository } from './repository/tenancySetting';
import { UserLoginSessionRepository } from './repository/userLoginSession';
import { UserPreferenceRepository } from './repository/userPreference';
import { UserRepository } from './repository/user';
import { WorksOrderRepository } from './repository/worksOrder';
import { AccountRepository } from './repository/account';
import { CategoryDocumentRepository } from './repository/categoryDocument';
import { SubscriptionsRepository } from './repository/subscriptions';
import { SubCategoryRepository } from './repository/subCategory';
import { BusinessRepository } from './repository/business';

@Global()
@Module({
    imports: [HttpModule],
    controllers: [HealthController, GenericController],
    providers: [
        RedisService,
        OAuth2Service,
        GenericService,
        DynamodbService,
        EventsEmitterService,
        PrismaService,
        AppLoggerMiddleware,
        AuthorizationMiddleware,
        ActivityRepository,
        AddressRepository,
        ApplicantScheduleViewingRepository,
        AutomatedTaskRepository,
        BoardRepository,
        CateRepository,
        ColumnRepository,
        ConversationLinkRepository,
        ConversationRepository,
        DocusignTemplateRepository,
        FileRepository,
        IntegrationRepository,
        InvitationRepository,
        InvoiceLineItemRepository,
        InvoiceRepository,
        InvoiceTenancyRepository,
        JournalRepository,
        OrganisationRepository,
        OrganisationTaskChecklistRepository,
        OrganisationUserMetaDataRepository,
        OrganisationUserRepository,
        ParentPropertyEntityRepository,
        PermissionRepository,
        ProblemCardRepository,
        PropertyRepository,
        RoomRepository,
        ReminderRepository,
        ReportingHistoryRepository,
        SessionRepository,
        SupplierPropertyRepository,
        TaskChecklistRepository,
        TaskCommentRepository,
        TaskLabelRepository,
        TaskReminderRepository,
        TaskRepository,
        TaskSchedulerRepository,
        TemplateLetterRepository,
        TenancyRepository,
        TenancySettingRepository,
        UserLoginSessionRepository,
        UserPreferenceRepository,
        UserRepository,
        WorksOrderRepository,
        AccountRepository,
        CategoryDocumentRepository,
        SubscriptionsRepository,
        SubCategoryRepository,
        BusinessRepository,
    ],
    exports: [
        OAuth2Service,
        GenericService,
        DynamodbService,
        EventsEmitterService,
        PrismaService,
        AppLoggerMiddleware,
        AuthorizationMiddleware,
        ActivityRepository,
        AddressRepository,
        ApplicantScheduleViewingRepository,
        AutomatedTaskRepository,
        BoardRepository,
        CateRepository,
        ColumnRepository,
        ConversationLinkRepository,
        ConversationRepository,
        DocusignTemplateRepository,
        FileRepository,
        IntegrationRepository,
        InvitationRepository,
        InvoiceLineItemRepository,
        InvoiceRepository,
        InvoiceTenancyRepository,
        JournalRepository,
        OrganisationRepository,
        OrganisationTaskChecklistRepository,
        OrganisationUserMetaDataRepository,
        OrganisationUserRepository,
        ParentPropertyEntityRepository,
        PermissionRepository,
        ProblemCardRepository,
        PropertyRepository,
        RoomRepository,
        ReminderRepository,
        ReportingHistoryRepository,
        SessionRepository,
        SupplierPropertyRepository,
        TaskChecklistRepository,
        TaskCommentRepository,
        TaskLabelRepository,
        TaskReminderRepository,
        TaskRepository,
        TaskSchedulerRepository,
        TemplateLetterRepository,
        TenancyRepository,
        TenancySettingRepository,
        UserLoginSessionRepository,
        UserPreferenceRepository,
        UserRepository,
        WorksOrderRepository,
        AccountRepository,
        CategoryDocumentRepository,
        RedisService,
        SubscriptionsRepository,
        SubCategoryRepository,
        BusinessRepository,
    ],
})
export class CommonModule {}
