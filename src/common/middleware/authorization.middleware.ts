import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { RedisService } from 'src/common/service/redis.service';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class AuthorizationMiddleware implements NestMiddleware {
    private logger = new Logger('UserService', { timestamp: true });

    constructor(
        private redisService: RedisService,
        private httpService: HttpService,
    ) {}

    /*private verifier = CognitoJwtVerifier.create({
        userPoolId: process.env.COGNITO_USERPOOL_ID,
        tokenUse: 'id',
        clientId: process.env.COGNITO_CLIENT_ID,
    });*/

    async use(req: any, res: Response, next: NextFunction) {
        if (req.cookies['_T']) {
            try {
                const resp = await this.httpService.axiosRef.post(
                    process.env.CRM_FETCH_TICKET_URL,
                    req.cookies['_T'],
                    {
                        headers: {
                            'Content-Type': 'text/plain',
                        },
                    },
                );
                if (resp.data.sub && resp.data.email) {
                    req.user = resp.data;
                    next();
                } else {
                    this.logger.log(
                        `fetchTicket:resp=${JSON.stringify(resp.data)}`,
                    );
                    return res.status(401).json({ error: 'Unauthorized.' });
                }
            } catch (err) {
                this.logger.log(`fetchTicket:error=${JSON.stringify(err)}`);
                return res.status(401).json({ error: 'Unauthorized.' });
            }
        } else if (req.cookies['letting-jti'] && req.cookies['letting-auth']) {
            try {
                const jti = await this.redisService.get(
                    req.cookies['letting-jti'],
                );
                if (!jti) {
                    return res.status(401).json({ error: 'Unauthorized.' });
                }
                const parts = req.cookies['letting-auth'].split('.');
                if (parts.length !== 3) {
                    throw new Error('Invalid JWT format');
                }
                // Decode the payload (middle part)
                const payload = parts[1];
                const decodedPayload = Buffer.from(payload, 'base64').toString(
                    'utf-8',
                );
                // Parse the JSON payload
                const result = JSON.parse(decodedPayload);
                req.user = result;
                next();
            } catch (err) {
                return res.status(401).json({ error: 'Unauthorized.' });
            }
        } else {
            const token = req.headers.authorization?.split(' ')[1];
            if (!token) {
                return res.status(401).json({ error: 'Unauthorized.' });
            }

            /*try {
                const payload = await this.verifier.verify(token);
                req.user = payload;
                next();
            } catch (err) {
                return res.status(401).json({ error: 'Unauthorized.' });
            }*/
            next();
        }
    }
}
