import { Injectable, NestMiddleware } from '@nestjs/common';
import { Response, NextFunction } from 'express';

import { CognitoJwtVerifier } from 'aws-jwt-verify';

@Injectable()
export class AuthorizationMiddleware implements NestMiddleware {

    private verifier = CognitoJwtVerifier.create({
        userPoolId: process.env.COGNITO_USERPOOL_ID,
        tokenUse: 'id',
        clientId: process.env.COGNITO_CLIENT_ID,
    });

    async use(req: any, res: Response, next: NextFunction) {
        const token = req.headers.authorization?.split(' ')[1];
        if (!token) {
            return res.status(401).json({ error: 'Unauthorized.' });
        }

        try {
            const payload = await this.verifier.verify(token);
            req.user = payload;
            res.cookie('c-jti', payload.jti, {
                httpOnly: true,
            });
            next();
        } catch (err) {
            return res.status(401).json({ error: 'Unauthorized.' });
        }
    }
}
