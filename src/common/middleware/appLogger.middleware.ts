import { Injectable, NestMiddleware, Logger } from '@nestjs/common';

import { Request, Response, NextFunction } from 'express';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
    private logger = new Logger('HTTP');

    use(request: Request, response: Response, next: NextFunction): void {
        const { ip, method, url, baseUrl } = request;
        const userAgent = request.get('user-agent') || '';
        const startTime = Date.now();

        if (baseUrl.startsWith('/health')) {
            next();
            return;
        }

        response.on('close', () => {
            const { statusCode } = response;
            const contentLength = response.get('content-length') || 0;
            const elapsedTime = Date.now() - startTime;
            this.logger.log(
                `${method}|${baseUrl}${url}|${statusCode}|${contentLength}|${userAgent}|${ip}|${elapsedTime}ms`,
            );
        });

        next();
    }
}
