import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SearchService } from '../service/search.service';
import { SearchQueryDTO } from '../model/search.model';
import { LwRequest } from '../../common/util/requestUtil';
import { Req } from '@nestjs/common';

@ApiTags('Search')
@Controller('api/v1/search')
export class SearchController {
    constructor(private readonly searchService: SearchService) {}

    @Get()
    @ApiOperation({
        summary: 'Universal search endpoint',
        description:
            'Search across properties, tenancies, references, and documents',
    })
    @ApiResponse({
        status: 200,
        description: 'Search results',
    })
    async search(
        @Query() query: SearchQueryDTO,
        @Req() req: LwRequest,
    ): Promise<any> {
        const organisationId = req.user['custom:organisationId'];
        return this.searchService.search(query, organisationId);
    }
}
