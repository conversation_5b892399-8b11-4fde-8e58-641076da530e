import { Modu<PERSON> } from '@nestjs/common';
import { SearchController } from './controller/search.controller';
import { SearchService } from './service/search.service';
import { PropertyModule } from '../property/property.module';
import { ContractModule } from '../contract/contract.module';
import { DocumentModule } from '../document/document.module';
@Module({
    imports: [PropertyModule, ContractModule, DocumentModule],
    controllers: [SearchController],
    providers: [SearchService],
    exports: [SearchService],
})
export class SearchModule {}
