import { ApiProperty } from '@nestjs/swagger';

export enum SearchType {
    PROPERTY = 'property',
    TENANCY = 'tenancy',
    REFERENCE = 'reference',
    DOCUMENT = 'document',
}

export class SearchQueryDTO {
    @ApiProperty()
    keyword: string;

    @ApiProperty({
        enum: SearchType,
        description: 'One of: property, tenancy, reference, document',
    })
    type: SearchType;
}

export class PropertySearchResult {
    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    addressLine1: string;

    @ApiProperty()
    addressLine2: string;

    @ApiProperty()
    city: string;

    @ApiProperty()
    postCode: string;
}

export class TenancySearchResult {
    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    tenancyId: string;

    @ApiProperty()
    reference: string;

    @ApiProperty()
    address: string;
}

export class DocumentSearchResult {
    @ApiProperty()
    documentId: string;

    @ApiProperty()
    fileName: string;
}
