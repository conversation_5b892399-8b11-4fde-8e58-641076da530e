import { Injectable, Logger } from '@nestjs/common';
import { PropertyService } from '../../property/service/property.service';
import { SearchQueryDTO, SearchType } from '../model/search.model';
import { ContractService } from '../../contract/service/contract.service';
import { DocumentService } from '../../document/service/document.service';

@Injectable()
export class SearchService {
    private readonly logger = new Logger(SearchService.name);

    constructor(
        private propertyService: PropertyService,
        private contractService: ContractService,
        private documentService: DocumentService,
    ) {}

    async search(query: SearchQueryDTO, organisationId: string): Promise<any> {
        try {
            switch (query.type) {
                case SearchType.PROPERTY:
                    return await this.searchProperties(
                        query.keyword,
                        organisationId,
                    );
                case SearchType.TENANCY:
                case SearchType.REFERENCE:
                    return await this.searchTenancies(
                        query.keyword,
                        organisationId,
                    );
                case SearchType.DOCUMENT:
                    return await this.searchDocuments(
                        query.keyword,
                        organisationId,
                    );
                default:
                    throw new Error(`Unsupported search type: ${query.type}`);
            }
        } catch (error) {
            this.logger.error(`Search failed: ${error.message}`, error.stack);
            throw error;
        }
    }

    private async searchProperties(
        keyword: string,
        organisationId: string,
    ): Promise<any> {
        const { propertyList: properties } =
            await this.propertyService.searchOrgProperties({
                organisationId,
                address: keyword,
            });
        return {
            type: SearchType.PROPERTY,
            results: properties.map((property) => ({
                id: property.id,
                name: property.name,
                subTitle: property?.reference,
            })),
        };
    }

    private async searchTenancies(
        keyword: string,
        organisationId: string,
    ): Promise<any> {
        const tenancyList = await this.contractService.searchByKeyword(
            organisationId,
            keyword,
        );
        return {
            type: SearchType.TENANCY,
            results: tenancyList,
        };
    }

    private async searchDocuments(
        keyword: string,
        organisationId: string,
    ): Promise<any> {
        const { data } = await this.documentService.getDocumentByPage({
            organisationId,
            name: keyword,
            page: 1,
            size: 10,
        });
        return {
            type: SearchType.DOCUMENT,
            results: data.map((document: { id: string; name: string }) => ({
                id: document.id,
                fileName: document.name,
                type: 'document',
            })),
        };
    }
}
