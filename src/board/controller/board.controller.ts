import { Response } from 'express';
import {
    Body,
    Controller,
    Delete,
    Get,
    HttpStatus,
    Logger,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiBody,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiQuery,
    getSchemaPath,
} from '@nestjs/swagger';

import { ResponseCode } from 'src/common/constant/responseCode';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from 'src/common/util/requestUtil';

import { TaskStatus } from 'src/common/enum/task';
import { TaskLabelName } from 'src/common/enum/taskLabel';

import { SortBy } from 'src/common/model';
import { AutomatedTask } from 'src/common/model/automatedTask';
import { Board } from 'src/common/model/board';
import { Column } from 'src/common/model/column';
import { OrganisationTaskChecklist } from 'src/common/model/organisationTaskChecklist';
import { ParentPropertyEntity } from 'src/common/model/parentPropertyEntity';
import { Property } from 'src/common/model/property';
import { Task } from 'src/common/model/task';
import { TaskLabel } from 'src/common/model/taskLabel';
import { Frequency, TaskScheduler } from 'src/common/model/taskScheduler';
import { User } from 'src/common/model/user';

import {
    PropertyScheduledTasksQueryDTO,
    PropertyTasksQueryDTO,
    ScheduledTasksQueryDTO,
    TaskLabelItemVO,
} from '../model/board.model';
import { BoardService } from '../service/board.service';
import { SubscriptionUtil } from '../../common/util/subscriptionUtil';

@ApiExtraModels(
    BaseRes,
    AutomatedTask,
    Board,
    Column,
    Frequency,
    OrganisationTaskChecklist,
    ParentPropertyEntity,
    Property,
    Task,
    TaskLabel,
    TaskScheduler,
    User,
    PropertyScheduledTasksQueryDTO,
    PropertyTasksQueryDTO,
    ScheduledTasksQueryDTO,
)
@Controller('api/v1/board')
@ApiBearerAuth()
export class BoardController {
    private logger = new Logger('BoardController', { timestamp: true });

    constructor(private boardService: BoardService) {}

    /**************** Field start ****************/
    @ApiOperation({ summary: 'Get Tasks For Board' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Task),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                boardId: {
                    type: 'string',
                    description: 'Board ID',
                },
                propertyIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: 'Property IDs',
                },
                userIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: 'User IDs',
                },
                deadline: {
                    type: 'object',
                    properties: {
                        le: {
                            type: 'string',
                            format: 'date-time',
                            description: 'Less Than Or Equal',
                        },
                        ge: {
                            type: 'string',
                            format: 'date-time',
                            description: 'Greater Than Or Equal',
                        },
                    },
                    description: 'Deadline',
                },
                limit: {
                    type: 'number',
                    description: 'Limit',
                },
            },
            required: ['boardId'],
        },
    })
    @Post('/getTasksForBoard')
    public async getTasksForBoard(
        @Body('boardId') boardId: string,
        @Body('propertyIds') propertyIds: string[],
        @Body('userIds') userIds: string[],
        @Body('deadline')
        deadline: {
            le: string;
            ge: string;
        },
        @Body('limit') limit: number,
        @Res() response: Response,
    ) {
        if (!boardId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getTasksForBoard({
                boardId,
                propertyIds,
                userIds,
                deadline,
                limit,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Board For Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Board),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'boardId',
        description: 'Board ID',
        required: true,
    })
    @Get('/getBoardForTask')
    public async getBoardForTask(
        @Query('boardId') boardId: string,
        @Res() response: Response,
    ) {
        if (!boardId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getBoardForTask(boardId);
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Column For Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Column),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'columnId',
        description: 'Column ID',
        required: true,
    })
    @Get('/getColumnForTask')
    public async getColumnForTask(
        @Query('columnId') columnId: string,
        @Res() response: Response,
    ) {
        if (!columnId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getColumnForTask(columnId);
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Linked Contractor For Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(User),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'contractorUserId',
        description: 'Contractor User ID',
        required: true,
    })
    @Get('/getLinkedContractorForTask')
    public async getLinkedContractorForTask(
        @Query('contractorUserId') contractorUserId: string,
        @Res() response: Response,
    ) {
        if (!contractorUserId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data =
                await this.boardService.getLinkedContractorForTask(
                    contractorUserId,
                );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Parent Property Entity For Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(ParentPropertyEntity),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'parentPropertyEntityId',
        description: 'Parent Property Entity ID',
        required: true,
    })
    @Get('/getParentPropertyEntityForTask')
    public async getParentPropertyEntityForTask(
        @Query('parentPropertyEntityId') parentPropertyEntityId: string,
        @Res() response: Response,
    ) {
        if (!parentPropertyEntityId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getParentPropertyEntityForTask(
                parentPropertyEntityId,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Property For Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Property),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'propertyId',
        description: 'Property ID',
        required: true,
    })
    @Get('/getPropertyForTask')
    public async getPropertyForTask(
        @Query('propertyId') propertyId: string,
        @Res() response: Response,
    ) {
        if (!propertyId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getPropertyForTask(propertyId);
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }
    /**************** Field end ****************/

    /**************** Query start ****************/
    @ApiOperation({ summary: 'Get Archived Tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Task),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/getArchivedTasks')
    public async getArchivedTasks(
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        try {
            const data = await this.boardService.getArchivedTasks(
                request.user.sub,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Automated Tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'object',
                            properties: {
                                documentEvents: {
                                    type: 'array',
                                    items: {
                                        $ref: getSchemaPath(AutomatedTask),
                                    },
                                },
                                propertyEvents: {
                                    type: 'array',
                                    items: {
                                        $ref: getSchemaPath(AutomatedTask),
                                    },
                                },
                                tenancyEvents: {
                                    type: 'array',
                                    items: {
                                        $ref: getSchemaPath(AutomatedTask),
                                    },
                                },
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/automated-tasks')
    public async getAutomatedTasks(
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        try {
            const data = await this.boardService.getAutomatedTasks(
                request.user['custom:organisationId'],
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Boards' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Board),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/list')
    public async getBoards(
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        try {
            const data = await this.boardService.getBoardsWithColumns(
                request.user['custom:organisationId'],
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Board Columns' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Column),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                boardId: {
                    type: 'string',
                    description: 'Board ID',
                },
                propertyIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: 'Property IDs',
                },
            },
            required: ['boardId'],
        },
    })
    @Post('/getBoardColumns')
    public async getBoardColumns(
        @Body('boardId') boardId: string,
        @Body('propertyIds') propertyIds: string[],
        @Res() response: Response,
    ) {
        if (!boardId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getBoardColumns(
                boardId,
                propertyIds,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Board Tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Task),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                boardId: {
                    type: 'string',
                    description: 'Board ID',
                },
                propertyIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    description: 'Property IDs',
                },
                status: {
                    type: 'string',
                    enum: Object.values(TaskStatus),
                    description: 'Status',
                },
                assignee: {
                    type: 'string',
                    description: 'Assignee',
                },
            },
            required: ['boardId'],
        },
    })
    @Post('/getBoardTasks')
    public async getBoardTasks(
        @Body('boardId') boardId: string,
        @Body('propertyIds') propertyIds: string[],
        @Body('status') status: TaskStatus,
        @Body('assignee') assignee: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!boardId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getBoardTasks(
                boardId,
                propertyIds,
                status,
                assignee,
                request.user['custom:organisationId'],
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Dashboard Task Widget' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'object',
                            properties: {
                                totalUnfinishedTasks: {
                                    type: 'number',
                                },
                                unassignedTasks: {
                                    type: 'number',
                                },
                                overdueTasks: {
                                    type: 'number',
                                },
                                dueTodayTasks: {
                                    type: 'number',
                                },
                                dueWeekTasks: {
                                    type: 'number',
                                },
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'organisationId',
        description: 'Organisation ID',
        required: true,
    })
    @Get('/getDashboardTaskWidget')
    public async getDashboardTaskWidget(
        @Query('organisationId') organisationId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!organisationId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getDashboardTaskWidget(
                request.user.sub,
                organisationId,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Property Scheduled Tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(TaskScheduler),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            $ref: getSchemaPath(PropertyScheduledTasksQueryDTO),
        },
    })
    @Post('/getPropertyScheduledTasks')
    public async getPropertyScheduledTasks(
        @Body('organisationId') organisationId: string,
        @Body('propertyId') propertyId: string,
        @Body('taskLabelName') taskLabelName: TaskLabelName,
        @Body('active') active: boolean,
        @Body('page') page: number,
        @Body('pageSize') pageSize: number,
        @Res() response: Response,
    ) {
        if (
            (taskLabelName && !organisationId) ||
            !propertyId ||
            !page ||
            !pageSize
        ) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getPropertyScheduledTasks({
                organisationId,
                propertyId,
                taskLabelName,
                active,
                page,
                pageSize,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Property Tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Task),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            $ref: getSchemaPath(PropertyTasksQueryDTO),
        },
    })
    @Post('/getPropertyTasks')
    public async getPropertyTasks(
        @Body('organisationId') organisationId: string,
        @Body('propertyId') propertyId: string,
        @Body('status') status: TaskStatus,
        @Body('sortBy') sortBy: SortBy,
        @Body('page') page: number = 1,
        @Body('pageSize') pageSize: number = 30,
        @Res() response: Response,
    ) {
        if (!organisationId || !propertyId || !page || !pageSize) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.getPropertyTasks({
                organisationId,
                propertyId,
                status,
                sortBy,
                page,
                pageSize,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Scheduled Tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'object',
                            properties: {
                                active: {
                                    type: 'number',
                                },
                                notActive: {
                                    type: 'number',
                                },
                                total: {
                                    type: 'number',
                                },
                                items: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            id: {
                                                type: 'string',
                                            },
                                            property: {
                                                type: 'string',
                                            },
                                            taskLabel: {
                                                type: 'string',
                                            },
                                            frequency: {
                                                $ref: getSchemaPath(Frequency),
                                            },
                                            active: {
                                                type: 'boolean',
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            $ref: getSchemaPath(ScheduledTasksQueryDTO),
        },
    })
    @Post('/scheduled-tasks')
    public async getScheduledTasks(
        @Body('propertyIds') propertyIds: string[],
        @Body('taskLabelIds') taskLabelIds: string[],
        @Body('active') active: boolean,
        @Body('page') page: number = 1,
        @Body('pageSize') pageSize: number = 30,
        @Res() response: Response,
        @Req() request: LwRequest,
    ) {
        try {
            const data = await this.boardService.getScheduledTasks({
                organisationId: request.user['custom:organisationId'],
                propertyIds,
                taskLabelIds,
                active,
                page,
                pageSize,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Task Labels' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(TaskLabelItemVO),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'includeTasks',
        required: false,
        description: 'include tasks or not, default: false',
        example: false,
    })
    @Get('/task-labels')
    public async getTaskLabels(
        @Req() request: LwRequest,
        @Res() response: Response,
        @Query('includeTasks') includeTasks: boolean = false,
    ) {
        try {
            const data = await this.boardService.getTaskLabels(
                request.user['custom:organisationId'],
                includeTasks,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Search Organisation Task Checklists' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(OrganisationTaskChecklist),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'search',
        description: 'Search',
        required: true,
    })
    @Get('/searchOrganisationTaskChecklists')
    public async searchOrganisationTaskChecklists(
        @Query('search') search: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!search) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data =
                await this.boardService.searchOrganisationTaskChecklists(
                    request.user.sub,
                    search,
                );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }
    /**************** Query end ****************/

    /**************** Mutation start ****************/
    @ApiOperation({ summary: 'Create Column' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Column),
                        },
                    },
                },
            ],
        },
    })
    @Post('/createColumn')
    public async createColumn(
        @Body() input: Column,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!input) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.createColumn(
                request.user.sub,
                input,
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_COLUMN(
                    data.columnBoardId,
                ),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Create Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @Post('/createTask')
    public async createTask(
        @Body() input: Task,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!input) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.createTask(
                request.user.sub,
                input,
                request.headers['authorization']?.split(' ')[1],
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_TASK(data.taskBoardId),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Create Task For Rightmove' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                propertyId: {
                    type: 'string',
                    description: 'Property ID',
                },
                type: {
                    type: 'string',
                    enum: ['REMOVE', 'SEND'],
                    description: 'Type',
                },
            },
            required: ['propertyId', 'type'],
        },
    })
    @Post('/createTaskForRightmove')
    public async createTaskForRightmove(
        @Body('propertyId') propertyId: string,
        @Body('type') type: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!propertyId || !type) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.createTaskForRightmove(
                request.user.sub,
                {
                    propertyId,
                    type,
                },
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Create Task For Schedule Viewing' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                cognitoId: {
                    type: 'string',
                    description: 'Cognito ID',
                },
                taskId: {
                    type: 'string',
                    description: 'Task ID',
                },
                organisationId: {
                    type: 'string',
                    description: 'Organisation ID',
                },
                propertyId: {
                    type: 'string',
                    description: 'Property ID',
                },
                applicantId: {
                    type: 'string',
                    description: 'Applicant ID',
                },
                deadline: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Deadline',
                },
                currentUserId: {
                    type: 'string',
                    description: 'Current User ID',
                },
            },
        },
    })
    @Post('/createTaskForScheduleViewing')
    public async createTaskForScheduleViewing(
        @Body('cognitoId') cognitoId: string,
        @Body('taskId') taskId: string,
        @Body('organisationId') organisationId: string,
        @Body('propertyId') propertyId: string,
        @Body('applicantId') applicantId: string,
        @Body('deadline') deadline: string,
        @Body('currentUserId') currentUserId: string,
        @Res() response: Response,
    ) {
        if (!cognitoId || !organisationId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.createTaskForScheduleViewing({
                cognitoId,
                taskId,
                organisationId,
                propertyId,
                applicantId,
                deadline,
                currentUserId,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Archive Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @ApiParam({
        name: 'taskId',
        description: 'Task ID',
        required: true,
    })
    @Put('/archiveTask/:taskId')
    public async archiveTask(
        @Param('taskId') taskId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!taskId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.archiveTask(
                request.user.sub,
                taskId,
                request.headers['authorization']?.split(' ')[1],
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_TASK(data.taskBoardId),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Unarchive Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @ApiParam({
        name: 'taskId',
        description: 'Task ID',
        required: true,
    })
    @Put('/unarchiveTask/:taskId')
    public async unarchiveTask(
        @Param('taskId') taskId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!taskId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.unarchiveTask(
                request.user.sub,
                taskId,
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_TASK(data.taskBoardId),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Update Column' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Column),
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                id: {
                    type: 'string',
                    description: 'Column ID',
                },
                name: {
                    type: 'string',
                    description: 'Column Name',
                },
                index: {
                    type: 'number',
                    description: 'Column Index',
                },
            },
        },
    })
    @Put('/updateColumn')
    public async updateColumn(
        @Body() input: Column,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!input) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.updateColumn(
                request.user.sub,
                input,
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_COLUMN(
                    data.columnBoardId,
                ),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Update Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @Put('/updateTask')
    public async updateTask(
        @Body() input: Task,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!input) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.updateTask(
                request.user.sub,
                input,
                request.headers['authorization']?.split(' ')[1],
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_TASK(data.taskBoardId),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Delete Column' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Column),
                        },
                    },
                },
            ],
        },
    })
    @ApiParam({
        name: 'columnId',
        description: 'Column ID',
        required: true,
    })
    @Delete('/deleteColumn/:columnId')
    public async deleteColumn(
        @Param('columnId') columnId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!columnId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.deleteColumn(
                request.user.sub,
                columnId,
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_COLUMN(
                    data.columnBoardId,
                ),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Delete Organisation Task Checklist' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(OrganisationTaskChecklist),
                        },
                    },
                },
            ],
        },
    })
    @ApiParam({
        name: 'organisationTaskChecklistId',
        description: 'Organisation Task Checklist ID',
        required: true,
    })
    @Delete('/deleteOrganisationTaskChecklist/:organisationTaskChecklistId')
    public async deleteOrganisationTaskChecklist(
        @Param('organisationTaskChecklistId')
        organisationTaskChecklistId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!organisationTaskChecklistId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data =
                await this.boardService.deleteOrganisationTaskChecklist(
                    request.user.sub,
                    organisationTaskChecklistId,
                );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Delete Task' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(Task),
                        },
                    },
                },
            ],
        },
    })
    @ApiParam({
        name: 'taskId',
        description: 'Task ID',
        required: true,
    })
    @Delete('/deleteTask/:taskId')
    public async deleteTask(
        @Param('taskId') taskId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!taskId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.deleteTask(
                request.user.sub,
                taskId,
                request.headers['authorization']?.split(' ')[1],
            );
            await SubscriptionUtil.send(
                SubscriptionUtil.SUBSCRIPTION.ON_CHANGE_TASK(data.taskBoardId),
                data,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Delete Task Label' })
    @ApiOkResponse({
        schema: {
            allOf: [
                {
                    $ref: getSchemaPath(BaseRes),
                },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(TaskLabel),
                        },
                    },
                },
            ],
        },
    })
    @ApiParam({
        name: 'taskLabelId',
        description: 'Task Label ID',
        required: true,
    })
    @Delete('/deleteTaskLabel/:taskLabelId')
    public async deleteTaskLabel(
        @Param('taskLabelId') taskLabelId: string,
        @Req() request: LwRequest,
        @Res() response: Response,
    ) {
        if (!taskLabelId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.boardService.deleteTaskLabel(
                request.user.sub,
                taskLabelId,
            );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }
    /**************** Mutation end ****************/
}
