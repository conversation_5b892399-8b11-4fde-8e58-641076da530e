import { Module } from '@nestjs/common';

import { AutomatedTaskRepository } from 'src/common/repository/automatedTask';
import { BoardRepository } from 'src/common/repository/board';
import { ColumnRepository } from 'src/common/repository/column';
import { OrganisationTaskChecklistRepository } from 'src/common/repository/organisationTaskChecklist';
import { ParentPropertyEntityRepository } from 'src/common/repository/parentPropertyEntity';
import { ProblemCardRepository } from 'src/common/repository/problemCard';
import { PropertyRepository } from 'src/common/repository/property';
import { TaskRepository } from 'src/common/repository/task';
import { TaskChecklistRepository } from 'src/common/repository/taskChecklist';
import { TaskCommentRepository } from 'src/common/repository/taskComment';
import { TaskLabelRepository } from 'src/common/repository/taskLabel';
import { TaskSchedulerRepository } from 'src/common/repository/taskScheduler';
import { TenancyRepository } from 'src/common/repository/tenancy';
import { UserRepository } from 'src/common/repository/user';
import { WorksOrderRepository } from 'src/common/repository/worksOrder';

import { EventsEmitterService } from 'src/common/service/eventsEmitter.service';

import { DocumentService } from 'src/document/service/document.service';

import { BoardController } from './controller/board.controller';

import { BoardService } from './service/board.service';

@Module({
    controllers: [BoardController],
    providers: [
        AutomatedTaskRepository,
        BoardRepository,
        ColumnRepository,
        OrganisationTaskChecklistRepository,
        ParentPropertyEntityRepository,
        ProblemCardRepository,
        PropertyRepository,
        TaskRepository,
        TaskChecklistRepository,
        TaskCommentRepository,
        TaskLabelRepository,
        TaskSchedulerRepository,
        TenancyRepository,
        UserRepository,
        WorksOrderRepository,
        EventsEmitterService,
        DocumentService,
        BoardService,
    ],
    exports: [],
})
export class BoardModule {}
