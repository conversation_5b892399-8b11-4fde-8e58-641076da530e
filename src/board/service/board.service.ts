import { Injectable, Logger } from '@nestjs/common';

import { v1 as uuid } from 'uuid';

import {
    createMaintenanceTaskUpdatedEvent,
    createTaskStatusUpdatedEvent,
    createTaskAssignedEvent,
} from '@rentancy-com/loftyworks-events';

import { ParentType, SortByDirection } from 'src/common/enum';
import { ColumnName } from 'src/common/enum/column';
import { ProblemCardStatus } from 'src/common/enum/problemCard';
import { TaskStatus } from 'src/common/enum/task';
import { TaskLabelName } from 'src/common/enum/taskLabel';

import { Column } from 'src/common/model/column';
import { Task } from 'src/common/model/task';
import { User } from 'src/common/model/user';

import { AutomatedTaskRepository } from 'src/common/repository/automatedTask';
import { BoardRepository } from 'src/common/repository/board';
import { ColumnRepository } from 'src/common/repository/column';
import { OrganisationTaskChecklistRepository } from 'src/common/repository/organisationTaskChecklist';
import { ParentPropertyEntityRepository } from 'src/common/repository/parentPropertyEntity';
import { ProblemCardRepository } from 'src/common/repository/problemCard';
import { PropertyRepository } from 'src/common/repository/property';
import { TaskRepository } from 'src/common/repository/task';
import { TaskChecklistRepository } from 'src/common/repository/taskChecklist';
import { TaskCommentRepository } from 'src/common/repository/taskComment';
import { TaskLabelRepository } from 'src/common/repository/taskLabel';
import { TaskSchedulerRepository } from 'src/common/repository/taskScheduler';
import { TenancyRepository } from 'src/common/repository/tenancy';
import { UserRepository } from 'src/common/repository/user';
import { WorksOrderRepository } from 'src/common/repository/worksOrder';

import { EventsEmitterService } from 'src/common/service/eventsEmitter.service';

import { ENV_CONFIG } from 'src/common/constant/config.constant';
import { checkOrganisation } from 'src/common/util/authorization';

import { DocumentService } from 'src/document/service/document.service';

import {
    BoardTaskVO,
    PropertyScheduledTasksQueryDTO,
    PropertyTasksQueryDTO,
    ScheduledTasksQueryDTO,
    TaskLabelVO,
    TaskPropertyVO,
    TaskUserVO,
} from '../model/board.model';

import { getMimeType } from '../util/board.util';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const lodash = require('lodash');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const moment = require('moment');

@Injectable()
export class BoardService {
    private logger = new Logger('BoardService', { timestamp: true });

    constructor(
        private automatedTaskRepository: AutomatedTaskRepository,
        private boardRepository: BoardRepository,
        private columnRepository: ColumnRepository,
        private organisationTaskChecklistRepository: OrganisationTaskChecklistRepository,
        private parentPropertyEntityRepository: ParentPropertyEntityRepository,
        private problemCardRepository: ProblemCardRepository,
        private propertyRepository: PropertyRepository,
        private taskRepository: TaskRepository,
        private taskChecklistRepository: TaskChecklistRepository,
        private taskCommentRepository: TaskCommentRepository,
        private taskLabelRepository: TaskLabelRepository,
        private taskSchedulerRepository: TaskSchedulerRepository,
        private tenancyRepository: TenancyRepository,
        private userRepository: UserRepository,
        private worksOrderRepository: WorksOrderRepository,
        private eventsEmitterService: EventsEmitterService,
        private documentService: DocumentService,
    ) {}

    /**************** Field start ****************/
    public async getTasksForBoard({
        boardId,
        propertyIds = [],
        userIds = [],
        deadline,
        limit,
    }) {
        this.logger.log(
            JSON.stringify({
                boardId,
                propertyIds,
                userIds,
                deadline,
                limit,
            }),
        );

        const tasks = await this.taskRepository.findByBoardId(boardId);

        let filteredTasks: Task[] = [];

        if (userIds.length > 0) {
            for (const userId of userIds) {
                if (userId !== 'unassigned') {
                    const assignedTasks = tasks.filter(
                        (task) => task.taskUserId === userId,
                    );

                    if (assignedTasks?.length > 0) {
                        filteredTasks = [...filteredTasks, ...assignedTasks];
                    }
                }
            }

            if (userIds.includes('unassigned')) {
                filteredTasks = [
                    ...filteredTasks,
                    ...tasks.filter((task) => !task.taskUserId),
                ];
            }
        } else {
            filteredTasks = tasks;
        }

        if (deadline) {
            let filterDate: any, predicate: any;

            const { le, ge } = deadline;

            if (le) {
                filterDate = moment(le).utc();
                predicate = (date1: Date, date2: Date) => {
                    return moment(date1).isSameOrBefore(moment(date2));
                };
            } else if (ge) {
                filterDate = moment(ge).utc();
                predicate = (date1: Date, date2: Date) => {
                    return moment(date1).isSameOrAfter(moment(date2));
                };
            }

            const now = moment().utc();

            filteredTasks = filteredTasks.filter((task) => {
                if (task.deadline) {
                    const taskDeadline = moment(task.deadline).utc();
                    return (
                        (filterDate.isSameOrBefore(now) ||
                            taskDeadline.isSameOrAfter(now)) &&
                        predicate(taskDeadline, filterDate)
                    );
                }
            });
        }

        if (propertyIds.length > 0) {
            filteredTasks = (
                await Promise.all(
                    filteredTasks.map(async (task) => {
                        const { parentType, parentId } = task;

                        if (
                            parentType === ParentType.PROPERTY &&
                            propertyIds.includes(parentId)
                        ) {
                            return task;
                        }

                        if (parentType === ParentType.TENANCY) {
                            const tenancy =
                                await this.tenancyRepository.findById(parentId);

                            if (
                                propertyIds.includes(tenancy.tenancyPropertyId)
                            ) {
                                return task;
                            }
                        }
                    }),
                )
            ).filter((task) => task);
        }

        if (limit > 0) {
            filteredTasks = filteredTasks.slice(0, limit);
        }

        return filteredTasks;
    }

    public async getBoardForTask(boardId: string) {
        const board = await this.boardRepository.findById(boardId);
        return board;
    }

    public async getColumnForTask(columnId: string) {
        const column = await this.columnRepository.findById(columnId);
        return column;
    }

    public async getLinkedContractorForTask(contractorUserId: string) {
        const user = await this.userRepository.findById(contractorUserId);
        return user;
    }

    public async getParentPropertyEntityForTask(
        parentPropertyEntityId: string,
    ) {
        const parentPropertyEntity =
            await this.parentPropertyEntityRepository.findById(
                parentPropertyEntityId,
            );
        return parentPropertyEntity;
    }

    public async getPropertyForTask(propertyId: string) {
        const property = await this.propertyRepository.findById(propertyId);
        return property;
    }
    /**************** Field end ****************/

    /**************** Query start ****************/
    public async getArchivedTasks(cognitoId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoId);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        const archivedTasks =
            await this.taskRepository.findByOrganisationIdAndStatus(
                user?.currentOrganisation,
                TaskStatus.ARCHIVED,
            );

        return archivedTasks;
    }

    public async getAutomatedTasks(organisationId: string) {
        const documentEvents = [];
        const propertyEvents = [];
        const tenancyEvents = [];

        const automatedTasks =
            await this.automatedTaskRepository.findByOrganisationId(
                organisationId,
            );

        for (const task of automatedTasks) {
            const { eventName } = task;

            if (eventName?.startsWith('DOCUMENT')) {
                documentEvents.push(task);
            } else if (eventName?.startsWith('PROPERTY')) {
                propertyEvents.push(task);
            } else if (eventName?.startsWith('TENANCY')) {
                tenancyEvents.push(task);
            }
        }

        return {
            documentEvents,
            propertyEvents,
            tenancyEvents,
        };
    }

    public async getBoards(cognitoId: string, organisationId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoId);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        checkOrganisation(user?.currentOrganisation, organisationId);

        const boards =
            await this.boardRepository.findByOrganisationId(organisationId);
        return boards;
    }

    public async getBoardColumns(boardId: string, propertyIds: string[] = []) {
        const boardColumns = (
            await this.columnRepository.findByBoardId(boardId)
        ).sort((column1, column2) => column1.index - column2.index);

        const boardColumnsWithTasks = await Promise.all(
            boardColumns.map(async (column) => {
                let tasks = await this.taskRepository.findByColumnId(column.id);

                if (tasks?.length > 0 && propertyIds?.length > 0) {
                    tasks = await this.filterTasksByPropertyIds(
                        tasks,
                        propertyIds,
                    );
                }
                const tasksVOs = await Promise.all(
                    tasks.map((task) => this.getBoardTaskVO(task)),
                );
                return {
                    ...column,
                    tasks: tasksVOs,
                };
            }),
        );

        return boardColumnsWithTasks;
    }

    private async getBoardTaskVO(task: Task): Promise<BoardTaskVO> {
        const taskVO = new BoardTaskVO(task);
        const { parentType, parentId, taskLabelId, taskUserId } = task;
        const promises: Promise<void>[] = [];

        // parentType
        if (parentId) {
            const parentQuery = (async () => {
                let entity: any;
                if (parentType === ParentType.PROPERTY) {
                    entity = await this.propertyRepository.findById(parentId);
                } else if (parentType === ParentType.PARENTPROPERTYENTITY) {
                    entity =
                        await this.parentPropertyEntityRepository.findById(
                            parentId,
                        );
                }

                if (entity) {
                    const vo = new TaskPropertyVO(entity);
                    if (parentType === ParentType.PROPERTY) {
                        taskVO.property = vo;
                    } else if (parentType === ParentType.PARENTPROPERTYENTITY) {
                        taskVO.parentPropertyEntity = vo;
                    }
                }
            })();
            promises.push(parentQuery);
        }

        // 处理用户查询
        if (taskUserId) {
            const userQuery = (async () => {
                const user =
                    await this.userRepository.findUserWithImage(taskUserId);
                if (user) {
                    taskVO.user = new TaskUserVO(user);
                }
            })();
            promises.push(userQuery);
        }

        // label
        if (taskLabelId) {
            const labelQuery = (async () => {
                const label =
                    await this.taskLabelRepository.findById(taskLabelId);
                if (label) {
                    taskVO.label = new TaskLabelVO(label);
                }
            })();
            promises.push(labelQuery);
        }
        await Promise.all(promises);

        return taskVO;
    }

    public async getBoardTasks(
        boardId: string,
        propertyIds: string[] = [],
        status: TaskStatus,
        assignee: string,
    ) {
        let boardTasks = await this.taskRepository.findByBoardId(
            boardId,
            status,
            assignee,
        );

        if (propertyIds?.length > 0) {
            boardTasks = await this.filterTasksByPropertyIds(
                boardTasks,
                propertyIds,
            );
        }

        return boardTasks;
    }

    public async getDashboardTaskWidget(
        cognitoId: string,
        organisationId: string,
    ) {
        const boards = await this.getBoards(cognitoId, organisationId);

        const boardId = boards?.[0]?.id;

        const [columns, tasks] = await Promise.all([
            this.columnRepository.findByBoardId(boardId),
            this.taskRepository.findByBoardId(boardId),
        ]);

        const doneColumnId = columns?.find(
            (column) => column?.name?.toUpperCase() === 'DONE',
        )?.id;

        const unfinishedTasks = tasks.filter(
            (task) => task.taskColumnId !== doneColumnId,
        );

        const unassignedTasks = tasks.filter((task) => !task.taskUserId);

        const overdueTasks = unfinishedTasks.filter((task) => {
            const { deadline } = task;
            return deadline && moment(deadline).utc().isBefore(moment().utc());
        });

        const dueTodayTasks = unfinishedTasks.filter((task) => {
            const { deadline } = task;
            return (
                deadline && moment(deadline).utc().isSame(moment().utc(), 'day')
            );
        });

        const dueWeekStartDate = moment().utc().startOf('day');
        const dueWeekEndDate = moment().utc().endOf('day').add(5, 'days');

        const dueWeekTasks = unfinishedTasks.filter((task) => {
            const { deadline } = task;

            return (
                moment(deadline).utc().isSameOrAfter(dueWeekStartDate) &&
                moment(deadline).utc().isSameOrBefore(dueWeekEndDate)
            );
        });

        return {
            totalUnfinishedTasks: unfinishedTasks?.length,
            unassignedTasks: unassignedTasks?.length,
            overdueTasks: overdueTasks?.length,
            dueTodayTasks: dueTodayTasks?.length,
            dueWeekTasks: dueWeekTasks?.length,
        };
    }

    public async getPropertyScheduledTasks({
        organisationId,
        propertyId,
        taskLabelName,
        active,
        page,
        pageSize,
    }: PropertyScheduledTasksQueryDTO) {
        this.logger.log(
            JSON.stringify({
                organisationId,
                propertyId,
                taskLabelName,
                active,
                page,
                pageSize,
            }),
        );

        let scheduledTasks =
            await this.taskSchedulerRepository.findByPropertyId(propertyId);

        if (!lodash.isNil(active)) {
            scheduledTasks = scheduledTasks.filter(
                (task) => task.active === active,
            );
        }

        if (taskLabelName) {
            const taskLabels =
                await this.taskLabelRepository.findByOrganisationId(
                    organisationId,
                );
            scheduledTasks = scheduledTasks.filter(
                (task) =>
                    taskLabels
                        .find(
                            (taskLabel) =>
                                taskLabel.id === task.taskSchedulerLabelId,
                        )
                        ?.name.toLowerCase() === taskLabelName.toLowerCase(),
            );
        }

        return scheduledTasks.slice((page - 1) * pageSize, page * pageSize);
    }

    public async getPropertyTasks({
        organisationId,
        propertyId,
        status,
        sortBy,
        page,
        pageSize,
    }: PropertyTasksQueryDTO) {
        this.logger.log(
            JSON.stringify({
                organisationId,
                propertyId,
                status,
                sortBy,
                page,
                pageSize,
            }),
        );

        let tasks =
            await this.taskRepository.findByOrganisationId(organisationId);

        tasks = await this.filterTasksByPropertyIds(tasks, [propertyId]);

        switch (status) {
            case TaskStatus.ACTIVE:
                tasks = tasks.filter(
                    (task) =>
                        ![
                            TaskStatus.ARCHIVED,
                            TaskStatus.COMPLETED,
                            TaskStatus.DELETED,
                        ].includes(task.status),
                );
                break;
            case TaskStatus.ARCHIVED:
                tasks = tasks.filter(
                    (task) => task.status === TaskStatus.ARCHIVED,
                );
                break;
            case TaskStatus.COMPLETED:
                tasks = tasks.filter(
                    (task) => task.status === TaskStatus.COMPLETED,
                );
                break;
            case TaskStatus.DELETED:
                tasks = tasks.filter(
                    (task) => task.status === TaskStatus.DELETED,
                );
                break;
            default:
                break;
        }

        if (sortBy) {
            const { field: sortByField, direction: sortByDirection } = sortBy;

            switch (sortByField) {
                case 'ASSIGNEE':
                    const users = await Promise.all(
                        tasks.map(
                            async (task) =>
                                await this.userRepository.findById(
                                    task.taskUserId,
                                ),
                        ),
                    );
                    tasks = tasks.sort((task1, task2) => {
                        const userA = users.find(
                            (user) => user.id === task1.taskUserId,
                        );
                        const userB = users.find(
                            (user) => user.id === task2.taskUserId,
                        );
                        return sortByDirection === SortByDirection.ASC
                            ? userA.companyName.localeCompare(userB.companyName)
                            : userB.companyName.localeCompare(
                                  userA.companyName,
                              );
                    });
                    break;
                case 'DEADLINE':
                    tasks = tasks.sort((taskA, taskB) =>
                        sortByDirection === SortByDirection.ASC
                            ? moment(taskA.deadline)
                                  .utc()
                                  .diff(moment(taskB.deadline).utc())
                            : moment(taskB.deadline)
                                  .utc()
                                  .diff(moment(taskA.deadline).utc()),
                    );
                    break;
                case 'STATUS': {
                    const columns = await Promise.all(
                        tasks.map(
                            async (task) =>
                                await this.columnRepository.findById(
                                    task.taskColumnId,
                                ),
                        ),
                    );
                    tasks = tasks.sort((task1, task2) => {
                        const columnA = columns.find(
                            (column) => column.id === task1.taskColumnId,
                        );
                        const columnB = columns.find(
                            (column) => column.id === task2.taskColumnId,
                        );
                        return sortByDirection === SortByDirection.ASC
                            ? columnA.index - columnB.index
                            : columnB.index - columnA.index;
                    });
                    break;
                }
                default:
                    break;
            }
        }

        return tasks.slice((page - 1) * pageSize, page * pageSize);
    }

    public async getScheduledTasks({
        organisationId,
        propertyIds = [],
        taskLabelIds = [],
        active,
        page,
        pageSize,
    }: ScheduledTasksQueryDTO) {
        this.logger.log(
            JSON.stringify({
                organisationId,
                propertyIds,
                taskLabelIds,
                active,
                page,
                pageSize,
            }),
        );

        const result = [];

        let notActiveCount = 0;
        let activeCount = 0;

        const [properties, taskLabels, taskSchedulers] = await Promise.all([
            this.propertyRepository.findByOrganisationId(organisationId),
            this.taskLabelRepository.findByOrganisationId(organisationId),
            this.taskSchedulerRepository.findByOrganisationId(organisationId),
        ]);

        const propertiesGroupById = lodash.groupBy(properties, 'id');
        const taskLabelsGroupById = lodash.groupBy(taskLabels, 'id');

        for (const scheduler of taskSchedulers) {
            if (scheduler.active) {
                activeCount++;
            } else {
                notActiveCount++;
            }

            const {
                id,
                taskSchedulerLabelId,
                taskSchedulerPropertyId,
                frequency,
            } = scheduler;

            if (
                scheduler.active == active &&
                (propertyIds?.length === 0 ||
                    propertyIds?.includes(taskSchedulerPropertyId)) &&
                (taskLabelIds?.length === 0 ||
                    taskLabelIds?.includes(taskSchedulerLabelId))
            ) {
                result.push({
                    id,
                    property:
                        propertiesGroupById[taskSchedulerPropertyId]?.[0]
                            ?.addressLine1 || 'ARCHIVED PROPERTY',
                    taskLabel:
                        taskLabelsGroupById[taskSchedulerLabelId]?.[0]?.name ||
                        'UNKNOWN LABEL',
                    frequency,
                    active: scheduler.active,
                });
            }
        }

        return {
            active: activeCount,
            notActive: notActiveCount,
            total: result.length,
            items: result.slice((page - 1) * pageSize, page * pageSize),
        };
    }

    public async getTaskLabels(organisationId: string) {
        const taskLabels =
            await this.taskLabelRepository.findByOrganisationId(organisationId);
        return taskLabels;
    }

    public async searchOrganisationTaskChecklists(
        cognitoId: string,
        search: string,
    ) {
        const user = await this.userRepository.findByCognitoId(cognitoId);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        const organisationTaskChecklist =
            await this.organisationTaskChecklistRepository.findByOrganisationIdAndSearch(
                user?.currentOrganisation,
                search,
            );
        return organisationTaskChecklist;
    }
    /**************** Query end ****************/

    /**************** Mutation start ****************/
    public async createColumn(cognitoId: string, input: Column) {
        this.logger.log(JSON.stringify(input));

        const user = await this.userRepository.findByCognitoId(cognitoId);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        const { columnBoardId, name, description } = input;

        const board = await this.boardRepository.findById(columnBoardId);

        if (!board) {
            this.logger.error(
                `The board is not found, boardId=${columnBoardId}.`,
            );
            throw new Error(
                `The board is not found, boardId=${columnBoardId}.`,
            );
        }

        checkOrganisation(user.currentOrganisation, board.boardOrganisationId);

        const boardColumnCount = board.columnCount ? board.columnCount + 1 : 1;

        const column: any = {
            id: uuid(),
            name,
            index: boardColumnCount - 1,
            columnBoardId,
            columnOrganisationId: user.currentOrganisation,
            taskCount: 0,
            owner: user.id,
        };

        if (description) {
            column.description = description;
        }

        await this.boardRepository.transactCreateColumn(column, {
            id: columnBoardId,
            columnCount: boardColumnCount,
        });

        return await this.columnRepository.findById(column.id);
    }

    public async createTask(cognitoId: string, input: Task, token?: string) {
        this.logger.log(JSON.stringify(input));

        const user = await this.userRepository.findByCognitoId(cognitoId);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        const {
            deadline,
            description,
            images,
            name,
            note,
            parentId,
            parentType,
            shareToExternalCalendar,
            status,
            taskBoardId,
            taskColumnId,
            taskLabelId,
            taskProblemCardId,
            taskUserId,
            time,
            type,
        } = input;

        const column = await this.columnRepository.findById(taskColumnId);

        if (!column) {
            this.logger.error(
                `The column is not found, columnId=${taskColumnId}.`,
            );
            throw new Error(
                `The column is not found, columnId=${taskColumnId}.`,
            );
        }

        const columnTaskCount = column.taskCount ? column.taskCount + 1 : 1;

        const board = await this.boardRepository.findById(column.columnBoardId);

        if (!board) {
            this.logger.error(
                `The board is not found, boardId=${column.columnBoardId}.`,
            );
            throw new Error(
                `The board is not found, boardId=${column.columnBoardId}.`,
            );
        }

        const boardTaskCount = board.taskCount ? board.taskCount + 1 : 1;
        const boardTaskRefId = board.taskRefId
            ? Number.parseInt(
                  board.taskRefId.substring(
                      board.taskRefId.lastIndexOf('-') + 1,
                  ),
              ) + 1
            : 1;

        checkOrganisation(user.currentOrganisation, board.boardOrganisationId);

        const task: any = {
            id: uuid(),
            index: columnTaskCount - 1,
            mutator: user.cognitoId,
            name,
            owner: user.id,
            referenceId: `T-${boardTaskRefId}`,
            taskBoardId,
            taskColumnId,
            taskOrganisationId: user.currentOrganisation,
            taskProblemCardId,
            taskUserId: taskUserId ? taskUserId : undefined,
        };

        if (deadline) {
            task.deadline = deadline;
        }

        if (description) {
            task.description = description;
        }

        if (note) {
            task.note = note;
        }

        if (parentType && parentId) {
            const parentName = await this.getParentName(parentType, parentId);
            if (parentName) {
                task.parentId = parentId;
                task.parentName = parentName;
                task.parentType = parentType;
            }
        }

        if (!lodash.isNil(shareToExternalCalendar)) {
            task.shareToExternalCalendar = shareToExternalCalendar;
        }

        if (status) {
            task.status = status;
        }

        if (taskLabelId) {
            task.taskLabelId = taskLabelId;
        }

        if (!lodash.isNil(time)) {
            task.time = time;
        }

        if (type) {
            task.type = type;
        }

        await this.boardRepository.transactCreateTask(
            task,
            {
                id: taskBoardId,
                taskCount: boardTaskCount,
                taskRefId: `T-${boardTaskRefId}`,
            },
            {
                id: taskColumnId,
                taskCount: columnTaskCount,
            },
        );

        const createdTask = await this.taskRepository.findById(task.id);

        const domainEvents = [];

        if (createdTask.taskUserId) {
            const taskAssignedEvent = createTaskAssignedEvent({
                subjectId: createdTask.id,
                targetId: createdTask.taskUserId,
                organisationId: createdTask.taskOrganisationId,
                name: createdTask.name,
                actorId: user.id,
            });

            domainEvents.push(taskAssignedEvent);
        }

        if (
            createdTask.taskLabelId &&
            createdTask.taskProblemCardId &&
            createdTask.parentType === ParentType.PROPERTY
        ) {
            const maintenanceUpdatedEvent = createMaintenanceTaskUpdatedEvent({
                targetId: createdTask.id,
                propertyId: createdTask.parentId,
                organisationId: user.currentOrganisation,
                actorId: user.id,
            });

            domainEvents.push(maintenanceUpdatedEvent);
        }

        await this.eventsEmitterService.putEvents(domainEvents);

        if (images?.length > 0) {
            await Promise.all(
                images.map((image) => {
                    const document: any = {
                        key: image,
                        imageTaskId: createdTask.id,
                        imageOrganisationId: user.currentOrganisation,
                        type: 'PHOTO',
                    };

                    document.name = image.split('/').pop() || '';
                    const extname = image.split('.').pop();
                    document.mimeType = getMimeType(extname);

                    return this.documentService.createDocument(document);
                }),
            );
        }

        if (shareToExternalCalendar && token) {
            await this.addTaskEventToCalendar(createdTask, token);
        }

        return createdTask;
    }

    public async createTaskForRightmove(cognitoId: string, input: any) {
        this.logger.log(JSON.stringify(input));

        const { propertyId, type } = input;

        const property = await this.propertyRepository.findById(propertyId);

        if (!property) {
            this.logger.error(
                `The property is not found, propertyId=${propertyId}.`,
            );
            throw new Error(
                `The property is not found, propertyId=${propertyId}.`,
            );
        }

        const propertyOrganisationId = property.propertyOrganisationId;

        const boards = this.getBoards(cognitoId, propertyOrganisationId);

        const boardId = boards?.[0]?.id;

        const generalTaskLabel = (
            await this.taskLabelRepository.findByOrganisationId(
                propertyOrganisationId,
            )
        )?.filter((taskLabel) => taskLabel.name === TaskLabelName.GENERAL)?.[0];

        const doneColumn = (
            await this.columnRepository.findByBoardId(boardId)
        )?.filter((column) => column.name === ColumnName.DONE)?.[0];

        const task: any = {
            taskBoardId: boardId,
            taskColumnId: doneColumn?.id,
            taskLabelId: generalTaskLabel?.id,
            parentId: propertyId,
            parentType: ParentType.PROPERTY,
            deadline: moment().utc().startOf('days').toISOString(),
            time: false,
        };

        if (type === 'REMOVE') {
            task.name = 'Property Removed from RightMove';
        } else if (type === 'SEND') {
            task.name = 'Property Posted to RightMove';
        }

        return await this.createTask(cognitoId, task);
    }

    public async createTaskForScheduleViewing(input: any) {
        const {
            cognitoId,
            taskId,
            organisationId,
            propertyId,
            applicantId,
            deadline,
            currentUserId,
        } = input;

        if (taskId) {
            await this.taskRepository.deleteById(taskId);
        }

        const boards = this.getBoards(cognitoId, organisationId);

        const boardId = boards?.[0]?.id;

        const appointmentTaskLabel = (
            await this.taskLabelRepository.findByOrganisationId(organisationId)
        )?.filter(
            (taskLabel) => taskLabel.name === TaskLabelName.APPOINTMENT,
        )?.[0];

        const todoColumn = (
            await this.columnRepository.findByBoardId(boardId)
        )?.filter((column) => column.name === ColumnName.TO_DO)?.[0];

        const task: any = {
            taskBoardId: boardId,
            taskColumnId: todoColumn?.id,
            taskLabelId: appointmentTaskLabel?.id,
            taskUserId: currentUserId,
            parentId: propertyId,
            parentType: ParentType.PROPERTY,
            deadline,
            name: 'ScheduleViewing',
            time: true,
        };

        const applicant = await this.userRepository.findById(applicantId);

        if (applicant) {
            task.description = `Applicant: ${applicant.fname} ${applicant.sname}`;
        }

        const createdTask = await this.createTask(cognitoId, task);

        return createdTask;
    }

    public async archiveTask(cognitoId: string, taskId: string, token: string) {
        return this.archiveOrDeleteTask(
            cognitoId,
            taskId,
            TaskStatus.ARCHIVED,
            token,
        );
    }

    public async unarchiveTask(cognitoId: string, taskId: string) {
        const [user, task] = await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.taskRepository.findById(taskId),
        ]);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!task) {
            this.logger.error(`The task is not found, taskId=${taskId}.`);
            throw new Error(`The task is not found, taskId=${taskId}.`);
        }

        const {
            name,
            description,
            taskBoardId,
            taskColumnId,
            taskLabelId,
            taskOrganisationId,
            taskUserId,
            parentType,
            parentId,
            deadline,
            time,
        } = task;

        checkOrganisation(user?.currentOrganisation, taskOrganisationId);

        const input: any = {
            name,
            description,
            taskBoardId,
            taskColumnId,
            taskLabelId,
            taskUserId,
            parentType,
            parentId,
            deadline,
            time,
        };

        const column = await this.columnRepository.findById(taskColumnId);

        if (!column) {
            const columns =
                await this.columnRepository.findByBoardId(taskBoardId);
            input.taskColumnId = columns?.[0]?.id;
        }

        const newTask = await this.createTask(cognitoId, input);

        await this.taskRepository.deleteById(taskId);

        const [documents, taskChecklists, taskComments, worksOrders] =
            await Promise.all([
                this.documentService.getTaskDocuments(taskId),
                this.taskChecklistRepository.findByTaskId(taskId),
                this.taskCommentRepository.findByTaskId(taskId),
                this.worksOrderRepository.findByTaskId(taskId),
            ]);

        const updateTaskDependencies = [
            ...documents.map((document) => {
                return this.documentService.updateDocument({
                    ...document,
                    id: document.id,
                    documentTaskId: newTask.id,
                });
            }),
            ...taskChecklists.map((taskChecklist) => {
                return this.taskChecklistRepository.updateItem(
                    taskChecklist.id,
                    {
                        taskChecklistTaskId: newTask.id,
                    },
                );
            }),
            ...taskComments.map((taskComment) => {
                return this.taskCommentRepository.updateItem(taskComment.id, {
                    taskCommentTaskId: newTask.id,
                });
            }),
            ...worksOrders.map((worksOrder) => {
                return this.worksOrderRepository.updateItem(worksOrder.id, {
                    worksOrderTaskId: newTask.id,
                });
            }),
        ];

        await Promise.all(updateTaskDependencies);

        return newTask;
    }

    public async updateColumn(cognitoId: string, input: Column) {
        const { id, name, index = -1 } = input;

        const [user, column] = await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.columnRepository.findById(id),
        ]);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!column) {
            this.logger.error(`The column is not found, columnId=${id}.`);
            throw new Error(`The column is not found, columnId=${id}.`);
        }

        checkOrganisation(
            user?.currentOrganisation,
            column?.columnOrganisationId,
        );

        const update: any = {};

        if (index > -1 && column.index !== index) {
            const filter =
                column.index < index
                    ? (c: Column) => c.index <= index && c.index > column.index
                    : (c: Column) => c.index >= index && c.index < column.index;

            const updater =
                column.index < index
                    ? (c: Column) =>
                          this.columnRepository.updateItem(c.id, {
                              index: c.index - 1,
                          })
                    : (c: Column) =>
                          this.columnRepository.updateItem(c.id, {
                              index: c.index + 1,
                          });

            const columns = (
                await this.columnRepository.findByBoardId(column.columnBoardId)
            )
                .filter((c: Column) => c.id !== id)
                .filter(filter);

            await Promise.all(columns.map(updater));

            update.index = index;
        }

        if (name) {
            update.name = name;
        }

        if (index > -1 || name) {
            await this.columnRepository.updateItem(id, update);
            return await this.columnRepository.findById(id);
        }

        return column;
    }

    public async updateTask(cognitoId: string, input: Task, token: string) {
        this.logger.log(JSON.stringify(input));

        const { id, description, shareToExternalCalendar, taskUserId } = input;

        const [user, task] = await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.taskRepository.findById(id),
        ]);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!task) {
            this.logger.error(`The task is not found, taskId=${id}.`);
            throw new Error(`The task is not found, taskId=${id}.`);
        }

        let updated = false;

        if (task.status !== TaskStatus.DELETED) {
            const updateItems = await this.buildTaskUpdateItems(
                user,
                input,
                task,
            );

            if (lodash.keys(updateItems).length > 0) {
                await this.taskRepository.updateItem(id, updateItems);
                updated = true;
            }
        }

        if (lodash.isNil(description)) {
            await this.taskRepository.removeFieldsById(id, ['description']);
            updated = true;
        }

        if (lodash.isNil(taskUserId)) {
            await this.taskRepository.removeFieldsById(id, ['taskUserId']);
            updated = true;
        }

        const updatedTask = updated
            ? await this.taskRepository.findById(id)
            : null;

        if (updatedTask) {
            const domainEvents = [];

            if (updatedTask.index !== task.index) {
                const taskStatusUpdatedEvent = createTaskStatusUpdatedEvent({
                    subjectId: task.id,
                    targetId: updatedTask.taskUserId,
                    organisationId: updatedTask.taskOrganisationId,
                    name: updatedTask.name,
                    actorId: user.id,
                });
                domainEvents.push(taskStatusUpdatedEvent);
            }

            if (updatedTask.taskUserId && !task.taskUserId) {
                const taskAssignedEvent = createTaskAssignedEvent({
                    subjectId: updatedTask.id,
                    targetId: updatedTask.taskUserId,
                    organisationId: updatedTask.taskOrganisationId,
                    name: updatedTask.name,
                    actorId: user.id,
                });

                domainEvents.push(taskAssignedEvent);
            }

            if (
                updatedTask.taskProblemCardId &&
                updatedTask.parentType === ParentType.PROPERTY
            ) {
                const maintenanceUpdatedEvent =
                    createMaintenanceTaskUpdatedEvent({
                        targetId: updatedTask.id,
                        propertyId: updatedTask.parentId,
                        organisationId: updatedTask.taskOrganisationId,
                        actorId: user.id,
                    });
                domainEvents.push(maintenanceUpdatedEvent);
            }

            await this.eventsEmitterService.putEvents(domainEvents);

            if (shareToExternalCalendar && token) {
                await this.addTaskEventToCalendar(updatedTask, token);
            }
        }

        return await this.taskRepository.findById(id);
    }

    public async deleteColumn(cognitoId: string, columnId: string) {
        const [user, column] = await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.columnRepository.findById(columnId),
        ]);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!column) {
            this.logger.error(`The column is not found, columnId=${columnId}.`);
            throw new Error(`The column is not found, columnId=${columnId}.`);
        }

        const currentOrganisationId = user?.currentOrganisation;

        const { columnBoardId, columnOrganisationId, index, taskCount } =
            column;

        checkOrganisation(currentOrganisationId, columnOrganisationId);

        if (taskCount > 0) {
            this.logger.error(
                `Unable to delete column with tasks: ${columnId}`,
            );
            throw new Error(`Unable to delete column with tasks: ${columnId}`);
        }

        const [board, columns] = await Promise.all([
            this.boardRepository.findById(columnBoardId),
            this.columnRepository
                .findByBoardId(columnBoardId)
                .then((columns) => {
                    return columns.filter((column) => column.index > index);
                }),
        ]);

        await this.boardRepository.updateItem(columnBoardId, {
            columnCount: board.columnCount - 1,
        });

        await Promise.all(
            columns.map((column) => {
                return this.columnRepository.updateItem(column.id, {
                    index: column.index - 1,
                });
            }),
        );

        await this.columnRepository.deleteById(columnId);

        return column;
    }

    public async deleteOrganisationTaskChecklist(
        cognitoId: string,
        organisationTaskChecklistId: string,
    ) {
        const [user, organisationTaskChecklist] = await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.organisationTaskChecklistRepository.findById(
                organisationTaskChecklistId,
            ),
        ]);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!organisationTaskChecklist) {
            this.logger.error(
                `The organisation task checklist is not found, organisationTaskChecklistId=${organisationTaskChecklistId}.`,
            );
            throw new Error(
                `The organisation task checklist is not found, organisationTaskChecklistId=${organisationTaskChecklistId}.`,
            );
        }

        checkOrganisation(
            user?.currentOrganisation,
            organisationTaskChecklist?.organisationTaskChecklistOrganisationId,
        );

        const taskLabels = await this.taskLabelRepository.findByOrganisationId(
            user?.currentOrganisation,
        );

        await Promise.all(
            taskLabels.map(async (taskLabel) => {
                const { id, organisationTaskChecklistIds } = taskLabel;

                if (
                    organisationTaskChecklistIds.includes(
                        organisationTaskChecklistId,
                    )
                ) {
                    return await this.taskLabelRepository.updateItem(id, {
                        organisationTaskChecklistIds:
                            organisationTaskChecklistIds.filter(
                                (id) => id !== organisationTaskChecklistId,
                            ),
                    });
                }
            }),
        );

        await this.organisationTaskChecklistRepository.deleteById(
            organisationTaskChecklistId,
        );

        return organisationTaskChecklist;
    }

    public async deleteTask(cognitoId: string, taskId: string, token: string) {
        return await this.archiveOrDeleteTask(
            cognitoId,
            taskId,
            TaskStatus.DELETED,
            token,
        );
    }

    public async deleteTaskLabel(cognitoId: string, taskLabelId: string) {
        const [user, taskLabel] = await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.taskLabelRepository.findById(taskLabelId),
        ]);

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!taskLabel) {
            this.logger.error(
                `The task label is not found, taskLabelId=${taskLabelId}.`,
            );
            throw new Error(
                `The task label is not found, taskLabelId=${taskLabelId}.`,
            );
        }

        checkOrganisation(
            user?.currentOrganisation,
            taskLabel?.taskLabelOrganisationId,
        );

        const [automatedTasks, scheduledTasks] = await Promise.all([
            this.automatedTaskRepository.findByOrganisationId(
                user?.currentOrganisation,
            ),
            this.taskSchedulerRepository.findByOrganisationId(
                user?.currentOrganisation,
            ),
        ]);

        await Promise.all([
            ...automatedTasks.map(async (automatedTask) => {
                const { id, automatedTasksLabelId } = automatedTask;
                if (automatedTasksLabelId === taskLabelId) {
                    return this.automatedTaskRepository.removeFieldsById(id, [
                        'automatedTasksLabelId',
                    ]);
                }
            }),
            ...scheduledTasks.map(async (scheduledTask) => {
                const { id, taskSchedulerLabelId } = scheduledTask;

                if (taskSchedulerLabelId === taskLabelId) {
                    return this.taskSchedulerRepository.removeFieldsById(id, [
                        'taskSchedulerLabelId',
                    ]);
                }
            }),
        ]);

        await this.taskLabelRepository.deleteById(taskLabelId);

        return taskLabel;
    }
    /**************** Mutation end ****************/

    private async filterTasksByPropertyIds(
        tasks: Task[],
        propertyIds: string[],
    ) {
        const promises = tasks.map(async (task) => {
            const { parentType, parentId } = task;

            if (
                [ParentType.PARENTPROPERTYENTITY, ParentType.PROPERTY].includes(
                    parentType,
                ) &&
                propertyIds.includes(parentId)
            ) {
                return task;
            }

            if (parentType === ParentType.TENANCY) {
                const tenancy = await this.tenancyRepository.findById(parentId);

                if (propertyIds.includes(tenancy.tenancyPropertyId)) {
                    return task;
                }
            }
        });

        return (await Promise.all(promises)).filter((task) => task);
    }

    private async archiveOrDeleteTask(
        cognitoId: string,
        taskId: string,
        status: TaskStatus,
        token: string,
    ) {
        const [user, task] = (await Promise.all([
            this.userRepository.findByCognitoId(cognitoId),
            this.taskRepository.findById(taskId),
        ])) as [User, Task];

        if (!user) {
            this.logger.error(`The user is not found, cognitoId=${cognitoId}.`);
            throw new Error(`The user is not found, cognitoId=${cognitoId}.`);
        }

        if (!task) {
            this.logger.error(`The task is not found, taskId=${taskId}.`);
            throw new Error(`The task is not found, taskId=${taskId}.`);
        }

        const { index, taskColumnId, taskUserId, owner } = task;

        const hasPermission =
            owner === user?.id || (!owner && taskUserId === user?.id);

        if (token && !hasPermission) {
            this.logger.error(
                `This task is not created by you, you can't archive or delete it!`,
            );
            throw new Error(
                `This task is not created by you, you can't archive or delete it!`,
            );
        }

        const [column, tasks] = await Promise.all([
            this.columnRepository.findById(taskColumnId),
            this.taskRepository.findByColumnId(taskColumnId).then((tasks) => {
                return tasks.filter((task) => task.index > index);
            }),
        ]);

        const board = await this.boardRepository.findById(column.columnBoardId);

        checkOrganisation(
            user?.currentOrganisation,
            board?.boardOrganisationId,
        );

        await Promise.all([
            this.boardRepository.updateItem(board.id, {
                taskCount: board.taskCount - 1,
            }),
            this.columnRepository.updateItem(column.id, {
                taskCount: column.taskCount - 1,
            }),
            ...tasks.map((task) => {
                return this.taskRepository.updateItem(task.id, {
                    index: task.index - 1,
                });
            }),
            this.taskRepository.updateItem(taskId, {
                mutator: cognitoId,
                status,
            }),
        ]);

        await this.addTaskEventToCalendar(task, token);

        return task;
    }

    private async addTaskEventToCalendar(task: Task, token: string) {
        const response = await fetch(
            `${ENV_CONFIG.CORE_API_URL}/applicantScheduleViewing/syncTask?taskId=${task.id}`,
            {
                method: 'GET',
                headers: {
                    authorization: `Bearer ${token}`,
                },
                redirect: 'follow',
            },
        );

        return response.json();
    }

    private async getParentName(parentType: ParentType, parentId: string) {
        switch (parentType) {
            case ParentType.PARENTPROPERTYENTITY: {
                const parentPropertyEntity =
                    await this.parentPropertyEntityRepository.findById(
                        parentId,
                    );
                return parentPropertyEntity.name;
            }
            case ParentType.PROPERTY: {
                const property =
                    await this.propertyRepository.findById(parentId);
                return property.addressLine1;
            }
            case ParentType.TENANCY: {
                const tenancy = await this.tenancyRepository.findById(parentId);
                const property = await this.propertyRepository.findById(
                    tenancy.tenancyPropertyId,
                );
                return property.addressLine1;
            }
            default:
                return null;
        }
    }

    private async buildTaskUpdateItems(user: User, input: Task, task: Task) {
        const oldTaskColumn = await this.columnRepository.findById(
            task.taskColumnId,
        );

        checkOrganisation(
            user?.currentOrganisation,
            oldTaskColumn?.columnOrganisationId,
        );

        const {
            deadline,
            description,
            disabled,
            index = -1,
            linkedContractor,
            name,
            note,
            parentId,
            parentType,
            shareToExternalCalendar,
            status,
            taskColumnId,
            taskLabelId,
            taskUserId,
            time,
            type,
        } = input;

        const updateItems: any = {
            mutator: user.id,
        };

        if (deadline) {
            updateItems.deadline = deadline;
        }

        if (description) {
            updateItems.description = description;
        }

        if (!lodash.isNil(disabled)) {
            updateItems.disabled = disabled;
        }

        if (index > -1) {
            if (taskColumnId && taskColumnId !== oldTaskColumn.id) {
                await this.updateTaskIndexesAndColumnTaskCount(
                    input,
                    oldTaskColumn,
                );
                updateItems.taskColumnId = taskColumnId;
            } else {
                await this.updateTaskIndexes(input, task);
            }

            updateItems.index = index;
        }

        if (linkedContractor) {
            updateItems.linkedContractor = linkedContractor;
        }

        if (name) {
            updateItems.name = name;
        }

        if (note) {
            updateItems.note = note;
        }

        if (parentType && parentId) {
            const parentName = await this.getParentName(parentType, parentId);
            if (parentName) {
                updateItems.parentType = parentType;
                updateItems.parentId = parentId;
                updateItems.parentName = parentName;
            }
        }

        if (!lodash.isNil(shareToExternalCalendar)) {
            updateItems.shareToExternalCalendar = shareToExternalCalendar;
        }

        if (status) {
            updateItems.status = status;
        }

        if (taskLabelId) {
            updateItems.taskLabelId = taskLabelId;
        }

        if (taskUserId) {
            updateItems.taskUserId = taskUserId;
        }

        if (!lodash.isNil(time)) {
            updateItems.time = time;
        }

        if (type) {
            updateItems.type = type;
        }

        await this.updateProblemCard(input, task, oldTaskColumn);

        return updateItems;
    }

    private async updateTaskIndexesAndColumnTaskCount(
        newTask: Task,
        oldColumn: Column,
    ) {
        const oldColumnTasks = (
            await this.taskRepository.findByColumnId(oldColumn.id)
        ).filter((task) => task.index > newTask.index);

        const newColumn = await this.columnRepository.findById(
            newTask.taskColumnId,
        );

        const newColumnTasks = (
            await this.taskRepository.findByColumnId(newTask.taskColumnId)
        )?.filter((task) => task.index > newTask.index);

        await Promise.all([
            ...oldColumnTasks.map((task) => {
                return this.taskRepository.updateItem(task.id, {
                    index: task.index - 1,
                });
            }),
            ...newColumnTasks.map((task) => {
                return this.taskRepository.updateItem(task.id, {
                    index: task.index + 1,
                });
            }),
            this.columnRepository.updateItem(oldColumn.id, {
                taskCount: oldColumn.taskCount - 1,
            }),
            this.columnRepository.updateItem(newColumn.id, {
                taskCount: newColumn.taskCount + 1,
            }),
        ]);
    }

    private async updateTaskIndexes(newTask: Task, oldTask: Task) {
        const filter =
            oldTask.index < newTask.index
                ? (t: Task) =>
                      t.index <= newTask.index && t.index > oldTask.index
                : (t: Task) =>
                      t.index >= newTask.index && t.index < oldTask.index;

        const updater =
            oldTask.index < newTask.index
                ? (t: Task) =>
                      this.taskRepository.updateItem(t.id, {
                          index: t.index - 1,
                      })
                : (t: Task) =>
                      this.taskRepository.updateItem(t.id, {
                          index: t.index + 1,
                      });

        const tasks = (
            await this.taskRepository.findByColumnId(oldTask.taskColumnId)
        )
            .filter((t: Task) => t.id !== oldTask.id)
            .filter(filter);

        await Promise.all(tasks.map(updater));
    }

    private async updateProblemCard(
        newTask: Task,
        oldTask: Task,
        oldColumn: Column,
    ) {
        if (!oldTask.taskProblemCardId) {
            return;
        }

        if (newTask.taskColumnId) {
            const columns = (
                await this.columnRepository.findByBoardId(oldTask.taskBoardId)
            ).sort((a, b) => a.index - b.index);

            const lastColumnId = columns[columns.length - 1].id;

            if (
                newTask.taskColumnId === lastColumnId &&
                oldColumn.id !== lastColumnId
            ) {
                await this.problemCardRepository.updateItem(
                    oldTask.taskProblemCardId,
                    {
                        status: ProblemCardStatus.RESOLVED,
                    },
                );
            } else if (
                newTask.taskColumnId !== lastColumnId &&
                oldColumn.id === lastColumnId
            ) {
                await this.problemCardRepository.updateItem(
                    oldTask.taskProblemCardId,
                    {
                        status: ProblemCardStatus.TODO,
                    },
                );
            }
        }
    }
}
