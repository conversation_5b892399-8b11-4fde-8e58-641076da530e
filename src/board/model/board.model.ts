import { ApiProperty } from '@nestjs/swagger';

import { SortBy } from 'src/common/model';
import { TaskStatus } from 'src/common/enum/task';
import { TaskLabelName } from 'src/common/enum/taskLabel';
import { Property } from '../../common/model/property';
import { User } from '../../common/model/user';
import { File } from '../../common/model/file';
import { TaskLabel } from '../../common/model/taskLabel';
import { Task } from '../../common/model/task';

export class PropertyScheduledTasksQueryDTO {
    @ApiProperty({
        required: true,
    })
    propertyId: string;

    @ApiProperty({
        required: false,
    })
    organisationId: string;

    @ApiProperty({
        enum: TaskLabelName,
        required: false,
    })
    taskLabelName: TaskLabelName;

    @ApiProperty({
        required: false,
    })
    active: boolean;

    @ApiProperty({
        required: true,
    })
    page: number;

    @ApiProperty({
        required: true,
    })
    pageSize: number;
}

export class PropertyTasksQueryDTO {
    @ApiProperty({
        required: true,
    })
    propertyId: string;

    @ApiProperty({
        required: true,
    })
    organisationId: string;

    @ApiProperty({
        required: false,
        enum: TaskStatus,
    })
    status: TaskStatus;

    @ApiProperty({
        required: false,
        type: SortBy,
    })
    sortBy: SortBy;

    @ApiProperty({
        required: false,
    })
    page: number;

    @ApiProperty({
        required: false,
    })
    pageSize: number;
}

export class ScheduledTasksQueryDTO {
    @ApiProperty({
        required: true,
    })
    organisationId: string;

    @ApiProperty({
        required: false,
    })
    propertyIds: string[];

    @ApiProperty({
        required: false,
    })
    taskLabelIds: string[];

    @ApiProperty({
        required: false,
    })
    active: boolean;

    @ApiProperty({
        required: true,
    })
    page: number;

    @ApiProperty({
        required: true,
    })
    pageSize: number;
}

export class TaskPropertyVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    addressLine1: string;
    constructor(property: any) {
        this.id = property.id;
        this.addressLine1 = property.addressLine1;
    }
}

export class UserImageVO {
    @ApiProperty()
    key: string;
    constructor(image: File) {
        this.key = image.key;
    }
}

export class TaskUserVO {
    @ApiProperty()
    fname: string;
    @ApiProperty()
    sname: string;
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    image: UserImageVO;
    constructor(user: User) {
        this.fname = user.fname;
        this.sname = user.sname;
        this.id = user.id;
        this.image = user.image ? new UserImageVO(user.image) : null;
    }
}

export class TaskLabelVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    constructor(label: TaskLabel) {
        this.id = label.id;
        this.name = label.name;
    }
}

export class BoardTaskVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    referenceId: string;
    @ApiProperty()
    index: number;
    @ApiProperty()
    name: string;
    @ApiProperty()
    deadline: Date;
    @ApiProperty()
    parentPropertyEntity: TaskPropertyVO;
    @ApiProperty()
    property: TaskPropertyVO;
    @ApiProperty()
    user: TaskUserVO;
    @ApiProperty()
    label: TaskLabelVO;
    constructor(task: Task) {
        this.id = task.id;
        this.referenceId = task.referenceId;
        this.index = task.index;
        this.name = task.name;
        this.deadline = task.deadline;
    }
}
