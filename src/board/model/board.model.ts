import { ApiProperty } from '@nestjs/swagger';

import { SortBy } from 'src/common/model';
import { TaskStatus, TaskType } from 'src/common/enum/task';
import { TaskLabelName } from 'src/common/enum/taskLabel';
import { Property } from '../../common/model/property';
import { User } from '../../common/model/user';
import { File } from '../../common/model/file';
import { TaskLabel } from '../../common/model/taskLabel';
import { Task } from '../../common/model/task';
import { Board } from '../../common/model/board';
import { Organisation } from '../../common/model/organisation';
import { Column } from '../../common/model/column';
import { AutomatedTask } from '../../common/model/automatedTask';

export class PropertyScheduledTasksQueryDTO {
    @ApiProperty({
        required: true,
    })
    propertyId: string;

    @ApiProperty({
        required: false,
    })
    organisationId: string;

    @ApiProperty({
        enum: TaskLabelName,
        required: false,
    })
    taskLabelName: TaskLabelName;

    @ApiProperty({
        required: false,
    })
    active: boolean;

    @ApiProperty({
        required: true,
    })
    page: number;

    @ApiProperty({
        required: true,
    })
    pageSize: number;
}

export class PropertyTasksQueryDTO {
    @ApiProperty({
        required: true,
    })
    propertyId: string;

    @ApiProperty({
        required: true,
    })
    organisationId: string;

    @ApiProperty({
        required: false,
        enum: TaskStatus,
    })
    status: TaskStatus;

    @ApiProperty({
        required: false,
        type: SortBy,
    })
    sortBy: SortBy;

    @ApiProperty({
        required: false,
    })
    page: number;

    @ApiProperty({
        required: false,
    })
    pageSize: number;
}

export class ScheduledTasksQueryDTO {
    @ApiProperty({
        required: true,
    })
    organisationId: string;

    @ApiProperty({
        required: false,
    })
    propertyIds: string[];

    @ApiProperty({
        required: false,
    })
    taskLabelIds: string[];

    @ApiProperty({
        required: false,
    })
    active: boolean;

    @ApiProperty({
        required: true,
    })
    page: number;

    @ApiProperty({
        required: true,
    })
    pageSize: number;
}

export class TaskPropertyVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    addressLine1: string;
    constructor(property: any) {
        this.id = property.id;
        this.addressLine1 = property.addressLine1;
    }
}

export class UserImageVO {
    @ApiProperty()
    key: string;
    constructor(image: File) {
        this.key = image.key;
    }
}

export class TaskUserVO {
    @ApiProperty()
    fname: string;
    @ApiProperty()
    sname: string;
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    image: UserImageVO;
    constructor(user: User) {
        this.fname = user.fname;
        this.sname = user.sname;
        this.id = user.id;
        this.image = user.image ? new UserImageVO(user.image) : null;
    }
}

export class TaskLabelVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    iconName: string;
    constructor(label: TaskLabel) {
        this.id = label.id;
        this.name = label.name;
        this.iconName = label.iconName;
    }
}

export class ColumnTaskVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    referenceId: string;
    @ApiProperty()
    index: number;
    @ApiProperty()
    name: string;
    @ApiProperty()
    deadline: Date;
    @ApiProperty()
    parentPropertyEntity: TaskPropertyVO;
    @ApiProperty()
    property: TaskPropertyVO;
    @ApiProperty()
    user: TaskUserVO;
    @ApiProperty()
    label: TaskLabelVO;
    constructor(task: Task) {
        this.id = task.id;
        this.referenceId = task.referenceId;
        this.index = task.index;
        this.name = task.name;
        this.deadline = task.deadline;
    }
}
export class TaskBoardVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    columnCount: number;
    constructor(board: Board) {
        this.id = board.id;
        this.columnCount = board.columnCount;
    }
}

export class TaskOrganisationVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    constructor(organisation: Organisation) {
        this.id = organisation.id;
        this.name = organisation.name;
    }
}

export class TaskColumnVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    index: number;
    constructor(column: Column) {
        this.id = column.id;
        this.name = column.name;
        this.index = column.index;
    }
}

export class BoardTaskVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    type: TaskType;
    @ApiProperty()
    deadline: Date;
    @ApiProperty()
    label: TaskLabelVO;
    @ApiProperty()
    board: TaskBoardVO;
    @ApiProperty()
    name: string;
    @ApiProperty()
    parentPropertyEntity: TaskPropertyVO;
    @ApiProperty()
    property: TaskPropertyVO;
    @ApiProperty()
    user: TaskUserVO;
    @ApiProperty()
    organisation: TaskOrganisationVO;
    @ApiProperty()
    status: TaskStatus;
    @ApiProperty()
    column: TaskColumnVO;
    constructor(task: Task) {
        this.id = task.id;
        this.type = task.type;
        this.name = task.name;
        this.deadline = task.deadline;
        this.status = task.status;
    }
}

export class AutomatedTaskLabelVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    name: string;
    constructor(label: TaskLabel) {
        this.id = label.id;
        this.name = label.name;
    }
}

export class AutomatedTasksVO {
    @ApiProperty()
    active: boolean;
    @ApiProperty()
    assignee: string;
    @ApiProperty()
    createdAt: Date;
    @ApiProperty()
    description: string;
    @ApiProperty()
    eventName: string;
    @ApiProperty()
    eventValue: string;
    @ApiProperty()
    id: string;
    @ApiProperty()
    label: AutomatedTaskLabelVO;
    constructor(automatedTask: AutomatedTask) {
        this.id = automatedTask.id;
        this.assignee = automatedTask.assignee;
        this.active = automatedTask.active;
        this.description = automatedTask.description;
        this.eventName = automatedTask.eventName;
        this.eventValue = automatedTask.eventValue;
        this.createdAt = automatedTask.createdAt;
    }
}

export class TaskLabelTaskVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    description: string;
    constructor(task: Task) {
        this.id = task.id;
        this.name = task.name;
        this.description = task.description;
    }
}

export class TaskLabelItemVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    isDefault: boolean;
    @ApiProperty()
    organisationTaskChecklistIds: string[];
    @ApiProperty()
    tasks: TaskLabelTaskVO[];
    constructor(taskLabel: TaskLabel) {
        this.id = taskLabel.id;
        this.name = taskLabel.name;
        this.isDefault = taskLabel.isDefault;
        this.organisationTaskChecklistIds =
            taskLabel.organisationTaskChecklistIds;
    }
}

export class BoardColumnVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    name: string;
    constructor(column: Column) {
        this.id = column.id;
        this.name = column.name;
    }
}

export class BoardVO {
    @ApiProperty({ required: true })
    id: string;
    @ApiProperty()
    columns: BoardColumnVO[];
    constructor(board: Board) {
        this.id = board.id;
    }
}
