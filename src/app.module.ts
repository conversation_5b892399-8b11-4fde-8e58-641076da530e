import {
    MiddlewareConsumer,
    Module,
    NestModule,
    RequestMethod,
} from '@nestjs/common';
import { SearchModule } from './search/search.module';
import { AppLoggerMiddleware } from './common/middleware/appLogger.middleware';
import { AuthorizationMiddleware } from './common/middleware/authorization.middleware';
import { CommonModule } from './common/common.module';
import { ApplicationModule } from './application/application.module';
import { BoardModule } from './board/board.module';
import { ContractModule } from './contract/contract.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { DocumentModule } from './document/document.module';
import { IntegrationModule } from './integration/integration.module';
import { OrganisationModule } from './organisation/organisation.module';
import { PropertyModule } from './property/property.module';
import { ReportModule } from './report/report.module';
import { TaskModule } from './schedule/schedule.module';
import { ChatbotModule } from './chatbot/chatbot.module';
import { OpenApiModule } from './openapi/openapi.module';
import { UserModule } from './user/user.module';
import { InvitationModule } from './invitation/invitation.module';
import { WebPushSubscriptionModule } from './webPushSubscription/webPushSubscription.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
    imports: [
        SearchModule,
        EventEmitterModule.forRoot(),
        ScheduleModule.forRoot(),
        CommonModule,
        ApplicationModule,
        BoardModule,
        ContractModule,
        DashboardModule,
        DocumentModule,
        IntegrationModule,
        OrganisationModule,
        PropertyModule,
        ReportModule,
        TaskModule,
        ChatbotModule,
        OpenApiModule,
        UserModule,
        InvitationModule,
        WebPushSubscriptionModule,
    ],
    providers: [],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(AppLoggerMiddleware).forRoutes('*');
        consumer
            .apply(AuthorizationMiddleware)
            .exclude({
                path: '/api/public/eot-email-confirm',
                method: RequestMethod.ALL,
            })
            .exclude({
                path: '/api/v1/schedule/(.*)',
                method: RequestMethod.ALL,
            })
            .exclude({
                path: '/api/openapi/(.*)',
                method: RequestMethod.ALL,
            })
            .forRoutes('/api/*');
    }
}
