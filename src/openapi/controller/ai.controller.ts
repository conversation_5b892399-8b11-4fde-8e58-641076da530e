import { Controller, Post, UseGuards, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { AiService } from '../service/ai.service';
import { ApiKeyGuard } from '../../common/auth/api-key.guard';
import {
    EndOfTenancyPageAIQueryDTO,
    EndOfTenancyResponseDTO,
    PropertyAIQueryDTO,
    TenancyAIQueryDTO,
    TenancyResponseDTO,
    PropertyResponseDTO,
    ConversationAIQueryDTO,
    TaskAIQueryDTO,
    ContactAIQueryDTO,
    ReportIssueAIQueryDTO,
    ReportIssueSuggestQueryDTO,
    ReportIssueSuggestResponseDTO,
    ComplianceAIQueryDTO,
    ComplianceResponseDTO,
    DocumentAIQueryDTO,
    DocumentResponseDTO,
    AddTaskAIQueryDTO,
    TaskSuggestQueryDTO,
    TaskSuggestResponseDTO,
    SendMessageAIQueryDTO,
    SendEmailAIQueryDTO,
    EmailIntegrationQueryDTO,
    EmailIntegrationResponseDTO,
} from '../model/eot.model';

@ApiTags('open api - ai')
@Controller('api/openapi/ai')
export class AiController {
    constructor(private readonly aiService: AiService) {}

    @Post('eot/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query tenancy that are about to expire.',
        description:
            'Retrieve tenancy records that are nearing their end based on filters such as address, stage, and end date.',
    })
    @ApiBody({
        type: EndOfTenancyPageAIQueryDTO,
        description: 'Query parameters for searching tenancy records.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching tenancy records.',
        type: [EndOfTenancyResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchEndOfTenancy(@Body() query: EndOfTenancyPageAIQueryDTO) {
        return this.aiService.searchEndOfTenancy(query);
    }

    @Post('property/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query property.',
        description:
            'Retrieve property records based on filters such as address, type, status',
    })
    @ApiBody({
        type: PropertyAIQueryDTO,
        description: 'Query parameters for searching property records.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching property records.',
        type: [PropertyResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchProperty(@Body() query: PropertyAIQueryDTO) {
        return this.aiService.searchProperty(query);
    }

    @Post('tenancy/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query tenancy.',
        description:
            'Retrieve tenancy records based on filters such as address, type, status',
    })
    @ApiBody({
        type: EndOfTenancyPageAIQueryDTO,
        description: 'Query parameters for searching tenancy records.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching tenancy records.',
        type: [TenancyResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchTenancy(@Body() query: TenancyAIQueryDTO) {
        return this.aiService.searchTenancy(query);
    }

    @Post('task/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query my tasks.',
        description: 'Retrieve my tasks.',
    })
    @ApiBody({
        type: TenancyAIQueryDTO,
        description: 'Query parameters for searching my tasks.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching tasks records.',
        type: [TenancyResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchMyTask(@Body() query: TaskAIQueryDTO) {
        return this.aiService.searchTasks(query);
    }

    @Post('conversation/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query my conversations.',
        description: 'Retrieve my conversations.',
    })
    @ApiBody({
        type: ConversationAIQueryDTO,
        description: 'Query parameters for searching my conversations.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching conversations records.',
        type: [TenancyResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchMyConversation(@Body() query: ConversationAIQueryDTO) {
        return this.aiService.searchConversations(query);
    }

    @Post('contact/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query contacts.',
        description: 'Retrieve contacts records.',
    })
    @ApiBody({
        type: ContactAIQueryDTO,
        description: 'Query parameters for searching contacts.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching contacts records.',
        type: [TenancyResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchContact(@Body() query: ContactAIQueryDTO) {
        return this.aiService.searchContacts(query);
    }

    @Post('issue/report')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Report a property issue',
        description: 'Create a new property issue report',
    })
    @ApiBody({
        type: ReportIssueAIQueryDTO,
        description: 'Data for reporting a property issue',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully reported the issue',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async reportIssue(@Body() query: ReportIssueAIQueryDTO) {
        return this.aiService.reportIssue(query);
    }

    @Post('issue/suggest')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Get suggestions for reporting an issue',
        description:
            'Retrieve available properties, categories and rooms for issue reporting',
    })
    @ApiBody({
        type: ReportIssueSuggestQueryDTO,
        description: 'Query parameters for getting issue reporting suggestions',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved suggestions',
        type: ReportIssueSuggestResponseDTO,
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async reportIssueSuggest(@Body() query: ReportIssueSuggestQueryDTO) {
        return this.aiService.reportIssueSuggest(query);
    }

    @Post('compliance/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query compliance documents.',
        description: 'Retrieve compliance documents based on filters.',
    })
    @ApiBody({
        type: ComplianceAIQueryDTO,
        description: 'Query parameters for searching compliance documents.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching compliance documents.',
        type: [ComplianceResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchCompliance(@Body() query: ComplianceAIQueryDTO) {
        return this.aiService.searchCompliance(query);
    }

    @Post('document/search')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Query documents.',
        description:
            'Retrieve documents based on filters such as name, type, and date.',
    })
    @ApiBody({
        type: DocumentAIQueryDTO,
        description: 'Query parameters for searching documents.',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved matching documents.',
        type: [DocumentResponseDTO],
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async searchDocument(@Body() query: DocumentAIQueryDTO) {
        return this.aiService.searchDocument(query);
    }

    @Post('task/add')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Add a new task',
        description: 'Create a new task with specified details',
    })
    @ApiBody({
        type: AddTaskAIQueryDTO,
        description: 'Data for creating a new task',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully created the task',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async addTask(@Body() query: AddTaskAIQueryDTO) {
        return this.aiService.addTask(query);
    }

    @Post('task/suggest')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Get suggestions for creating a task',
        description:
            'Retrieve available columns, labels, and assignees for task creation',
    })
    @ApiBody({
        type: TaskSuggestQueryDTO,
        description: 'Query parameters for getting task creation suggestions',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved suggestions',
        type: TaskSuggestResponseDTO,
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async taskSuggest(@Body() query: TaskSuggestQueryDTO) {
        return this.aiService.taskSuggest(query);
    }

    @Post('message/send')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Send a message',
        description: 'Send a message to specified recipients',
    })
    @ApiBody({
        type: SendMessageAIQueryDTO,
        description: 'Data for sending a message',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully sent the message',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async sendMessage(@Body() query: SendMessageAIQueryDTO) {
        return this.aiService.sendMessage(query);
    }

    @Post('email/send')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Send an email',
        description: 'Send an email to specified recipients',
    })
    @ApiBody({
        type: SendEmailAIQueryDTO,
        description: 'Data for sending an email',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully sent the email',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async sendEmail(@Body() query: SendEmailAIQueryDTO) {
        return this.aiService.sendEmail(query);
    }

    @Post('email/integrations')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'Get available email integrations',
        description:
            'Retrieve available email integrations for the current workspace',
    })
    @ApiBody({
        type: EmailIntegrationQueryDTO,
        description: 'Query parameters for getting email integrations',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved email integrations',
        type: EmailIntegrationResponseDTO,
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async getEmailIntegrations(@Body() query: EmailIntegrationQueryDTO) {
        return this.aiService.getEmailIntegrations(query);
    }
}
