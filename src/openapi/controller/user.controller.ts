import {
    Body,
    Controller,
    Get,
    Post,
    Req,
    Res,
    UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserService } from '../service/user.service';
import { ApiKeyGuard } from 'src/common/auth/api-key.guard';
import { LwRequest, responseOk } from 'src/common/util/requestUtil';
import { UserLoginSummaryDTO } from '../model/user.model';
import { Response } from 'express';

@ApiTags('open api - user')
@Controller('api/openapi/user')
export class UserController {
    constructor(private readonly userService: UserService) {}

    @Get('summarizeUserLoginRecord')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'summarizeUserLoginRecord',
        description: 'summarize all user login record',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully summarized',
        type: UserLoginSummaryDTO,
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async summarizeUserLoginRecord() {
        return this.userService.summarizeUserLoginRecord();
    }

    @Post('signUp')
    @ApiOperation({
        summary: 'signUp',
        description: 'signUp',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully signUp',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    @ApiBody({
        description: 'input',
    })
    async signUp(
        @Body() input: any,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        return responseOk(res, await this.userService.signUp(input));
    }

    @Post('signIn')
    @ApiOperation({
        summary: 'signIn',
        description: 'signIn',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully signIn',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async signIn(
        @Body() input: any,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        const result = await this.userService.signIn(input);
        if (result) {
            res.cookie('letting-auth', result.idToken, {
                domain: '.loftyworks.com',
                httpOnly: true,
                secure: true,
                path: '/',
            });
            res.cookie('letting-jti', result.jti, {
                domain: '.loftyworks.com',
                httpOnly: true,
                secure: true,
                path: '/',
            });
        }
        return responseOk(res, { exp: result.exp });
    }

    @Get('refreshAuth')
    @ApiOperation({
        summary: 'refreshAuth',
        description: 'refreshAuth',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully refreshAuth',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async refreshAuth(
        @Body() input: any,
        @Req() req: any,
        @Res() res: Response,
    ) {
        input.jti = req.cookies['letting-jti'];
        const result = await this.userService.refreshAuth(input);
        if (result) {
            res.cookie('letting-auth', result.idToken, {
                domain: '.loftyworks.com',
                httpOnly: true,
                secure: true,
                path: '/',
            });
            res.cookie('letting-jti', result.jti, {
                domain: '.loftyworks.com',
                httpOnly: true,
                secure: true,
                path: '/',
            });
        }
        return responseOk(res, { exp: result.exp });
    }

    @Get('logOut')
    @ApiOperation({
        summary: 'logOut',
        description: 'logOut',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully logOut',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async logOut(@Req() req: any, @Res() res: Response) {
        const jti = req.cookies['letting-jti'];
        res.clearCookie('letting-auth', {
            domain: '.loftyworks.com',
            httpOnly: true,
            secure: true,
            path: '/',
        });
        res.clearCookie('letting-jti', {
            domain: '.loftyworks.com',
            httpOnly: true,
            secure: true,
            path: '/',
        });
        return responseOk(res, await this.userService.logOut(jti));
    }

    @Post('logOutNotify')
    @ApiOperation({
        summary: 'logOutNotify',
        description: 'logOutNotify',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully logOut',
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async logOutNotify(
        @Body() input: any,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        res.clearCookie('letting-auth', {
            domain: '.loftyworks.com',
            httpOnly: true,
            secure: true,
            path: '/',
        });
        res.clearCookie('letting-jti', {
            domain: '.loftyworks.com',
            httpOnly: true,
            secure: true,
            path: '/',
        });
        return responseOk(res, await this.userService.logOutNotify(input));
    }
}
