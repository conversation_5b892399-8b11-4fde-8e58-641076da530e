import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UserService } from '../service/user.service';
import { ApiKeyGuard } from '../../common/auth/api-key.guard';
import {
    UserLoginSummaryDTO
} from '../model/user.model';

@ApiTags('open api - user')
@Controller('api/openapi/user')
export class UserController {
    constructor(private readonly userService: UserService) {}

    @Get('summarizeUserLoginRecord')
    @UseGuards(ApiKeyGuard)
    @ApiOperation({
        summary: 'summarizeUserLoginRecord',
        description:
            'summarize all user login record',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully summarized',
        type: UserLoginSummaryDTO,
    })
    @ApiResponse({ status: 400, description: 'Invalid request parameters.' })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - API Key required.',
    })
    async reportIssueSuggest() {
        return this.userService.summarizeUserLoginRecord();
    }
}
