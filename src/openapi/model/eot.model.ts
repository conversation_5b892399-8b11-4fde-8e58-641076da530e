import { ApiProperty } from '@nestjs/swagger';

export class EndOfTenancyPageAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    page: number;

    @ApiProperty()
    size: number;

    @ApiProperty({ required: false })
    keyword?: string;

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    stageList?: string[];

    @ApiProperty({ required: false })
    fromEndDate?: string;

    @ApiProperty({ required: false })
    toEndDate?: string;
}

export class PropertyAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty({ required: false })
    organisationId: string;

    @ApiProperty({ required: false })
    address: string;

    @ApiProperty({ required: false })
    type: string;

    @ApiProperty({ required: false })
    status: string;
}

export class TenancyAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty({ required: false })
    organisationId: string;

    @ApiProperty({ required: false })
    address: string;

    @ApiProperty()
    startDate: string;

    @ApiProperty()
    renewalDate: string;

    @ApiProperty()
    endDate: string;

    @ApiProperty()
    type: string;
}

export class TaskAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty({ required: false })
    assignedUserId: string;

    @ApiProperty({ required: false })
    columnId: string;

    @ApiProperty({ required: false })
    labelId: string;

    @ApiProperty({ required: false })
    parentId: string;
}

export class ConversationAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    associatedUserId: string;

    @ApiProperty({
        required: false,
        enum: ['asc', 'desc'],
        description:
            'Sort by update time: asc (oldest first) or desc (newest first)',
        default: 'desc',
    })
    sortByUpdateTime?: string;

    @ApiProperty({
        required: false,
        description: 'Maximum number of conversations to return',
        default: 100,
        minimum: 1,
        maximum: 1000,
    })
    limit?: number;
}

export class ContactAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    ifContactSearch: boolean;

    @ApiProperty()
    type: string;

    @ApiProperty()
    name: string;

    @ApiProperty({ required: false })
    fname?: string;

    @ApiProperty({ required: false })
    sname?: string;

    @ApiProperty()
    email: string;

    @ApiProperty()
    phone: string;
}

export class EndOfTenancyResponseDTO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    address: string;

    @ApiProperty()
    endDate: Date;

    @ApiProperty()
    reference: string;

    @ApiProperty()
    primaryTenantName: string;

    @ApiProperty()
    stage: string;

    @ApiProperty()
    status: string;
}

export class TenancyResponseDTO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    address: string;
}

export class PropertyResponseDTO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    address: string;
}

export class ReportIssueAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    categoryIcon: string;

    @ApiProperty()
    categoryId: string;

    @ApiProperty()
    categoryName: string;

    @ApiProperty({ required: false })
    description: string;

    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    roomId: string;

    @ApiProperty()
    roomName: string;
}

export class ReportIssueSuggestQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    propertyAddress: string;
}

export class ReportIssueSuggestResponseDTO {
    @ApiProperty()
    properties: Array<{
        id: string;
        address: string;
    }>;

    @ApiProperty()
    categories: Array<{
        id: string;
        name: string;
        icon: string;
        rooms: string[];
    }>;

    @ApiProperty()
    rooms: Array<{
        id: string;
        name: string;
    }>;
}

export class ComplianceAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    documentPropertyId: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    page: number;

    @ApiProperty()
    size: number;
}

export class ComplianceResponseDTO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    key: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    mineType: number;
}

export class DocumentAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    documentPropertyId: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    page: number;

    @ApiProperty()
    size: number;
}

export class DocumentResponseDTO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    key: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    mineType: number;
}

export class AddTaskAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    description: string;

    @ApiProperty()
    boardId: string;

    @ApiProperty()
    columnId: string;

    @ApiProperty()
    labelId: string;

    @ApiProperty({ required: false })
    parentId: string;
}

export class TaskSuggestQueryDTO {
    @ApiProperty()
    conversationId: string;
}

export class TaskSuggestResponseDTO {
    @ApiProperty()
    columns: Array<{
        id: string;
        name: string;
    }>;

    @ApiProperty()
    labels: Array<{
        id: string;
        name: string;
        color: string;
    }>;

    @ApiProperty()
    assignees: Array<{
        id: string;
        name: string;
        email: string;
    }>;
}

export class SendMessageAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty({
        description: 'Recipients of the message',
        type: [String],
    })
    associatedUserId: string;

    @ApiProperty({
        description: 'category for conversation PERSONAL/SHARED',
        type: [String],
    })
    category: string;

    @ApiProperty({
        description: 'Content of the message',
    })
    content: string;

    @ApiProperty({
        description:
            'message type default INTERNAL, but change to PORTAL if associatedUserId is landlord or tenant',
        required: true,
    })
    type: string;
}

export class SendEmailAIQueryDTO {
    @ApiProperty()
    conversationId: string;

    @ApiProperty({
        description: 'Email sender',
    })
    emailFrom: string;

    @ApiProperty({
        description: 'Email recipient',
    })
    emailTo: string;

    @ApiProperty({
        description: 'the email sender type GOOGLE_GMAIL/OUTLOOK',
    })
    emailType: string;

    @ApiProperty({
        description: 'Recipient(user id) of the email ',
        type: [String],
    })
    associatedUserId: string;

    @ApiProperty({
        description: 'category for conversation PERSONAL/SHARED',
        type: [String],
    })
    category: string;

    @ApiProperty({
        description: 'Email subject',
    })
    subject: string;

    @ApiProperty({
        description: 'Email content',
    })
    content: string;
}

export class EmailIntegrationQueryDTO {
    @ApiProperty()
    conversationId: string;
}

export class EmailIntegrationResponseDTO {
    @ApiProperty({
        description: 'List of email addresses available for sending emails',
        type: [String],
    })
    emailAddresses: string[];
}
