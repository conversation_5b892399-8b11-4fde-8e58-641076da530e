import { Injectable, Logger } from '@nestjs/common';
import { EotService } from '../../contract/service/eot.service';
import { PropertyService } from '../../property/service/property.service';
import { ContractService } from '../../contract/service/contract.service';
import { ChatbotService } from '../../chatbot/service/chatbot.service';
import { TaskService } from '../../task/service/task.service';
import { DocumentService } from '../../document/service/document.service';
import { CommunicationService } from '../../communication/service/communication.service';
import { UserService } from '../../user/service/user.service';
import {
    EndOfTenancyPageAIQueryDTO,
    EndOfTenancyResponseDTO,
    PropertyAIQueryDTO,
    TaskAIQueryDTO,
    TenancyAIQueryDTO,
    ConversationAIQueryDTO,
    ContactAIQueryDTO,
    ReportIssueAIQueryDTO,
    ReportIssueSuggestQueryDTO,
    ComplianceAIQueryDTO,
    DocumentAIQueryDTO,
    TaskSuggestQueryDTO,
    AddTaskAIQueryDTO,
    SendMessageAIQueryDTO,
    SendEmailAIQueryDTO,
    EmailIntegrationQueryDTO,
    EmailIntegrationResponseDTO,
} from '../model/eot.model';
import { EndOfTenancyPageQueryDTO } from '../../contract/model/eot.model';
import { PropertySearchPageDTO } from '../../property/model/property.model';
import { TenancySearchPageDTO } from '../../contract/model/tenancy.model';
import { TaskSearchDTO } from '../../task/model/task.model';
import { UserSearchDTO } from '../../user/model/user.model';
import { ToolAction, ListType } from '../../chatbot/enum/message.enum';
import { DocumentPageQueryDTO } from '../../document/model/document.model';
import { SuccessMessage } from '../../common/enum/successMessage';
import { RoomRepository } from '../../common/repository/room';
import { CateRepository } from '../../common/repository/category';
import { UserRepository } from '../../common/repository/user';
import { IntegrationRepository } from '../../common/repository/integration';
import { BoardRepository } from '../../common/repository/board';
import { TaskLabelRepository } from '../../common/repository/taskLabel';
import { ColumnRepository } from '../../common/repository/column';

@Injectable()
export class AiService {
    private logger = new Logger('AiService', { timestamp: true });

    constructor(
        private eotService: EotService,
        private chatbotService: ChatbotService,
        private propertyService: PropertyService,
        private contractService: ContractService,
        private taskService: TaskService,
        private communicationService: CommunicationService,
        private userService: UserService,
        private documentService: DocumentService,
        private roomRepository: RoomRepository,
        private categoryRepository: CateRepository,
        private userRepository: UserRepository,
        private integrationRepository: IntegrationRepository,
        private boardRepository: BoardRepository,
        private taskLabelRepository: TaskLabelRepository,
        private columnRepository: ColumnRepository,
    ) {}

    async searchEndOfTenancy(query: EndOfTenancyPageAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const eotQuery = Object.assign(new EndOfTenancyPageQueryDTO(), query);
        eotQuery.organisationId = conversationInfo.organisationId;
        const eotList: any = await this.eotService.getEotByPage(eotQuery);
        const eotDtoList: EndOfTenancyResponseDTO[] = [];
        for (const eot of eotList.data) {
            eotDtoList.push({
                id: eot.id,
                propertyId: eot.propertyId,
                address: eot.address,
                endDate: eot.endDate,
                reference: eot.reference,
                primaryTenantName: eot.primaryTenantName,
                stage: eot.stage,
                status: eot.status,
            });
        }
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_EOT]: {
                    [ListType.EOT_LIST]: eotDtoList,
                },
            },
        });
        return {
            total:
                'the total is ' +
                eotList.total +
                ' but we only show the 100 records',
            eotDtoList,
        };
    }

    async searchProperty(query: PropertyAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const propertyQuery = Object.assign(new PropertySearchPageDTO(), query);
        propertyQuery.organisationId = conversationInfo.organisationId;
        const { total, propertyList } =
            await this.propertyService.searchOrgProperties(propertyQuery);
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_PROPERTY]: {
                    [ListType.PROPERTY_LIST]: propertyList,
                },
            },
        });
        return {
            total:
                'the total is ' + total + ' but we only show the 100 records',
            propertyList,
        };
    }

    async searchTenancy(query: TenancyAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const tenancyQuery = Object.assign(new TenancySearchPageDTO(), query);
        tenancyQuery.organisationId = conversationInfo.organisationId;
        const { total, tenancyList } =
            await this.contractService.searchTenancy(tenancyQuery);
        await this.updateLatestToolOutput(query.conversationId, {
            [ToolAction.SEARCH_TENANCY]: {
                [ListType.TENANCY_LIST]: tenancyList,
            },
        });
        return {
            total:
                'the total is ' + total + ' but we only show the 100 records',
            tenancyList,
        };
    }

    async searchTasks(query: TaskAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const boardList = await this.taskService.listBoards(
            conversationInfo.organisationId,
            conversationInfo.userId,
        );
        const boardId = boardList[0].id;
        const taskQuery = new TaskSearchDTO();
        taskQuery.boardId = boardId;

        if (query.assignedUserId) {
            taskQuery.taskUserId = query.assignedUserId;
        }

        if (query.columnId) {
            taskQuery.columnId = query.columnId;
        }

        if (query.labelId) {
            taskQuery.labelId = query.labelId;
        }

        if (query.parentId) {
            taskQuery.parentId = query.parentId;
        }

        const taskList = await this.taskService.searchTasks(taskQuery);

        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_TASKS]: {
                    [ListType.TASK_LIST]: taskList,
                },
            },
        });
        return taskList;
    }

    async searchConversations(query: ConversationAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const userId = conversationInfo.userId;
        const userInfo = await this.userRepository.findByCognitoId(userId);

        let members: string[] | undefined;
        if (query.associatedUserId) {
            members = [userInfo.id, query.associatedUserId];
        }

        const conversationList =
            await this.communicationService.queryConversationsDashboard(
                {
                    organisationId: conversationInfo.organisationId,
                    memberIds: members,
                    sortType:
                        query.sortByUpdateTime === 'asc' ? 'Oldest' : 'Latest',
                },
                conversationInfo.userId,
            );

        const limitedList = query.limit
            ? conversationList.slice(0, query.limit)
            : conversationList;

        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_CONVERSATIONS]: {
                    [ListType.CONVERSATION_LIST]: limitedList,
                },
            },
        });
        return limitedList;
    }

    async searchContacts(query: ContactAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const userQuery = Object.assign(new UserSearchDTO(), query);
        if (query.ifContactSearch) {
            userQuery.types = [
                'SUPPLIER',
                'AGENT',
                'THIRD_PARTY',
                'TENANT',
                'LEASEHOLDER',
                'CONSULTANT',
                'OWNER',
                'FINANCE',
                'COLLEGE',
                'LANDLORD',
                'MANAGER',
                'GUARANTOR',
                'HELPDESK',
                'UNASSIGNED',
                'NEW',
                'OTHER',
                'INBOX',
                'PROSPECT',
                'LOFTYPAY_ADMIN',
            ];
        }
        userQuery.organisationId = conversationInfo.organisationId;
        const { total, contactList } =
            await this.userService.searchUsers(userQuery);
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_CONTACT]: {
                    [ListType.CONTACT_LIST]: contactList,
                },
            },
        });
        return {
            total:
                'the total is ' + total + ' but we only show the 100 records',
            contactList: contactList,
        };
    }

    async updateLatestToolOutput(conversationId: string, newToolOutput: any) {
        const conversationInfo =
            await this.chatbotService.getConversationById(conversationId);

        const existingToolOutput = conversationInfo.latestToolOutput || {};
        const mergedToolOutput = {
            ...(existingToolOutput as object),
            ...newToolOutput,
        };

        return await this.chatbotService.updateConversation(conversationId, {
            latestToolOutput: mergedToolOutput,
        });
    }

    async reportIssue(query: ReportIssueAIQueryDTO) {
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.REPORT_ISSUE]: {
                    detail: {
                        categoryIcon: query.categoryIcon,
                        categoryId: query.categoryId,
                        categoryName: query.categoryName,
                        note: query.description,
                        propertyId: query.propertyId,
                        roomId: query.roomId,
                        roomName: query.roomName,
                    },
                },
            },
        });
        return SuccessMessage.ISSUE_REPORT_PREPARED;
    }

    async reportIssueSuggest(query: ReportIssueSuggestQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const propertyQuery = new PropertySearchPageDTO();
        propertyQuery.organisationId = conversationInfo.organisationId;
        if (query.propertyAddress) {
            propertyQuery.address = query.propertyAddress;
        }
        const { propertyList: properties } =
            await this.propertyService.searchOrgProperties(propertyQuery);

        const [categories, rooms] = await Promise.all([
            this.categoryRepository.scanAllTable(),
            this.roomRepository.scanAllTable(),
        ]);

        const response = {
            properties: properties.map((p) => ({
                id: p.id,
                address: p.name,
            })),
            categories: categories.map((c) => ({
                id: c.id,
                name: c.name,
                icon: c.icon,
                rooms: c.rooms,
            })),
            rooms: rooms.map((r) => ({
                id: r.id,
                name: r.name,
            })),
        };
        return response;
    }

    async searchCompliance(query: ComplianceAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const complianceQuery = Object.assign(
            new DocumentPageQueryDTO(),
            query,
        );
        complianceQuery.organisationId = conversationInfo.organisationId;
        if (query.documentPropertyId) {
            complianceQuery.propertyIds = [query.documentPropertyId];
        }
        if (query.type) {
            complianceQuery.types = [query.type];
        }
        const complianceData =
            await this.documentService.getDocumentByPage(complianceQuery);
        const complianceList = complianceData.data;
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_COMPLIANCE]: {
                    [ListType.COMPLIANCE_LIST]: complianceList,
                },
            },
        });
        return {
            total:
                'the total is ' +
                complianceData.total +
                ' but we only show the 100 records',
            complianceList,
        };
    }

    async searchDocument(query: DocumentAIQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const documentQuery = Object.assign(new DocumentPageQueryDTO(), query);
        documentQuery.organisationId = conversationInfo.organisationId;
        if (query.documentPropertyId) {
            documentQuery.propertyIds = [query.documentPropertyId];
        }
        if (query.type) {
            documentQuery.types = [query.type];
        }
        const documentData =
            await this.documentService.getDocumentByPage(documentQuery);
        const documentList = documentData.data;
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEARCH_DOCUMENT]: {
                    [ListType.DOCUMENT_LIST]: documentList,
                },
            },
        });
        return {
            total:
                'the total is ' +
                documentData.total +
                ' but we only show the 100 records',
            documentList,
        };
    }

    async addTask(query: AddTaskAIQueryDTO) {
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.ADD_TASK]: {
                    detail: {
                        name: query.name,
                        description: query?.description ?? '',
                        boardId: query.boardId,
                        columnId: query.columnId,
                        taskLabelId: query.labelId,
                        parentId: query?.parentId ?? '',
                    },
                },
            },
        });
        return SuccessMessage.TASK_PREPARED;
    }

    async taskSuggest(query: TaskSuggestQueryDTO) {
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const organisationId = conversationInfo.organisationId;
        const [boardItems, taskLabelItems] = await Promise.all([
            this.boardRepository.findByOrganisationId(organisationId),
            this.taskLabelRepository.findByOrganisationId(organisationId),
        ]);
        const boardId = boardItems[0]?.id || '';
        if (!boardId) {
            return {
                columns: [],
                labels: [],
            };
        }
        const columns = await this.columnRepository.findByBoardId(boardId);
        return {
            boardId: boardId,
            columns: columns,
            labels: taskLabelItems,
        };
    }

    async sendMessage(query: SendMessageAIQueryDTO) {
        const conversationId = query.conversationId;
        const conversationInfo =
            await this.chatbotService.getConversationById(conversationId);
        const userId = conversationInfo.userId;
        const userInfo = await this.userRepository.findByCognitoId(userId);
        const members: string[] = [userInfo.id, query.associatedUserId];
        const communicationConversation =
            await this.communicationService.checkExistingConversation(
                conversationInfo.organisationId,
                members,
                query.category,
            );
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEND_MESSAGE]: {
                    detail: {
                        conversationId: communicationConversation.exists
                            ? communicationConversation.conversation.id
                            : '',
                        isCreate: !communicationConversation.exists,
                        type: query.type,
                        content: query.content,
                        associatedUserId: query.associatedUserId,
                    },
                },
            },
        });
        return SuccessMessage.MESSAGE_PREPARED;
    }

    async sendEmail(query: SendEmailAIQueryDTO) {
        const conversationId = query.conversationId;
        const conversationInfo =
            await this.chatbotService.getConversationById(conversationId);
        const userId = conversationInfo.userId;
        const userInfo = await this.userRepository.findByCognitoId(userId);
        const members: string[] = [userInfo.id, query.associatedUserId];
        const communicationConversation =
            await this.communicationService.checkExistingConversation(
                conversationInfo.organisationId,
                members,
                query.category,
                true,
            );
        await this.chatbotService.updateConversation(query.conversationId, {
            latestToolOutput: {
                [ToolAction.SEND_EMAIL]: {
                    detail: {
                        category: query.category,
                        emailFrom: query.emailFrom,
                        emailType: query.emailType,
                        isCreate: !communicationConversation.exists,
                        type: 'EMAIL',
                        to: query.emailTo,
                        associatedUserId: query.associatedUserId,
                        subject: query.subject,
                        html: query.content,
                        id: communicationConversation.exists
                            ? communicationConversation.conversation.id
                            : '',
                    },
                },
            },
        });
        return SuccessMessage.EMAIL_PREPARED;
    }

    async getEmailIntegrations(
        query: EmailIntegrationQueryDTO,
    ): Promise<EmailIntegrationResponseDTO> {
        this.logger.log(
            'Getting email integrations for conversation: ' +
                query.conversationId,
        );
        const conversationInfo = await this.chatbotService.getConversationById(
            query.conversationId,
        );
        const organisationId = conversationInfo.organisationId;
        const integrations =
            await this.integrationRepository.findIntegrationByOrganisationId(
                organisationId,
            );
        const emailAddressList: string[] = [];
        if (integrations && integrations.length > 0) {
            for (const integration of integrations) {
                if (
                    integration.type &&
                    (integration.type === 'GOOGLE_GMAIL' ||
                        integration.type === 'OUTLOOK') &&
                    integration.emailAddress
                ) {
                    emailAddressList.push(integration.emailAddress);
                }
            }
        }

        return {
            emailAddresses: emailAddressList,
        };
    }
}
