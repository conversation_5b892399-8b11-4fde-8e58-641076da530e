import { Injectable, Logger } from '@nestjs/common';
import {
    listAllUserLoginRecord,
} from '../../common/service/ddb.service';
import {
    UserLoginSummaryDTO
} from '../model/user.model';

@Injectable()
export class UserService {
    private logger = new Logger('UserService', { timestamp: true });

    constructor() {}

    async summarizeUserLoginRecord() {
        const records = await listAllUserLoginRecord();
        const summaryMap = new Map<string, { email: string; organisationId: string; loginTimes: number }>();

        records.forEach(record => {
            const key = `${record.email}|${record.organisationId}`;
            const item = summaryMap.get(key) || {
                email: record.email,
                organisationId: record.organisationId,
                loginTimes: 0
            };
            item.loginTimes++;
            summaryMap.set(key, item);
        });

        const sortedItems = Array.from(summaryMap.values()).sort((a, b) => b.loginTimes - a.loginTimes);
        return { items: sortedItems };
    }
}
