import { Injectable, Logger } from '@nestjs/common';
import { listAllUserLoginRecord } from 'src/common/service/ddb.service';
import { OrganisationService } from 'src/organisation/service/organisation.service';
import { CreateOrganisationVO } from 'src/organisation/model/organisation.model';
import { UserRepository } from 'src/common/repository/user';
import { UserLoginSessionRepository } from 'src/common/repository/userLoginSession';
import { UserPreferenceRepository } from 'src/common/repository/userPreference';
import { OrganisationUserRepository } from 'src/common/repository/organisationUser';
import { RedisService } from 'src/common/service/redis.service';
import { v1 as uuidV1 } from 'uuid';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class UserService {
    private readonly logger = new Logger('UserService', { timestamp: true });

    constructor(
        private readonly httpService: HttpService,
        private readonly organisationService: OrganisationService,
        private readonly userRepository: UserRepository,
        private readonly userLoginSessionRepository: UserLoginSessionRepository,
        private readonly userPreferenceRepository: UserPreferenceRepository,
        private readonly organisationUserRepository: OrganisationUserRepository,
        private readonly redisService: RedisService,
    ) {}

    async summarizeUserLoginRecord() {
        await this.redisService.set('test', 'test');
        this.logger.log(
            `summarizeUserLoginRecord:test=${await this.redisService.get('test')}`,
        );
        this.logger.log(
            `summarizeUserLoginRecord:test del=${await this.redisService.del('test')}`,
        );
        const records = await listAllUserLoginRecord();
        const summaryMap = new Map<
            string,
            { email: string; organisationId: string; loginTimes: number }
        >();

        records.forEach((record) => {
            const key = `${record.email}|${record.organisationId}`;
            const item = summaryMap.get(key) || {
                email: record.email,
                organisationId: record.organisationId,
                loginTimes: 0,
            };
            item.loginTimes++;
            summaryMap.set(key, item);
        });

        const sortedItems = Array.from(summaryMap.values()).sort(
            (a, b) => b.loginTimes - a.loginTimes,
        );
        return { items: sortedItems };
    }

    async signUp(input: any) {
        // use loftyAgentId as cognitoId
        const cognitoId: string = input.loftyAgentId;

        // create user
        let user: any;
        const existedUser =
            await this.userRepository.findByCognitoId(cognitoId);
        this.logger.log(`signUp:existedUsers=${existedUser}`);
        user = {
            cognitoEmail: input.email,
            cognitoId: cognitoId,
            public: true,
            owner: cognitoId,
            onboardingStep: -1,
            onboardingSeen: false,
            nameConfirmed: true,
            type: 'ADMIN',
            fname: input.firstName,
            sname: input.lastName,
            loftyAgentBaseId: input.loftyAgentBaseId,
            emails: [{ type: 'Work', email: input.email }],
            phones: [
                { type: 'MOBILE', phone: input.phone, cca2: '', code: '' },
            ],
            createdDate: new Date().toISOString(),
        };
        if (!existedUser) {
            user.id = cognitoId;
            await this.userRepository.createItem(user);
            // create user preference
            const userPreference = {
                id: uuidV1(),
                owner: cognitoId,
                userPreferencesUserId: user.id,
                allowedNotifications: ['EMAIL_CHAT_NOTIFICATIONS'],
                createdAt: new Date().toISOString(),
                __typename: 'UserPreferences',
            };
            await this.userPreferenceRepository.createItem(userPreference);
        } else {
            await this.userRepository.updateItem(existedUser.id, user);
            user.id = existedUser.id;
        }
        const organisationId = String(input.teamId);
        const orgExisted =
            await this.organisationService.isOrganisationExist(organisationId);
        // orgId exists
        if (!orgExisted) {
            // create organisation
            const createOrganisationVO: CreateOrganisationVO = {
                organisationName: input.teamName,
                payoutVersion: null,
                utmCustomerAttributionSource: null,
                organisationId: organisationId,
            };
            await this.organisationService.initWorkspace(
                cognitoId,
                createOrganisationVO,
                true,
            );
        }
        const orgUser =
            await this.organisationUserRepository.findByUserIdAndOrganisationId(
                user.id,
                organisationId,
            );
        if (!orgUser) {
            // create organisation user
            const organisationUserObj = {
                id: uuidV1(),
                organisationUserOrganisationId: organisationId,
                organisationUserUserId: user.id,
                createdAt: new Date().toISOString(),
                __typename: 'OrganisationUser',
            };
            await this.organisationUserRepository.createItem(
                organisationUserObj,
            );
        }
        return true;
    }

    async signIn(input: any) {
        const formData = new FormData();
        formData.append('grant_type', 'authorization_code');
        formData.append('client_id', process.env.AUTH_SERVER_CLIENT_ID);
        formData.append('client_secret', process.env.AUTH_SERVER_CLIENT_SECRET);
        formData.append('code', input.authCode);
        this.logger.log(
            `signIn:AUTH_SERVER_TOKEN_URL=${process.env.AUTH_SERVER_TOKEN_URL}`,
        );
        const keysArray = Array.from(formData.keys());
        const valuesArray = Array.from(formData.values());
        this.logger.log(`signIn:formData.keys=${JSON.stringify(keysArray)}`);
        this.logger.log(
            `signIn:formData.values=${JSON.stringify(valuesArray)}`,
        );
        const resp = await this.httpService.axiosRef.post(
            process.env.AUTH_SERVER_TOKEN_URL,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            },
        );
        this.logger.log(`signIn:resp=${JSON.stringify(resp.data)}`);
        if (resp.data) {
            const idToken = resp.data['id_token'];
            const parts = idToken.split('.');
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format');
            }
            // Decode the payload (middle part)
            const payload = parts[1];
            const decodedPayload = Buffer.from(payload, 'base64').toString(
                'utf-8',
            );
            // Parse the JSON payload
            const result = JSON.parse(decodedPayload);

            this.logger.log(`signIn:result=${JSON.stringify(result)}`);
            await this.userLoginSessionRepository.createItem({
                id: result.jti,
                deviceId: result['device_id'],
                email: result.email,
                jti: result.jti,
                accessToken: resp.data['access_token'],
                refreshToken: resp.data['refresh_token'],
                createdAt: new Date().toISOString(),
            });
            await this.redisService.set(
                result.jti,
                JSON.stringify(result),
                3600,
            );

            this.logger.log(
                `signIn:redisService.get=${await this.redisService.get(result.jti)}`,
            );
            return {
                idToken,
                jti: result.jti,
                exp: result.exp,
            };
        }
        return null;
    }

    async refreshAuth(input: any) {
        const userLoginSessions =
            await this.userLoginSessionRepository.listByJti(input.jti);
        if (userLoginSessions.length === 0) {
            return;
        }
        const formData = new FormData();
        formData.append('grant_type', 'refresh_token');
        formData.append('client_id', process.env.AUTH_SERVER_CLIENT_ID);
        formData.append('client_secret', process.env.AUTH_SERVER_CLIENT_SECRET);
        formData.append('refresh_token', userLoginSessions[0].refreshToken);
        this.logger.log(
            `refreshAuth:AUTH_SERVER_TOKEN_URL=${process.env.AUTH_SERVER_TOKEN_URL}`,
        );
        const keysArray = Array.from(formData.keys());
        const valuesArray = Array.from(formData.values());
        this.logger.log(
            `refreshAuth:formData.keys=${JSON.stringify(keysArray)}`,
        );
        this.logger.log(
            `refreshAuth:formData.values=${JSON.stringify(valuesArray)}`,
        );
        const resp = await this.httpService.axiosRef.post(
            process.env.AUTH_SERVER_TOKEN_URL,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            },
        );
        this.logger.log(`refreshAuth:resp=${JSON.stringify(resp.data)}`);
        if (resp.data) {
            const idToken = resp.data['id_token'];
            const parts = idToken.split('.');
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format');
            }
            // Decode the payload (middle part)
            const payload = parts[1];
            const decodedPayload = Buffer.from(payload, 'base64').toString(
                'utf-8',
            );
            // Parse the JSON payload
            const result = JSON.parse(decodedPayload);

            this.logger.log(`refreshAuth:result=${JSON.stringify(result)}`);
            await Promise.all(
                userLoginSessions.map(async (userLoginSession) => {
                    await this.userLoginSessionRepository.updateItem(
                        userLoginSession.id,
                        {
                            deviceId: result['device_id'],
                            email: result.email,
                            jti: result.jti,
                            accessToken: resp.data['access_token'],
                            refreshToken: resp.data['refresh_token'],
                        },
                    );
                    await this.redisService.set(
                        result.jti,
                        JSON.stringify(result),
                        3600,
                    );

                    this.logger.log(
                        `refreshAuth:redisService.get=${await this.redisService.get(result.jti)}`,
                    );
                }),
            );
            return {
                idToken,
                jti: result.jti,
                exp: result.exp,
            };
        }
        return null;
    }

    async logOut(jti: string) {
        const userLoginSessions =
            await this.userLoginSessionRepository.listByJti(jti);
        this.logger.log(
            `logOut:userLoginSessions.length=${JSON.stringify(userLoginSessions.length)}`,
        );
        if (userLoginSessions.length === 0) {
            return;
        }
        await Promise.all(
            userLoginSessions.map(async (userLoginSession) => {
                await this.userLoginSessionRepository.deleteById(
                    userLoginSession.id,
                );
                this.logger.log(
                    `logOut:redisService.get=${JSON.stringify(await this.redisService.get(userLoginSession.jti))}`,
                );
                await this.redisService.del(userLoginSession.jti);
            }),
        );
        return true;
    }

    async logOutNotify(input: any) {
        const userLoginSessions =
            await this.userLoginSessionRepository.listByDeviceIdAndEmail(
                input.deviceId,
                input.email,
            );
        this.logger.log(
            `logOutNotify:userLoginSessions.length=${JSON.stringify(userLoginSessions.length)}`,
        );
        if (userLoginSessions.length === 0) {
            return;
        }
        await Promise.all(
            userLoginSessions.map(async (userLoginSession) => {
                await this.userLoginSessionRepository.deleteById(
                    userLoginSession.id,
                );
                this.logger.log(
                    `logOutNotify:redisService.get=${JSON.stringify(await this.redisService.get(userLoginSession.jti))}`,
                );
                await this.redisService.del(userLoginSession.jti);
            }),
        );
        return true;
    }
}
