import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>roller } from './controller/ai.controller';
import { UserController } from './controller/user.controller';
import { ContractModule } from '../contract/contract.module';
import { PropertyModule } from '../property/property.module';
import { ChatbotModule } from '../chatbot/chatbot.module';
import { TaskModule } from '../task/task.module';
import { DocumentModule } from '../document/document.module';
import { CommunicationModule } from '../communication/communication.module';
import { CommonModule } from '../common/common.module';
import { OrganisationModule } from '../organisation/organisation.module';
import { UserModule } from '../user/user.module';
import { AiService } from './service/ai.service';
import { UserService } from './service/user.service';
import { HttpModule } from '@nestjs/axios';

@Module({
    imports: [
        OrganisationModule,
        HttpModule,
        ContractModule,
        ChatbotModule,
        PropertyModule,
        TaskModule,
        CommunicationModule,
        UserModule,
        DocumentModule,
        CommonModule,
    ],
    controllers: [AiController, UserController],
    providers: [AiService, UserService],
})
export class OpenApiModule {}
