import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>roller } from './controller/ai.controller';
import { UserController } from './controller/user.controller';
import { ContractModule } from '../contract/contract.module';
import { PropertyModule } from '../property/property.module';
import { ChatbotModule } from '../chatbot/chatbot.module';
import { TaskModule } from '../task/task.module';
import { DocumentModule } from '../document/document.module';
import { CommunicationModule } from '../communication/communication.module';
import { CommonModule } from '../common/common.module';
import { UserModule } from '../user/user.module';
import { AiService } from './service/ai.service';
import { UserService } from './service/user.service';

@Module({
    imports: [
        ContractModule,
        ChatbotModule,
        PropertyModule,
        TaskModule,
        CommunicationModule,
        UserModule,
        DocumentModule,
        CommonModule,
    ],
    controllers: [<PERSON><PERSON><PERSON><PERSON><PERSON>, UserController],
    providers: [AiService, UserService],
})
export class OpenApiModule {}
