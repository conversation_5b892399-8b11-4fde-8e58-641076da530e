import { Controller, Post, Delete, Get, Body, Req, UseGuards } from '@nestjs/common';
import { SkipService } from '../../service/skip.service';
import { LwRequest} from '../../../common/util/requestUtil';
import { CreateSkipItemDto } from "../../model/skipItem.model";


@Controller('api/v1/skip')
export class SkipController {
    constructor(private skipService: SkipService) {}

    @Post()
    public async skip(@Req() req: LwRequest, @Body() dto: CreateSkipItemDto) {
        const dtoInstance = Object.assign(new CreateSkipItemDto(), dto);
        const currentUserId = req.user['sub'];
        dtoInstance.userId = currentUserId;
        return this.skipService.createSkipItem(dtoInstance.toPrisma());
    }

}
