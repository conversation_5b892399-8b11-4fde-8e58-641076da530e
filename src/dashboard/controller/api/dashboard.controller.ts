import { <PERSON>, Get, Logger, Req, Res} from '@nestjs/common';
import { BaseRes, LwRequest, responseOk, responseError } from '../../../common/util/requestUtil';
import { EndOfTenancyStage } from '../../../contract/enum/eotStage';
import { ApiOkResponse, ApiOperation, ApiQuery, getSchemaPath } from '@nestjs/swagger';
import { Response } from 'express';
import { DashboardService } from '../../service/dashboard.service';
import { ResponseCode } from '../../../common/constant/responseCode';

@Controller("api/v1/dashboard")
export class DashboardController{

    private logger = new Logger('DashboardController', { timestamp: true });
    constructor(
        private dashboardService: DashboardService
    ) {}

    @Get('statics')
    async getDashboardStatics(@Req() req: LwRequest, @Res() res: Response) {
        try{
            const currentOrgId = req.user['custom:organisationId'];
            const currentUserId = req.user['sub'];
            const dashboardStatics = await this.dashboardService.getDashboardStatics(currentOrgId, currentUserId);
            responseOk(res, dashboardStatics);
        }catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
    
}