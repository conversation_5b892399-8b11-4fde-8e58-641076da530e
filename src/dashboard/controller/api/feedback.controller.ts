import { <PERSON>, Post, Body, Get, Logger, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { BaseRes, LwRequest, responseError, responseOk } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { ApiExtraModels, ApiCreatedResponse, ApiOkResponse, ApiOperation, ApiBody, getSchemaPath } from '@nestjs/swagger';
import { CreateFeedbackDTO } from '../../model/feedback.model';
import { FeedbackService } from '../../service/feedback.service';
import { FeedbackDTO } from '../../model/feedback.model';

@Controller('api/v1/feedback')
@ApiExtraModels(BaseRes, CreateFeedbackDTO)
export class FeedbackController {
    private logger = new Logger('FeedbackController', { timestamp: true });

    constructor(private feedbackService: FeedbackService) {}

    @ApiOperation({ summary: 'Create a new feedback' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(FeedbackDTO) },
                    },
                },
            ],
        },
    })
    @Post()
    public async createFeedback(@Body() payload: {feedbackText: string}, @Req() req: LwRequest, @Res() res: Response) {
        try {
            const { feedbackText } = payload;
            if (!feedbackText) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            const result = await this.feedbackService.createFeedback(feedbackText);
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
