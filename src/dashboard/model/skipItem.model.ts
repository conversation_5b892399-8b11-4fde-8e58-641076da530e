import { ApiProperty } from '@nestjs/swagger';

export class CreateSkipItemDto {
    @ApiProperty()
    itemId: string;

    @ApiProperty()
    type: string;

    @ApiProperty({required: false})
    userId: string;

    @ApiProperty({required: false})
    complianceType: string;

    toPrisma(): any {
        return {
          itemId: this.itemId,
          type: this.type,
          userId: this.userId,
          complianceType: this.complianceType
        };
      }
}
