import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { DashboardService } from './service/dashboard.service';
import { DashboardController } from './controller/api/dashboard.controller';
import { SkipController } from './controller/api/skip.controller';
import { FeedbackController } from './controller/api/feedback.controller';
import { ContractModule } from '../contract/contract.module';
import { PropertyModule } from '../property/property.module';
import { DocumentModule } from '../document/document.module';
import { SkipService } from './service/skip.service';
import { CommunicationModule } from '../communication/communication.module';
import { FeedbackService } from './service/feedback.service';
import { TaskModule } from '../task/task.module';

@Module({
    imports: [
        ContractModule,
        PropertyModule,
        DocumentModule,
        CommunicationModule,
        TaskModule,
    ],
    providers: [DashboardService, SkipService,FeedbackService],
    controllers: [<PERSON>boardC<PERSON>roll<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>er,FeedbackController],
})
export class DashboardModule {}
