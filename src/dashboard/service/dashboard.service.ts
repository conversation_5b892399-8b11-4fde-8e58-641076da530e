import { Injectable, Logger } from '@nestjs/common';
import { EotService } from '../../contract/service/eot.service';
import { PropertyService } from '../../property/service/property.service';
import { DocumentService } from '../../document/service/document.service';
import { SkipService } from '../service/skip.service';
import { ComplianceType } from '../enums/compliance.enum';
import { EndOfTenancyPageQueryDTO } from '../../contract/model/eot.model';
import { EndOfTenancyStage } from '../../contract/enum/eotStage';
import { getUserByCognitoId } from 'src/common/service/ddb.service';
import { CommunicationService } from '../../communication/service/communication.service';
import { TaskService } from '../../task/service/task.service';
import { DashboardType } from '../enums/dashboard.enum';

@Injectable()
export class DashboardService {
    private logger = new Logger('DashboardService', { timestamp: true });

    constructor(
        private documentService: DocumentService,
        private propertyService: PropertyService,
        private eotService: EotService,
        private skipService: SkipService,
        private communicationService: CommunicationService,
        private taskService: TaskService,
    ) {}

    public async getComplianceStatics(organisationId: string, userId: string) {
        const userInfo = await getUserByCognitoId(userId);
        const propertyList =
            await this.propertyService.searchOrgPropertiesByManagerIdAndTime(
                organisationId,
                userInfo.id,
            );
        const propertyIds = propertyList.map((property) => property.id);
        const propertyMap = new Map<string, string>();
        for (const property of propertyList) {
            propertyMap.set(property.id, property.name);
        }
        const complianceTypeList = Object.values(ComplianceType);
        const existPropertyComplianceList =
            await this.documentService.getPropertyCompliances(
                propertyIds,
                complianceTypeList,
            );
        const propertyComplianceMap = new Map<string, Set<string>>();
        const expiringComplianceList = [];
        for (const compliance of existPropertyComplianceList) {
            if (!propertyComplianceMap.has(compliance.documentPropertyId)) {
                propertyComplianceMap.set(
                    compliance.documentPropertyId,
                    new Set(),
                );
            }
            propertyComplianceMap
                .get(compliance.documentPropertyId)
                .add(compliance.type);
            if (
                complianceTypeList.includes(compliance.type) &&
                compliance.expiry &&
                new Date(compliance.expiry) <
                    new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000)
            ) {
                expiringComplianceList.push({
                    propertyId: compliance.documentPropertyId,
                    type: compliance.type,
                    propertyAddress: propertyMap.get(
                        compliance.documentPropertyId,
                    ),
                    todoType: 'expiring',
                });
            }
        }
        const missingComplianceList = [];
        for (const property of propertyList) {
            const existingCompliances =
                propertyComplianceMap.get(property.id) || new Set();
            const missingCompliances = complianceTypeList.filter(
                (type) => !existingCompliances.has(type),
            );

            for (const complianceType of missingCompliances) {
                missingComplianceList.push({
                    propertyId: property.id,
                    type: complianceType,
                    propertyAddress: property.name,
                    todoType: 'missing',
                });
            }
        }
        const complianceList = expiringComplianceList.concat(
            missingComplianceList,
        );
        const filterComplianceList =
            await this.skipService.filterSkippedDataOnCompliance(
                complianceList,
                DashboardType.COMPLIANCE,
                userId,
            );
        return filterComplianceList;
    }

    public async getEotStatics(organisationId: string, userId: string) {
        let queryDTO = new EndOfTenancyPageQueryDTO();
        queryDTO = {
            organisationId: organisationId,
            page: parseInt('1'),
            size: parseInt('100'),
            stageList: [
                EndOfTenancyStage.WAITING_TO_START,
                EndOfTenancyStage.LANDLORD_REPLIED,
                EndOfTenancyStage.TENANT_REPLIED,
            ],
        };
        const currentUserId = userId;
        const eotList = await this.eotService.getEotByPage(queryDTO);
        eotList.data = eotList.data.map((eot) => {
            return {
                eotId: eot.id,
                stage: eot.stage,
                endDate: eot.endDate,
                address: eot.address,
            };
        });
        const filterEotList: any = await this.skipService.filterSkippedData(
            eotList.data,
            DashboardType.END_OF_TENANCY,
            currentUserId,
            'eotId',
        );
        return filterEotList;
    }

    public async getConversationStatics(
        organisationId: string,
        userId: string,
    ) {
        this.logger.log(
            `getConversationStatics for organisationId: ${organisationId} and userId: ${userId}`,
        );
        const conversationList =
            await this.communicationService.queryConversationsDashboard(
                {
                    organisationId: organisationId,
                    unread: true,
                },
                userId,
            );
        const filterConversationList: any =
            await this.skipService.filterSkippedDataOnConversation(
                conversationList,
                DashboardType.COMMUNICATION,
                userId,
                'conversationId',
            );
        return filterConversationList;
    }

    public async getTaskStatics(organisationId: string, userId: string) {
        this.logger.log(
            `getTaskStatics for organisationId: ${organisationId} and userId: ${userId}`,
        );
        const taskList = await this.taskService.getTasksForDashboard(
            organisationId,
            userId,
        );
        const filterTaskList: any = await this.skipService.filterSkippedData(
            taskList,
            DashboardType.TASK,
            userId,
            'taskId',
        );
        return filterTaskList;
    }

    async getDashboardStatics(organisationId: string, userId: string) {
        try {
            const [complianceList, eotList, conversationList, taskList] =
                await Promise.all([
                    this.getComplianceStatics(organisationId, userId),
                    this.getEotStatics(organisationId, userId),
                    this.getConversationStatics(organisationId, userId),
                    this.getTaskStatics(organisationId, userId),
                ]);
            const result = {
                complianceList: complianceList,
                eotList: eotList,
                conversationList: conversationList,
                communicationCount: conversationList.length,
                complianceCount: complianceList.length,
                taskList: taskList,
                taskCount: taskList.length,
                eotCount: eotList.length,
            };
            return result;
        } catch (error) {
            this.logger.error(error);
            throw new Error('Database query failed');
        }
    }
}
