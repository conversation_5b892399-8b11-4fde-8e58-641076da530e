import { Injectable,Logger } from '@nestjs/common';
import { PrismaService } from "src/common/service/prisma.service";
import { DashboardSkipItem } from "@prisma/client";
import { v1 as uuidV1 } from 'uuid';

@Injectable()
export class SkipService {

    private logger = new Logger('SkipService', { timestamp: true });


    constructor(
        private prisma: PrismaService) {
    }

    async createSkipItem(dto: DashboardSkipItem) {
        try {
            return await this.prisma.$transaction(async (tx) => {
                // Check if item already exists
                const existingItem = await tx.dashboardSkipItem.findFirst({
                    where: {
                        userId: dto.userId,
                        itemId: dto.itemId,
                        type: dto.type,
                        ...(dto.complianceType && { complianceType: dto.complianceType })
                    },
                });

                if (existingItem) {
                    return tx.dashboardSkipItem.update({
                        where: { id: existingItem.id },
                        data: { updatedAt: new Date() },
                    });
                }

                dto.id = uuidV1();
                return tx.dashboardSkipItem.create({
                    data: dto,
                });
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db operation failed');
        }
    }

    async getSkipItemIdsByUserIdAndType(userId: string, type: string) {
        try {
            return await this.prisma.dashboardSkipItem.findMany({
                where: {
                    userId: userId,
                    type: type
                },
                select: {
                    itemId: true,
                    complianceType: true,
                    updatedAt: true
                }
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db query fails");
        }
    }


    async filterSkippedData<T>(
        data: T[],                 
        type: string,  
        userId: string,
        idKey: keyof T = "id" as keyof T       
      ): Promise<T[]> {
        const skippedItems = await this.getSkipItemIdsByUserIdAndType(userId, type);
        const skippedIds = skippedItems.map(item => item.itemId);
        return data.filter(item => !skippedIds.includes((item as any)[idKey]));
    }

    async filterSkippedDataOnConversation<T>(
        data: T[],
        type: string,
        userId: string,
        idKey: keyof T = 'id' as keyof T,
    ): Promise<T[]> {
        const skippedItems = await this.getSkipItemIdsByUserIdAndType(userId, type);
        // const skippedIds = skippedItems.map(item => item.itemId);
        return data.filter(
            (item) =>
                !skippedItems.some(
                    (skippedItem) =>
                        skippedItem.itemId === (item as any)[idKey] &&
                        new Date(skippedItem.updatedAt) >
                            new Date((item as any).lastMessageTime),
                ),
        );
    }

    async filterSkippedDataOnCompliance<T>(
        data: T[],                 
        type: string,  
        userId: string,
      ): Promise<T[]> {
        const skippedItems = await this.getSkipItemIdsByUserIdAndType(userId, type);
        const filteredData = data.filter((item:any) => {
            return !skippedItems.some(skippedItem => skippedItem.itemId === item.propertyId && skippedItem.complianceType === item.type);
        });
        return filteredData;
    }
      
}
