import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from "src/common/service/prisma.service";
import { CreateFeedbackDTO } from '../model/feedback.model';
import { v1 as uuidV1 } from 'uuid';
import fetch from 'node-fetch';
import * as dotenv from 'dotenv';
import { getParameter } from '../../common/service/config.service'

dotenv.config();

@Injectable()
export class FeedbackService {
    private logger = new Logger('FeedbackService', { timestamp: true });

    constructor(private prisma: PrismaService
    ) {}

    async createFeedback(
        text: string
    ): Promise<CreateFeedbackDTO>{
        try{
            const uuid = uuidV1();
            const feedback = await this.prisma.feedback.create({
                data: {
                    id: uuid,
                    text: text
                },
            });

            await this.createJiraIssue(feedback.text);

            return feedback;

        } catch (e){
            this.logger.error(e);
            throw new Error('insert failed');
        }
    }

    private async createJiraIssue(feedbackText: string) {
        const JIRA_API_TOKEN = await getParameter('/main-service/JIRA_API_TOKEN');
        const JIRA_EMAIL = process.env.JIRA_EMAIL;
        const JIRA_INSTANCE = process.env.JIRA_INSTANCE;
        const JIRA_PROJECT_KEY = process.env.JIRA_PROJECT_KEY;
        const JIRA_ISSUE_TYPE = process.env.JIRA_ISSUE_TYPE || 'Story';

        const auth = Buffer.from(`${JIRA_EMAIL}:${JIRA_API_TOKEN}`).toString('base64');
        const jiraApiUrl = `${JIRA_INSTANCE}/rest/api/3/issue`;

        if (!JIRA_API_TOKEN || !JIRA_EMAIL ||!JIRA_INSTANCE ||!JIRA_PROJECT_KEY) {
            this.logger.error('Missing Jira environment variables.');
            throw new Error('JIRA environment variables are not set.');
        }

        const bodyData = JSON.stringify({
            fields: {
                project: {
                    key: JIRA_PROJECT_KEY,
                },
                summary: `Feedback: ${feedbackText.substring(0, 50)}`,
                description: {
                    type: 'doc',
                    version: 1,
                    content: [
                        {
                            type: 'paragraph',
                            content: [
                                {
                                    type: 'text',
                                    text: feedbackText,
                                },
                            ],
                        },
                    ],
                },
                issuetype: {
                    name: JIRA_ISSUE_TYPE,
                },
            },
        });

        try {
            const response = await fetch(jiraApiUrl, {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'Authorization': `Basic ${auth}`,
                },
                body: bodyData,
            });

            if (!response.ok) {
                this.logger.error(`Error creating Jira issue: ${response.status} ${response.statusText}`);
                throw new Error('Failed to create Jira issue');
            }

            const responseData = await response.json();
            this.logger.log('Successfully created JIRA issue:', responseData);

        } catch (error) {
            this.logger.error(`Failed to create Jira issue: ${error.message}`);
            throw new Error('JIRA issue creation failed');
        }
    }
}