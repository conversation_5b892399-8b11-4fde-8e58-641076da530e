import {
    Body,
    Controller,
    Get,
    Logger,
    Put,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiBody,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { ENV_CONFIG } from '../../../common/constant/config.constant';
import { WebPushSubscriptionService } from '../../service/webPushSubscription.service';
import { WebPushSubscription } from '../../../common/model/subscriptions';
import { DeviceDTO } from '../../model/subscriptions';

@Controller('api/v1/web-push-subscription')
@ApiExtraModels(BaseRes)
@ApiBearerAuth()
export class WebPushSubscriptionController {
    private readonly logger = new Logger('WebPushSubscriptionController', {
        timestamp: true,
    });

    constructor(
        private readonly webPushSubscriptionService: WebPushSubscriptionService,
    ) {}

    @ApiOperation({ summary: 'Get public vapid key' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'string',
                        },
                    },
                },
            ],
        },
    })
    @Get('/public-vapid-key')
    public async getPublicVapidKey(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            responseOk(res, ENV_CONFIG.VAPID_PUBLIC_KEY);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Register Subscription' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'string',
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        type: WebPushSubscription,
    })
    @Put('/register/subscription')
    public async registerSubscription(
        @Body() webPushSubscription: WebPushSubscription,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const userId =
                await this.webPushSubscriptionService.saveSubscription(
                    req.user.sub,
                    webPushSubscription,
                );
            responseOk(res, userId);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Register UserDevice' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'boolean',
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        type: DeviceDTO,
    })
    @Put('/register/user-device')
    public async registerUserDevice(
        @Body() deviceDTO: DeviceDTO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.webPushSubscriptionService.registerUserDevice(
                    req.user.sub,
                    deviceDTO,
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
