import { Injectable, Logger } from '@nestjs/common';
import { WebPushSubscription } from '../../common/model/subscriptions';
import { SubscriptionsRepository } from '../../common/repository/subscriptions';
import { DeviceDTO } from '../model/subscriptions';
import { UserRepository } from '../../common/repository/user';
import { ENV_CONFIG } from '../../common/constant/config.constant';
import ADMIN from 'firebase-admin';

@Injectable()
export class WebPushSubscriptionService {
    private readonly logger = new Logger('WebPushSubscriptionService', {
        timestamp: true,
    });

    constructor(
        private readonly subscriptionsRepository: SubscriptionsRepository,
        private readonly userRepository: UserRepository,
    ) {
        try {
            ADMIN.initializeApp({
                credential: ADMIN.credential.cert(
                    JSON.parse(ENV_CONFIG.GOOGLE_APPLICATION_CREDENTIALS),
                ),
            });
        } catch (e) {
            this.logger.error(e.stack);
        }
    }

    async saveSubscription(
        cognitoId: string,
        subscription: WebPushSubscription,
    ) {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        await this.subscriptionsRepository.createItem({
            userId: user.id,
            subscription,
        });
        return user.id;
    }

    async registerUserDevice(cognitoId: string, deviceDTO: DeviceDTO) {
        const { deviceId, authToken } = deviceDTO;
        const user = await this.userRepository.findByCognitoId(cognitoId);
        return await this.createMapping(user.id, authToken, deviceId);
    }

    private async createMapping(userId, authToken, deviceId) {
        const db = ADMIN.firestore();
        const userDevices = db
            .collection(`devices-${ENV_CONFIG.ENV}`)
            .doc(deviceId);
        await userDevices.set({
            userId,
            authToken,
        });

        return true;
    }
}
