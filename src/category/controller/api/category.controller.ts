import { <PERSON>, <PERSON>, Logger, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { CategoryService } from '../../service/category.service';
import { Category } from '../../../common/model/category';

@Controller('api/v1/category')
@ApiExtraModels(BaseRes, Category)
@ApiBearerAuth()
export class CategoryController {
    private readonly logger = new Logger('CategoryController', {
        timestamp: true,
    });

    constructor(private readonly categoryService: CategoryService) {}

    @ApiOperation({ summary: 'List Categories' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Category),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/list')
    public async listCategories(@Req() req: LwRequest, @Res() res: Response) {
        try {
            const result = await this.categoryService.listCategories();
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'List Categories By roomId' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(Category),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/list/by-room')
    public async listCategoriesByRoomId(
        @Query('roomId') roomId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.categoryService.listCategoriesByRoomId(roomId);
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
