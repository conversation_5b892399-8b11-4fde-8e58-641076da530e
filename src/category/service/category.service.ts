import { Injectable, Logger } from '@nestjs/common';
import { CateRepository } from '../../common/repository/category';
import { Category } from '../../common/model/category';

@Injectable()
export class CategoryService {
    private readonly logger = new Logger('CategoryService', {
        timestamp: true,
    });

    constructor(private readonly categoryRepository: CateRepository) {}

    async listCategories(): Promise<Category[]> {
        return this.categoryRepository.list();
    }

    async listCategoriesByRoomId(roomId: string): Promise<Category[]> {
        const result = await this.categoryRepository.listByRoomId(roomId);
        return result.sort(function compare(a, b) {
            return a.index - b.index;
        });
    }
}
