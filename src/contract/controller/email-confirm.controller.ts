import { Body, Controller, Delete, Get, Logger, Param, Post, Put, Query, Req, Res } from '@nestjs/common';
import { responseError, responseOk } from '../../common/util/requestUtil';
import { Response } from 'express';
import { EotService } from '../service/eot.service';
import { ResponseCode } from '../../common/constant/responseCode';
import { EndOfTenancyStage } from '../enum/eotStage';
import { UpdateEndOfTenancyDto} from '../model/eot.model';
import { getProperty } from 'src/common/service/ddb.service';

@Controller("/api/public/eot-email-confirm")
export class EmailConfirmController {
    private logger = new Logger('EotController', { timestamp: true });
    constructor(
        private eotService: EotService
    ) {}

    @Get()
    public async confirmInfo(@Query('token') token: string, @Res() res: Response) {
        this.logger.log(`confirmInfo: eotId=${token}`);
        try {
            let eotInfo:any = await this.eotService.getEotById(token);
            if ( (eotInfo.stage === EndOfTenancyStage.AWAITING_LANDLORD_REPLY) ||
                 (eotInfo.stage === EndOfTenancyStage.AWAITING_TENANT_REPLY) ) {
                const property = await getProperty(eotInfo.propertyId);
                console.log(`eotInfo: property: ${JSON.stringify(property, null, 2)}}`);
                eotInfo.propertyImages = property.images;
                responseOk(res, eotInfo);
            } else {
                responseError(400, res,  new ResponseCode(400, "current stage is not permitted confirm."));
            }            
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Post()
    public async emailConfirm(@Body() updateData: any, @Res() res: Response) {
        console.log(`body of emailConfirm: ${JSON.stringify(updateData)}`);
        const { token:id, stage} = updateData;
        delete(updateData.token);
        delete(updateData.stage);
        const landlordConfirmation = stage === EndOfTenancyStage.LANDLORD_REPLIED ? JSON.stringify(updateData) : undefined;
        const tenantConfirmation = stage === EndOfTenancyStage.TENANT_REPLIED ? JSON.stringify(updateData) : undefined;
        const updateEotInfo = { id, stage, landlordConfirmation, tenantConfirmation } as UpdateEndOfTenancyDto;
        console.log(`eotId: ${id}, stage: ${stage}, confirmation: ${JSON.stringify(updateEotInfo)}`);
        try {
            const eotInfo:any = await this.eotService.getEotById(id);
            if( (eotInfo.stage === EndOfTenancyStage.AWAITING_LANDLORD_REPLY && stage === EndOfTenancyStage.LANDLORD_REPLIED) ||
                (eotInfo.stage === EndOfTenancyStage.AWAITING_TENANT_REPLY   && stage === EndOfTenancyStage.TENANT_REPLIED)
            ) {
                const update:any = await this.eotService.updateEot(updateEotInfo);
                responseOk(res, update);
            } else {
                responseError(400, res,  new ResponseCode(400, "current stage is not permitted confirm."));
            }
        } catch(e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
