import {
    Body,
    Controller,
    Logger,
    Param,
    Put,
    Res,
} from '@nestjs/common';
import { Response } from 'express';
import { EotService } from '../../service/eot.service';
import { responseOk } from '../../../common/util/requestUtil';
import {
    ApiOperation,
    ApiParam,
    ApiResponse,
} from '@nestjs/swagger';
import { UpdateEndOfTenancyDto } from 'src/contract/model/eot.model';

@Controller('eot')
@ApiResponse({ status: 200, description: 'Success' })
@ApiResponse({ status: 400, description: 'Bad Request' })
@ApiResponse({ status: 500, description: 'Server Error' })
export class EotInternalController {
    private logger = new Logger('DocumentInternalController', {
        timestamp: true,
    });
    constructor(private eotService: EotService) {}

    @ApiOperation({ summary: 'Update eot by Property ID' })
    @ApiParam({
        name: 'id',
        description: 'property ID',
    })
    @Put('properties/:id')
    public async updateEotByPropertyId(
        @Param('id') propertyId: string,
        @Body() eotUpdateDTO: UpdateEndOfTenancyDto,
        @Res() res: Response,
    ) {
        const update = await this.eotService.updateEotByPropertyId(propertyId, this.convertDynamoDBToJSON(eotUpdateDTO));
        responseOk(res, update);
    }

    private convertDynamoDBToJSON(dynamoDBItem: any) {
        const json = {};
      
        for (const key in dynamoDBItem) {
          if (dynamoDBItem.hasOwnProperty(key)) {
            const value = dynamoDBItem[key];
            if (typeof value === 'object' && value !== null && value.S) {
              json[key] = value.S; 
            } else if (typeof value === 'object' && value !== null && value.N) {
              json[key] = Number(value.N); 
            } else if (typeof value === 'object' && value !== null && value.B) {
              json[key] = value.B; 
            } else {
              json[key] = value;
            }
          }
        }
      
        return json;
      }

}
