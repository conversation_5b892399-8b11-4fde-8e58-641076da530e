import { Body, Controller, Delete, Get, Logger, Param, Post, Put, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { EotService } from '../../service/eot.service';
import { BaseRes, LwRequest, responseError, responseOk } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { EndOfTenancy, EndOfTenancyPageQueryVO, UpdateEndOfTenancyDto, EndOfTenancyPageQueryDTO} from '../../model/eot.model';
import { ApiExtraModels, ApiOkResponse, ApiOperation, ApiQuery, ApiBody, getSchemaPath } from '@nestjs/swagger';
import { getUser,getEotLetter,getUserByCognitoId,getTaskByIds } from 'src/common/service/ddb.service';
import { EndOfTenancyStage } from 'src/contract/enum/eotStage';
import { invokeLambda } from '../../../common/util/invokeLambda';
import { EndOfTenancyStatus } from 'src/contract/enum/eotStatus';
import {LandlordConfirmationLetter, TenantConfirmationLetter, TerminationLetterforTenant, TerminationLetterforLandlord, ExtensionLetterforTenant, ExtensionLetterforLandlord}  from "../../../common/util/defaultEotLetterTemplate";
const moment = require('moment');

@Controller("api/v1/endOfTenancy")
@ApiExtraModels(BaseRes, EndOfTenancy, EndOfTenancyPageQueryVO)
export class EotController{
    private logger = new Logger('EotController', { timestamp: true });
    constructor(
        private eotService: EotService
    ) {}

    @ApiOperation({ summary: 'Get Eot by id' })
    @ApiQuery({
        name: 'eotId',
        description: 'end of tenancy ID',
    })
    @ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(BaseRes) },
            {
              properties: {
                data: { $ref: getSchemaPath(EndOfTenancy) },
              },
            },
          ],
        },
    })
    @Get()
    public async getEotById(@Query('eotId') eotId: string,
                            @Query('tenancyId') tenancyId: string,
                            @Req() req: LwRequest, @Res() res: Response) {
        if (!eotId && !tenancyId) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            if (eotId) {
              var eotInfo:any = await this.eotService.getEotById(eotId);
            }else if(tenancyId){
              var eotInfo:any = await this.eotService.getEotByTenancy(tenancyId);
              if(!eotInfo){
                responseOk(res, eotInfo);
                return;
              }
            }
            const currentOrgId = req.user['custom:organisationId'];
            const currentUserId = req.user['sub'];
            if (eotInfo.organisationId !== currentOrgId) {
                responseError(404, res, ResponseCode.NOT_FOUND);
                return;
            }
            const landlordId = eotInfo?.landlordId;
            const [landlordInfo, pmInfo, tenantInfo] = await Promise.all([
                getUser(landlordId),
                getUserByCognitoId(currentUserId),
                getUser(eotInfo.tenantId)
            ]);
            eotInfo.landlordEmail = landlordInfo?.denormalizedEmails;
            if(eotInfo.stage == EndOfTenancyStage.WAITING_TO_START){
              const landlordLetterTemplate = await getEotLetter(currentOrgId, "LANDLORD", "End of Tenancy", "Landlord Confirmation Letter") ?? LandlordConfirmationLetter;
              if (landlordLetterTemplate) {
                const letterData = this.buildLetter(landlordLetterTemplate, pmInfo.denormalizedEmails, eotInfo.landlordEmail);
                if (eotInfo?.landlordLetter) {
                  const landlordLetterJson = JSON.parse(eotInfo?.landlordLetter);
                  if (Object.keys(landlordLetterJson).length === 1) {
                    eotInfo.landlordLetter = JSON.stringify(letterData);
                  }
                } else {
                  eotInfo.landlordLetter = JSON.stringify(letterData);
                }
              }
            }
            if(eotInfo.stage == EndOfTenancyStage.LANDLORD_REPLIED){
              const tenantLetterTemplate = await getEotLetter(currentOrgId, "TENANT", "End of Tenancy", "Tenant Confirmation Letter") ?? TenantConfirmationLetter;
              if(tenantLetterTemplate){
                if (tenantLetterTemplate) {
                  const letterData = this.buildLetter(tenantLetterTemplate, pmInfo.denormalizedEmails, tenantInfo?.denormalizedEmails);
                  if (eotInfo?.tenantLetter) {
                    const tenantLetterJson = JSON.parse(eotInfo?.tenantLetter);
                    if (Object.keys(tenantLetterJson).length === 1) {
                      eotInfo.tenantLetter = JSON.stringify(letterData);
                    }
                  } else {
                    eotInfo.tenantLetter = JSON.stringify(letterData);
                  }
                }
              }
            }
            if(eotInfo.stage == EndOfTenancyStage.TENANT_REPLIED){
              const finalLetterTemplate = await getEotLetter(currentOrgId, "LANDLORD", "End of Tenancy", "Termination Letter for Landlord") ?? TerminationLetterforLandlord;
              if (finalLetterTemplate) {
                const letterData = this.buildLetter(finalLetterTemplate, pmInfo.denormalizedEmails, eotInfo.landlordEmail);
                if (eotInfo?.finalLetter) {
                  const finalLetterJson = JSON.parse(eotInfo?.finalLetter);
                  if (Object.keys(finalLetterJson).length === 1) {
                    eotInfo.finalLetter = JSON.stringify(letterData);
                  }
                } else {
                  eotInfo.finalLetter = JSON.stringify(letterData);
                }
              }
              
            }
            if(eotInfo?.taskIds?.length>0){
              const uniqueTaskIds: string[] = [...new Set(eotInfo.taskIds as string[])];
              const taskList = await getTaskByIds(uniqueTaskIds);
              eotInfo.taskInfos = taskList.map((task)=>{
                return {
                  id: task.id,
                  name: task.name,
                }
              })
            }
            responseOk(res, eotInfo);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

  private buildLetter(letterTemplate: any, from: string, to: string) {
    return {
      letterName: letterTemplate?.letterName,
      contactType: letterTemplate?.contactType,
      category: letterTemplate?.category,
      letterSubject: letterTemplate?.letterSubject,
      letterContent: letterTemplate?.letterContent,
      from: from,
      to: to
    };
  }

    @ApiOperation({ summary: 'Get eot by page' })
    @ApiQuery({
        name: 'page',
        description: 'Page number',
    })
    @ApiQuery({
        name: 'size',
        description: 'Page size',
    })
    @ApiQuery({
        name: 'keyword',
        description: 'keyword',
        required: false,
    })
    @ApiQuery({
        name: 'fromEndDate',
        description: 'fromEndDate',
        required: false,
    })
    @ApiQuery({
      name: 'toEndDate',
      description: 'toEndDate',
      required: false,
    })
    @ApiQuery({
        name: 'status',
        description: '1: active 2: pending 3: archived',
        required: false,
    })
    @ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(BaseRes) },
            {
              properties: {
                data: { type: 'array', items: { $ref: getSchemaPath(EndOfTenancyPageQueryVO) } },
              },
            },
          ],
        },
    })
    @Get('page')
    public async getEndOfTenancyByPage(
        @Query('page') page: string,
        @Query('size') size: string,
        @Req() req: LwRequest, 
        @Res() res: Response,
        @Query('keyword') keyword?: string,
        @Query('fromEndDate') fromEndDate?: string,
        @Query('toEndDate') toEndDate?: string,
        @Query('status') status?: string
    ) {
      if (!page || !size) {
        responseError(400, res, ResponseCode.PARAM_INVALID);
        return;
      }
      try {
        let queryDTO = new EndOfTenancyPageQueryDTO();
        queryDTO = {
            organisationId: req.user['custom:organisationId'],
            page: parseInt(page),
            size: parseInt(size),
            keyword: keyword,
            fromEndDate: fromEndDate,
            toEndDate: toEndDate,
            status: status
        }

        const eotList = await this.eotService.getEotByPage(queryDTO);
        responseOk(res, eotList);
      } catch (e) {
        this.logger.error(e);
        responseError(500, res, ResponseCode.SERVER_ERROR);
      }
    }

    @ApiOperation({ summary: 'Get eot by page' })
    @ApiQuery({
        name: 'page',
        description: 'Page number',
    })
    @ApiQuery({
        name: 'size',
        description: 'Page size',
    })
    @ApiQuery({
        name: 'keyword',
        description: 'keyword',
        required: false,
    })
    @ApiQuery({
        name: 'fromEndDate',
        description: 'fromEndDate',
        required: false,
    })
    @ApiQuery({
      name: 'toEndDate',
      description: 'toEndDate',
      required: false,
    })
    @ApiQuery({
        name: 'status',
        description: '1: active 2: pending 3: archived',
        required: false,
    })
    @ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(BaseRes) },
            {
              properties: {
                data: { type: 'array', items: { $ref: getSchemaPath(EndOfTenancyPageQueryVO) } },
              },
            },
          ],
        },
    })
    // @Get('previewedEmail')
    @Post('previewedEmail')
    public async previewedEmail(
        // @Query('eotId') eotId: string,
        // @Query('letter') letter: string,
        @Body() previewedInfo: any,
        @Req() req: LwRequest, 
        @Res() res: Response,
    ) {
      const {eotId, letter} = previewedInfo;
      if (!eotId || !letter) {
        responseError(400, res, ResponseCode.PARAM_INVALID);
        return;
      }
      try {
        let organisationId = req.user['custom:organisationId'];
        this.logger.log(`organisationId: ${organisationId}`);
        const currentEotInfo = await this.eotService.getEotById(eotId);
        let queryStringParameters = {
                organisationId: currentEotInfo.organisationId,
                userId: currentEotInfo.createdBy,
                propertyId: currentEotInfo.propertyId,
                tenancyId: currentEotInfo.tenancyId,
                landlordId: currentEotInfo.landlordId,
                eotId: currentEotInfo.id,
                EndOfTenancy_Status: currentEotInfo.status,
                Rent_Increase_Amount: Number(currentEotInfo.proposalPrice) - Number(currentEotInfo.oldPrice),
                templateContent: JSON.stringify(letter),
        };
        if( currentEotInfo.stage === EndOfTenancyStage.AWAITING_TENANT_REPLY) {
            const landlordConfirmation = JSON.parse(currentEotInfo.landlordConfirmation);
            const contractParmas = {
                Contract_Renewal_Duration: landlordConfirmation.renewPeriod,
                Contract_Renewal_Pending: currentEotInfo.status ===  EndOfTenancyStatus.PENDING,
                Contract_Renewal_False: !(!! landlordConfirmation.ifRenew),                     
            };
            queryStringParameters = {...queryStringParameters, ...contractParmas};
        }
        const renderParams = { queryStringParameters };
        const renderLambda = `rentancy-core-${process.env.STAGE ?? 'devuk'}-end-of-tenancy-letter-content`;
        console.log(`renderLambda: ${renderLambda}, renderParams:${JSON.stringify(renderParams, null, 2)}`);
        const result = await invokeLambda(renderLambda, renderParams);
        const {letter:letterFull, missingVariables} =  JSON.parse(result);
        const {from,to,letterSubject,letterContent} = JSON.parse(letterFull);    
        this.logger.log(`send email: ${JSON.stringify({from, to, letterSubject, letterContent})}`);
        if (missingVariables && missingVariables.length > 0) {
            this.logger.log(`missingVariables: ${JSON.stringify(missingVariables)}`);
        }
        responseOk(res, {from, to, letterSubject, letterContent});
        // responseOk(res, {test: "ok"});
      } catch (e) {
        this.logger.error(e);
        responseError(500, res, ResponseCode.SERVER_ERROR);
      }
    }

    @ApiOperation({ summary: 'Update end of tenancy' })
    @ApiBody({
        type: UpdateEndOfTenancyDto,
        description: 'Data transfer object to update end of tenancy',
    })
    @ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(BaseRes) },
            {
              properties: {
                data: { $ref: getSchemaPath(EndOfTenancy) },
              },
            },
          ],
        },
    })
    @Put()
    public async updateEndOfTenancy(
        @Query('eotId') eotId: string,
        @Body() updateData: UpdateEndOfTenancyDto,
        @Req() req: LwRequest,
        @Res() res: Response
    ) {
        if (!eotId) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
          const item:any = await this.eotService.getEotById(updateData.id)
          if (item.organisationId !== req.user['custom:organisationId']) {
              responseError(404, res, ResponseCode.NOT_FOUND);
          }
          let updateEotInfo = { ...updateData } as EndOfTenancy;
          if(updateEotInfo.stage === EndOfTenancyStage.AWAITING_LANDLORD_REPLY){
            const currentUserId = req.user['sub'];
            const currentUserInfo = await getUserByCognitoId(currentUserId);
            updateEotInfo.createdBy = currentUserInfo.id;
          }
          const update = await this.eotService.updateEot(updateEotInfo);
          responseOk(res, update);
      } catch (e) {
          this.logger.error(e);
          responseError(500, res, ResponseCode.SERVER_ERROR);
      }
    }


}
