import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/common/service/prisma.service';
import { EndOfTenancy } from '@prisma/client';
import { Page } from 'src/common/util/requestUtil';
import {
    EndOfTenancyPageQueryDTO,
    UpdateEndOfTenancyDto,
} from '../model/eot.model';
import { EndOfTenancyStage } from '../enum/eotStage';
import { EndOfTenancyStatus } from '../enum/eotStatus';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EotEvent } from '../event/eotEvent';
import { sendEmail } from '../../common/util/requestUtil';
import { invokeLambda } from '../../common/util/invokeLambda';
import { getOrganisation } from 'src/common/service/ddb.service';
import {
    createLandlordRespondedEvent,
    createTenancyEndedEvent,
    createTenantRespondedEvent,
} from '@rentancy-com/loftyworks-events';
import { EventsEmitterService } from '../../common/service/eventsEmitter.service';

@Injectable()
export class EotService {
    private logger = new Logger('EotService', { timestamp: true });

    constructor(
        private prisma: PrismaService,
        private readonly eventsEmitterInternal: EventEmitter2,
        private readonly eventsEmitterPropertyMgmt: EventsEmitterService,
    ) {}

    async getEotById(eotId: string): Promise<EndOfTenancy> {
        try {
            return await this.prisma.endOfTenancy.findUnique({
                where: {
                    id: eotId,
                },
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async getEotByTenancy(tenancyId: string): Promise<EndOfTenancy> {
        try {
            return await this.prisma.endOfTenancy.findFirst({
                where: {
                    tenancyId: tenancyId,
                },
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async getAllTenancyIdByOrganisationId(
        organisationId: string,
    ): Promise<any> {
        try {
            const filters: any = {};
            filters.organisationId = {
                equals: organisationId,
            };
            return await this.prisma.endOfTenancy.findMany({
                where: filters,
                select: {
                    tenancyId: true,
                },
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async getExpiredEotList(): Promise<any> {
        try {
            const filters: any = {};
            filters.stage = {
                equals: EndOfTenancyStage.WAITING_TO_START,
            };
            filters.status = {
                equals: EndOfTenancyStatus.ACTIVE,
            };
            filters.endDate = {
                lte: new Date(),
            };
            return await this.prisma.endOfTenancy.findMany({
                where: filters,
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async getCompletedAndExpiredEotList(): Promise<any> {
        try {
            const filters: any = {};
            filters.stage = {
                equals: EndOfTenancyStage.COMPLETED,
            };
            filters.status = {
                equals: EndOfTenancyStatus.ARCHIVED,
            };
            filters.endDate = {
                lte: new Date(),
            };
            return await this.prisma.endOfTenancy.findMany({
                where: filters,
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async getEotByPage(queryDTO: EndOfTenancyPageQueryDTO): Promise<Page> {
        try {
            const filters: any = {};
            filters.organisationId = {
                equals: queryDTO.organisationId,
            };
            if (queryDTO.keyword) {
                filters.address = {
                    contains: queryDTO.keyword,
                };
            }
            if (queryDTO.status) {
                filters.status = {
                    equals: queryDTO.status,
                };
            }
            if (queryDTO.stageList) {
                filters.stage = {
                    in: queryDTO.stageList,
                };
            }
            if (queryDTO.fromEndDate) {
                filters.endDate = {
                    gte: new Date(queryDTO.fromEndDate),
                };
            }
            if (queryDTO.toEndDate) {
                filters.endDate = {
                    lte: new Date(queryDTO.toEndDate),
                };
            }
            const [total, data] = await Promise.all([
                this.prisma.endOfTenancy.count({
                    where: filters,
                }),
                this.prisma.endOfTenancy.findMany({
                    where: filters,
                    skip: (queryDTO.page - 1) * queryDTO.size,
                    take: queryDTO.size,
                }),
            ]);
            return new Page(total, data);
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async createEot(eotDto: EndOfTenancy): Promise<EndOfTenancy> {
        try {
            return await this.prisma.endOfTenancy.create({
                data: eotDto,
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db insert fails');
        }
    }

    async batchCreateEot(eotDtoList: any[]): Promise<any> {
        try {
            return await this.prisma.endOfTenancy.createMany({
                data: eotDtoList,
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db insert fails');
        }
    }

    async updateEot(eotDto: UpdateEndOfTenancyDto): Promise<EndOfTenancy> {
        try {
            const currentEotInfo = await this.prisma.endOfTenancy.findUnique({
                where: {
                    id: eotDto.id,
                },
            });
            const organisationInfo = await getOrganisation(
                currentEotInfo.organisationId,
            );
            const organisationType = organisationInfo.type;
            await this.stageHandlers(eotDto, currentEotInfo, organisationType);
            if (eotDto.taskInfos) {
                const taskIds: string[] = eotDto.taskInfos.map(
                    (task) => task.id,
                );
                eotDto.taskIds = taskIds;
                delete eotDto.taskInfos;
            }
            const updateResult = await this.prisma.endOfTenancy.update({
                where: {
                    id: eotDto.id,
                },
                data: eotDto,
            });
            // emit domain-wide events for Property Management using EventBridge
            await this.emitDomainEvents(eotDto, currentEotInfo);

            const event = new EotEvent(eotDto.id, updateResult);
            // emit internal events for loftyworks-main-service
            this.eventsEmitterInternal.emit(eotDto.stage, event);

            if (
                eotDto.stage === EndOfTenancyStage.AWAITING_LANDLORD_REPLY ||
                eotDto.stage === EndOfTenancyStage.AWAITING_TENANT_REPLY ||
                (eotDto.stage === EndOfTenancyStage.TERMINATED &&
                    currentEotInfo?.tenantConfirmation)
            ) {
                await this.sendNotifyEmail(eotDto, currentEotInfo);
            }

            return updateResult;
        } catch (e) {
            this.logger.error(e);
            throw new Error('db update fails');
        }
    }

    private async emitDomainEvents(eot: UpdateEndOfTenancyDto, eotInfo: any) {
        const domainEvents = [];
        switch (eot.stage) {
            case EndOfTenancyStage.LANDLORD_REPLIED:
                domainEvents.push(
                    createLandlordRespondedEvent({
                        propertyId: eotInfo.propertyId,
                        organisationId: eotInfo.organisationId,
                        actorId: eotInfo.landlordId,
                        subjectId: eotInfo.tenancyId,
                        eotId: eot.id,
                    }),
                );
                break;
            case EndOfTenancyStage.TENANT_REPLIED:
                domainEvents.push(
                    createTenantRespondedEvent({
                        propertyId: eotInfo.propertyId,
                        organisationId: eotInfo.organisationId,
                        actorId: eotInfo.tenantId,
                        subjectId: eotInfo.tenancyId,
                        eotId: eot.id,
                    }),
                );
                break;
            case EndOfTenancyStage.EXPIRED:
                domainEvents.push(
                    createTenancyEndedEvent({
                        propertyId: eotInfo.propertyId,
                        organisationId: eotInfo.organisationId,
                        targetId: eot.id,
                        actorId: eotInfo.tenantId,
                    }),
                );
                break;
        }

        return this.eventsEmitterPropertyMgmt.putEvents(domainEvents);
    }

    private async sendNotifyEmail(
        eotDto: UpdateEndOfTenancyDto,
        currentEotInfo: {
            endDate: Date;
            id: string;
            tenancyId: string;
            propertyId: string;
            address: string;
            addressLine2: string;
            addressLine3: string;
            city: string;
            country: string;
            postcode: string;
            landlordId: string;
            oldPrice: string;
            proposalPrice: string;
            finalPrice: string;
            finalTerm: string;
            landlordLetter: string;
            landlordConfirmation: string;
            tenantId: string;
            tenantLetter: string;
            tenantConfirmation: string;
            finalConfirmation: string;
            primaryTenantName: string;
            finalLetter: string;
            stage: string;
            status: string;
            organisationId: string;
            reference: string;
            taskIds: string[];
            createdBy: string;
            createdAt: Date;
            updatedAt: Date;
        },
    ) {
        let queryStringParameters = {
            organisationId: eotDto.organisationId,
            userId: currentEotInfo.createdBy,
            propertyId: currentEotInfo.propertyId,
            tenancyId: currentEotInfo.tenancyId,
            landlordId: currentEotInfo.landlordId,
            eotId: eotDto.id,
            EndOfTenancy_Status: eotDto.status,
            Rent_Increase_Amount:
                Number(currentEotInfo.proposalPrice) -
                Number(currentEotInfo.oldPrice),
            templateContent: eotDto.landlordLetter,
        };
        if (eotDto.stage === EndOfTenancyStage.AWAITING_TENANT_REPLY) {
            const landlordConfirmation = JSON.parse(
                eotDto.landlordConfirmation,
            );
            queryStringParameters = {
                ...queryStringParameters,
                //@ts-ignore: error TS2353: Object literal may only specify known properties, and 'Contract_Renewal_Duration' does not exist in type
                Contract_Renewal_Duration: landlordConfirmation.renewPeriod,
                Contract_Renewal_Pending:
                    eotDto.status === EndOfTenancyStatus.PENDING,
                Contract_Renewal_False: !!!landlordConfirmation.ifRenew,
            };
            queryStringParameters.templateContent = eotDto.tenantLetter;
        }
        if (
            eotDto.stage === EndOfTenancyStage.TERMINATED &&
            currentEotInfo?.tenantConfirmation
        ) {
            queryStringParameters.templateContent = eotDto.finalLetter;
        }
        const renderParams = { queryStringParameters };
        const renderLambda = `rentancy-core-${process.env.STAGE ?? 'devuk'}-end-of-tenancy-letter-content`;
        console.log(
            `renderLambda: ${renderLambda}, renderParams:${JSON.stringify(renderParams, null, 2)}`,
        );
        const result = await invokeLambda(renderLambda, renderParams);
        const { letter, missingVariables } = JSON.parse(result);
        const { from, to, letterSubject, letterContent } = JSON.parse(letter);
        this.logger.log(
            `send email: ${JSON.stringify({ from, to, letterSubject, letterContent })}`,
        );
        if (missingVariables && missingVariables.length > 0) {
            this.logger.log(
                `missingVariables: ${JSON.stringify(missingVariables)}`,
            );
        }
        await sendEmail(from, to, letterSubject, letterContent);
    }

    async stageHandlers(
        eotDto: UpdateEndOfTenancyDto,
        currentEotInfo: any,
        organisationType: string,
    ) {
        if (eotDto.stage == EndOfTenancyStage.AWAITING_LANDLORD_REPLY) {
            eotDto.status = EndOfTenancyStatus.PENDING;
        } else if (eotDto.stage == EndOfTenancyStage.LANDLORD_REPLIED) {
            const landlordConfirmJson: any = JSON.parse(
                eotDto.landlordConfirmation,
            );
            if (landlordConfirmJson?.ifAccept) {
                eotDto.finalPrice = currentEotInfo.proposalPrice;
                landlordConfirmJson.expectedProposalRent =
                    currentEotInfo.proposalPrice;
                eotDto.landlordConfirmation =
                    JSON.stringify(landlordConfirmJson);
            } else {
                eotDto.finalPrice = landlordConfirmJson.expectedProposalRent;
            }
            if (!landlordConfirmJson?.ifRenew) {
                eotDto.stage = EndOfTenancyStage.TERMINATED;
                eotDto.status = EndOfTenancyStatus.ARCHIVED;
                eotDto.finalTerm = landlordConfirmJson.renewPeriod;
            } else {
                eotDto.status = EndOfTenancyStatus.PENDING;
            }
        } else if (eotDto.stage == EndOfTenancyStage.AWAITING_TENANT_REPLY) {
            eotDto.status = EndOfTenancyStatus.PENDING;
        } else if (eotDto.stage == EndOfTenancyStage.TENANT_REPLIED) {
            const tenantConfirmationJson: any = JSON.parse(
                eotDto.tenantConfirmation,
            );
            if (tenantConfirmationJson?.ifAcceptPrice) {
                tenantConfirmationJson.expectedIncrease = '0';
                eotDto.tenantConfirmation = JSON.stringify(
                    tenantConfirmationJson,
                );
            } else {
                eotDto.finalPrice = (
                    Number(currentEotInfo.oldPrice) +
                    Number(tenantConfirmationJson.expectedIncrease)
                ).toString();
            }
            eotDto.status = EndOfTenancyStatus.PENDING;
        } else if (eotDto.stage == EndOfTenancyStage.COMPLETED) {
            if (eotDto.finalConfirmation) {
                const finalConfirmationJson: any = JSON.parse(
                    eotDto.finalConfirmation,
                );
                if (
                    !finalConfirmationJson?.ifRenew &&
                    !finalConfirmationJson?.finalStatus
                ) {
                    eotDto.stage = EndOfTenancyStage.TERMINATED;
                    eotDto.status = EndOfTenancyStatus.ARCHIVED;
                    return;
                }
            }
            //if use landlord account to complete eot, then update final price
            this.logger.log(`organisationType: ${organisationType}`);
            if (organisationType === 'LANDLORD') {
                const landlordConfirmJson: any = JSON.parse(
                    eotDto.landlordConfirmation,
                );
                eotDto.finalPrice = landlordConfirmJson.expectedProposalRent;
                eotDto.finalTerm = landlordConfirmJson.renewPeriod;
            } else {
                if (eotDto.finalConfirmation) {
                    const finalConfirmationJson: any = JSON.parse(
                        eotDto.finalConfirmation,
                    );
                    if (
                        !finalConfirmationJson?.ifRenew &&
                        !finalConfirmationJson?.finalStatus
                    ) {
                        eotDto.stage = EndOfTenancyStage.TERMINATED;
                        eotDto.status = EndOfTenancyStatus.ARCHIVED;
                        return;
                    }
                }
                const finalConfirmationJson: any = JSON.parse(
                    eotDto.finalConfirmation,
                );
                if (finalConfirmationJson?.finalRent) {
                    eotDto.finalPrice = finalConfirmationJson.finalRent;
                }
                if (finalConfirmationJson?.finalTerm) {
                    eotDto.finalTerm = finalConfirmationJson.finalTerm;
                }
            }
            eotDto.status = EndOfTenancyStatus.ARCHIVED;
        } else if (eotDto.stage == EndOfTenancyStage.TERMINATED) {
            eotDto.status = EndOfTenancyStatus.ARCHIVED;
        }
    }

    async updateEotByPropertyId(propertyId, eotDto: any): Promise<any> {
        try {
            const updateResult = await this.prisma.endOfTenancy.updateMany({
                where: {
                    propertyId: propertyId,
                },
                data: eotDto,
            });
            return updateResult;
        } catch (e) {
            this.logger.error(e);
            throw new Error('db update fails');
        }
    }
}
