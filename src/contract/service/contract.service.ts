import { Injectable, Logger } from '@nestjs/common';
import {
    search as osSearch,
    searchWithTotal,
} from 'src/common/service/opensearch.service';
import {
    TenancySearchVO,
    TenancySearchPageDTO,
    TenancySearchSimpleVO,
} from '../model/tenancy.model';
import {
    getTenancy,
    createTenancy,
    createTenancySetting,
    getTenancySetting,
    updateTenancy,
} from 'src/common/service/ddb.service';
import { PeriodEnum } from '../enum/periodEnum';
import { createTenancyRenewedEvent } from '@rentancy-com/loftyworks-events';
import { EventsEmitterService } from '../../common/service/eventsEmitter.service';

@Injectable()
export class ContractService {
    private logger = new Logger('ContractService', { timestamp: true });

    constructor(private eventsEmitter: EventsEmitterService) {}

    async searchTenanciesByDate(
        organisationId: string,
        startDate: string,
        endDate: string,
    ): Promise<TenancySearchVO[]> {
        try {
            const pageSize = 100;
            const allResults: TenancySearchVO[] = [];
            let lastSortValues: any = null;

            while (true) {
                const queryBody: any = {
                    query: {
                        bool: {
                            must: [
                                {
                                    term: {
                                        'tenancyOrganisationId.keyword':
                                            organisationId,
                                    },
                                },
                                {
                                    range: {
                                        endDate: {
                                            gte: startDate,
                                            lte: endDate,
                                        },
                                    },
                                },
                            ],
                        },
                    },
                    _source: [
                        'id',
                        'tenancyPropertyId',
                        'address',
                        'landlords',
                        'tenants',
                        'rent',
                        'endDate',
                        'reference',
                        'status',
                    ],
                    size: pageSize,
                    sort: [{ 'id.keyword': 'asc' }],
                };

                if (lastSortValues) {
                    queryBody.search_after = lastSortValues;
                }

                const result: any = await osSearch({
                    index: 'tenancy',
                    body: queryBody,
                });
                this.logger.log(`Search result: ${JSON.stringify(result)}`);
                if (!result || result.length === 0) {
                    break;
                }

                const searchVOs = result.map(
                    ({
                        id,
                        tenancyPropertyId,
                        address,
                        landlords,
                        tenants,
                        rent,
                        endDate,
                        reference,
                        status,
                    }) =>
                        ({
                            id,
                            propertyId: tenancyPropertyId,
                            address,
                            landlords,
                            tenants,
                            rent,
                            endDate,
                            reference,
                            status,
                        }) as TenancySearchVO,
                );

                allResults.push(...searchVOs);

                lastSortValues = [result[result.length - 1].id];
                this.logger.log(
                    `lastSortValues: ${JSON.stringify(lastSortValues)}`,
                );

                if (result.length < pageSize) {
                    break;
                }
            }

            return allResults;
        } catch (e) {
            this.logger.error(e);
            throw new Error('ES query failed');
        }
    }

    async updateTenancyById(tenancyId: string, tenancyInfo): Promise<any> {
        return await updateTenancy(tenancyId, tenancyInfo);
    }

    async renewTenancy(eotInfo: any, tenancyInfo: any): Promise<any> {
        const copyTenancyInfo = this.copyTenancy(eotInfo, tenancyInfo);
        const tenancySettingsId = tenancyInfo?.tenancySettingId;
        if (tenancySettingsId) {
            const tenancySettingInfo =
                await getTenancySetting(tenancySettingsId);
            const copyTenancySettingInfo =
                this.copyTenancySetting(tenancySettingInfo);
            const createTenancySettingResult = await createTenancySetting(
                copyTenancySettingInfo,
            );
            copyTenancyInfo.tenancySettingId = createTenancySettingResult.id;
        }
        const copiedTenancy = await createTenancy(copyTenancyInfo);

        const tenancyRenewedEvent = createTenancyRenewedEvent({
            propertyId: tenancyInfo.tenancyPropertyId,
            targetId: copiedTenancy.id,
            actorId: copiedTenancy.primaryTenant,
            organisationId: tenancyInfo.tenancyOrganisationId,
        });

        return await this.eventsEmitter.putEvents([tenancyRenewedEvent]);
    }

    private copyTenancy(eotInfo: any, tenancyInfo: any): any {
        delete tenancyInfo.id;
        delete tenancyInfo?.teamMembers;
        delete tenancyInfo?.tenancySettingId;
        delete tenancyInfo?.deposit;
        delete tenancyInfo?.renewalDate;
        delete tenancyInfo?.rentReviewDate;
        delete tenancyInfo?.reviewNotice;

        const endDate = new Date(tenancyInfo?.endDate);
        endDate.setUTCDate(endDate.getUTCDate() + 1);
        tenancyInfo.startDate = endDate.toISOString();
        if (eotInfo.landlordConfirmation) {
            const landlordConfirmation = JSON.parse(
                eotInfo.landlordConfirmation,
            );
            const startDate = new Date(tenancyInfo.startDate);
            if (landlordConfirmation.renewPeriod === PeriodEnum.THREE_MONTH) {
                startDate.setUTCMonth(startDate.getUTCMonth() + 3);
            }
            if (landlordConfirmation.renewPeriod === PeriodEnum.SIX_MONTH) {
                startDate.setUTCMonth(startDate.getUTCMonth() + 6);
            }
            if (landlordConfirmation.renewPeriod === PeriodEnum.NINE_MONTH) {
                startDate.setUTCMonth(startDate.getUTCMonth() + 9);
            }
            if (landlordConfirmation.renewPeriod === PeriodEnum.TWELVE_MONTH) {
                startDate.setUTCMonth(startDate.getUTCMonth() + 12);
            }
            tenancyInfo.endDate = startDate.toISOString();
            if (landlordConfirmation.renewPeriod === PeriodEnum.ROLLING) {
                tenancyInfo.endDate = '';
            }
        }
        const lastPartReference = tenancyInfo.reference.split('-').pop();
        const date = new Date(tenancyInfo.startDate);
        if (typeof lastPartReference !== 'number') {
            tenancyInfo.reference =
                tenancyInfo.reference + '-' + date.getUTCFullYear();
        } else if (lastPartReference === date.getFullYear() - 1) {
            tenancyInfo.reference =
                tenancyInfo.reference + '-' + date.getUTCFullYear();
        } else if (lastPartReference === date.getFullYear()) {
            tenancyInfo.reference = tenancyInfo.reference + '-' + 1;
        } else {
            tenancyInfo.reference =
                tenancyInfo.reference.substring(
                    0,
                    tenancyInfo.reference.lastIndexOf('-'),
                ) +
                '-' +
                (lastPartReference + 1);
        }
        return tenancyInfo;
    }

    private copyTenancySetting(tenancySettingInfo: any): any {
        delete tenancySettingInfo.id;
        return tenancySettingInfo;
    }

    async getTenancyById(tenancyId: string): Promise<any> {
        return await getTenancy(tenancyId);
    }

    async searchTenancy({
        organisationId,
        address,
        type,
        startDate,
        renewalDate,
        endDate,
    }: TenancySearchPageDTO): Promise<any> {
        try {
            const query: any = {
                bool: {
                    must: [
                        {
                            term: {
                                'tenancyOrganisationId.keyword': organisationId,
                            },
                        },
                    ],
                },
            };

            if (address) {
                query.bool.must.push({
                    wildcard: {
                        'address.keyword': `*${address}*`,
                    },
                });
            }

            if (type) {
                query.bool.must.push({
                    term: {
                        'type.keyword': type,
                    },
                });
            }

            // Add date range conditions
            const dateRangeQueries = [];

            if (startDate) {
                dateRangeQueries.push({
                    range: {
                        startDate: {
                            gte: startDate,
                        },
                    },
                });
            }

            if (renewalDate) {
                dateRangeQueries.push({
                    range: {
                        renewalDate: {
                            gte: renewalDate,
                        },
                    },
                });
            }

            if (endDate) {
                dateRangeQueries.push({
                    range: {
                        endDate: {
                            lte: endDate,
                        },
                    },
                });
            }

            if (dateRangeQueries.length > 0) {
                query.bool.must.push(...dateRangeQueries);
            }

            const { total, hits } = await searchWithTotal({
                index: 'tenancy',
                body: {
                    query,
                    _source: ['id', 'address', 'tenancyPropertyId'],
                    size: 100,
                    track_total_hits: true,
                },
            });

            const searchVOs = hits.map(({ id, address, tenancyPropertyId }) => {
                return {
                    id: id,
                    address: address,
                    tenancyPropertyId: tenancyPropertyId,
                } as TenancySearchSimpleVO;
            });
            return { total, tenancyList: searchVOs };
        } catch (e) {
            this.logger.error(e);
            throw new Error('es query fails');
        }
    }

    async searchByKeyword(
        organisationId: string,
        keyword: string,
    ): Promise<TenancySearchSimpleVO[]> {
        try {
            const query: any = {
                bool: {
                    must: [
                        {
                            term: {
                                'tenancyOrganisationId.keyword': organisationId,
                            },
                        },
                        {
                            bool: {
                                should: [
                                    {
                                        wildcard: {
                                            'address.keyword': `*${keyword}*`,
                                        },
                                    },
                                    {
                                        wildcard: {
                                            'reference.keyword': `*${keyword}*`,
                                        },
                                    },
                                ],
                            },
                        },
                    ],
                },
            };

            const { hits } = await searchWithTotal({
                index: 'tenancy',
                body: {
                    query,
                    _source: [
                        'id',
                        'address',
                        'tenancyPropertyId',
                        'reference',
                        'postcode',
                        'title',
                    ],
                    size: 10,
                },
            });

            return hits.map(
                ({
                    id,
                    address,
                    tenancyPropertyId,
                    reference,
                    postcode,
                    title,
                }) => ({
                    id,
                    address,
                    tenancyPropertyId,
                    reference,
                    subTitle: postcode,
                    title,
                }),
            );
        } catch (e) {
            this.logger.error(e);
            throw new Error('ES query failed');
        }
    }
}
