import { ApiProperty } from '@nestjs/swagger';

export class Activity {
    @ApiProperty()
    id: string;

    @ApiProperty()
    action: string;

    @ApiProperty()
    activityOrganisationId: string;

    @ApiProperty()
    activityUserId: string;

    @ApiProperty()
    link: string;

    @ApiProperty()
    memberStatus: string;

    @ApiProperty()
    parentId: string;

    @ApiProperty()
    parentType: string

    @ApiProperty()
    pushStatus: string;

    @ApiProperty()
    readStatus: string

    @ApiProperty()
    resolveStatus: string

    @ApiProperty()
    sentStatus: string

    @ApiProperty()
    status: string

    @ApiProperty()
    subTitle: string;

    @ApiProperty()
    title: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    updatedDate: string;
}