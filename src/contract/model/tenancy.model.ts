import { ApiProperty } from '@nestjs/swagger';

export class TenancySearchVO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    endDate: string;

    @ApiProperty()
    reference: string;

    @ApiProperty()
    status: string;
}

export class TenancySearchPageDTO {
    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    address: string;

    @ApiProperty()
    startDate: string;

    @ApiProperty()
    renewalDate: string;

    @ApiProperty()
    endDate: string;

    @ApiProperty()
    type: string;
}

export class TenancySearchSimpleVO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    address: string;

    @ApiProperty()
    tenancyPropertyId: string;
}
