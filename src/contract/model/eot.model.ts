import { ApiProperty } from '@nestjs/swagger';


export class EndOfTenancy {
    @ApiProperty()
    id: string;

    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    address: string;

    @ApiProperty()
    oldPrice: string;

    @ApiProperty()
    finalPrice: string;

    @ApiProperty({required: false})
    finalTerm: string;

    @ApiProperty()
    proposalPrice: string;

    @ApiProperty()
    landlordEmail: string;

    @ApiProperty()
    landlordLetter: string;

    @ApiProperty()
    landlordConfirmation: string

    @ApiProperty()
    tenantLetter: string;

    @ApiProperty()
    tenantConfirmation: string

    @ApiProperty()
    finalConfirmation: string

    @ApiProperty()
    finalLetter: string

    @ApiProperty()
    stage: string

    @ApiProperty()
    status: string

    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    taskIds: string[];

    @ApiProperty()
    taskInfos: Task[];

    @ApiProperty()
    createdBy: string;
}

export class Task {
    @ApiProperty()
    id: string

    @ApiProperty()
    name: string
}


export class UpdateEndOfTenancyDto {
    @ApiProperty()
    id: string;

    @ApiProperty({required: false})
    propertyId: string;

    @ApiProperty({required: false})
    oldPrice: string;

    @ApiProperty({required: false})
    proposalPrice: string;

    @ApiProperty({required: false})
    finalPrice: string;

    @ApiProperty({required: false})
    finalTerm: string;

    @ApiProperty({required: false})
    landlordConfirmation: string

    @ApiProperty({required: false})
    landlordLetter: string;

    @ApiProperty({required: false})
    tenantLetter: string;

    @ApiProperty({required: false})
    tenantConfirmation: string

    @ApiProperty({required: false})
    finalConfirmation: string

    @ApiProperty({required: false})
    finalLetter: string;

    @ApiProperty()
    taskIds: string[];

    @ApiProperty()
    taskInfos: Task[];

    @ApiProperty()
    stage: string

    @ApiProperty()
    status: string

    @ApiProperty()
    createdBy: string;

    @ApiProperty()
    organisationId: string;
}


export class EndOfTenancyPageQueryDTO {
    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    page: number;

    @ApiProperty()
    size: number;

    @ApiProperty({ required: false })
    keyword?: string;

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    stageList?: string[];

    @ApiProperty({ required: false })
    fromEndDate?: string;

    @ApiProperty({ required: false })
    toEndDate?: string;
}


export class EndOfTenancyPageQueryVO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    address: string;

    @ApiProperty()
    endDate: Date;

    @ApiProperty()
    reference: string;

    @ApiProperty()
    primaryTenantName: string;

    @ApiProperty()
    stage: string;

    @ApiProperty()
    status: string;
}
