import { Modu<PERSON> } from '@nestjs/common';
import { ContractService } from './service/contract.service';
import { EotService } from './service/eot.service';
import { EventHandler } from './event/eventHandler';
import { EotController } from './controller/api/eot.controller';
import { EotInternalController } from './controller/client/eot.internal.controller';
import { EmailConfirmController } from './controller/email-confirm.controller';
import { EventsEmitterService } from '../common/service/eventsEmitter.service';

@Module({
  providers: [ContractService, EotService, EventHandler, EventsEmitterService],
  exports: [ContractService, EotService],
  controllers: [EotController, EmailConfirmController,EotInternalController]
})
export class ContractModule {}
