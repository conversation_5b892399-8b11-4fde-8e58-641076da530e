import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EotEvent } from './eotEvent';
import { createActivity } from 'src/common/service/ddb.service';
import { Activity } from "../model/activity.model";
import { v1 as uuidV1 } from 'uuid';
import { EndOfTenancyStage } from '../enum/eotStage';

const END_OF_TENANCY = "END_OF_TENANCY";

@Injectable()
export class EventHandler {
  @OnEvent(EndOfTenancyStage.TERMINATED)
  async handleLandlordTerminateEvent(event: EotEvent) {
    console.log(`handleLandlordTerminateEvent handled: ${event.eotId}, ${JSON.stringify(event.eotInfo)}`);
    
    if (this.shouldSkipActivityCreation(event.eotInfo)) {
      return;
    }
    
    await this.createEotActivity(event, "Land<PERSON> replied");
  }

  @OnEvent(EndOfTenancyStage.LANDLORD_REPLIED)
  async handleLandlordReplyEvent(event: EotEvent) {
    console.log(`LandlordReplyEvent handled: ${event.eotId}, ${JSON.stringify(event.eotInfo)}`);
    await this.createEotActivity(event, "Landlord replied");
  }

  @OnEvent(EndOfTenancyStage.TENANT_REPLIED)
  async handleTenantReplyEvent(event: EotEvent) {
    console.log(`TenantReplyEvent handled: ${event.eotId}, ${JSON.stringify(event.eotInfo)}`);
    await this.createEotActivity(event, "Tenant replied");
  }

  private async createEotActivity(event: EotEvent, subTitlePrefix: string) {
    const eotInfo = event.eotInfo;
    if(eotInfo?.createdBy){
      const activity: Activity = {
        id: uuidV1(),
        action: "INSERT",
        activityOrganisationId: eotInfo?.organisationId,
        activityUserId: eotInfo?.createdBy,
        link: `/end-of-tenancy/${event.eotId}`,
        memberStatus: "",
        parentId: event.eotId,
        parentType: END_OF_TENANCY,
        pushStatus: "NOT_PUSHED",
        readStatus: "UNREAD",
        resolveStatus: "UNRESOLVED",
        sentStatus: "UNSENT",
        status: "ACTIVE",
        subTitle: `${subTitlePrefix} (${eotInfo?.address})`,
        title: "End of Tenancy",
        type: END_OF_TENANCY,
        updatedDate: new Date().toISOString()
      };
      
      await createActivity(activity);
    }
  }

  private shouldSkipActivityCreation(eotInfo?: any): boolean {
    if (eotInfo?.landlordConfirmation) {
      try {
        const landlordConfirmationJson = JSON.parse(eotInfo.landlordConfirmation);
        return landlordConfirmationJson.ifRenew;
      } catch (error) {
        console.error("Failed to parse landlordConfirmation JSON", error);
      }
    }
    return false;
  }
}
