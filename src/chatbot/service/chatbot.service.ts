import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/common/service/prisma.service';
import { HttpService } from '@nestjs/axios';
import { v1 as uuidV1 } from 'uuid';
import {
    MessageType,
    WidgetUIType,
    ModelType,
    ToolAction,
    ListType,
} from '../enum/message.enum';
import { UserRepository } from '../../common/repository/user';

@Injectable()
export class ChatbotService {
    private logger = new Logger('ChatbotService', { timestamp: true });

    constructor(
        private readonly prisma: PrismaService,
        private readonly httpService: HttpService,
        private readonly userRepository: UserRepository,
    ) {}

    async chat(
        conversationId: string,
        content: string,
        userId: string,
        organisationId: string,
    ) {
        const userInfo = await this.userRepository.findByCognitoId(userId);
        const agentName = `${userInfo?.fname} ${userInfo?.sname}`;

        const { conversationInfo, isNewConversation } =
            await this.handleConversation(
                conversationId,
                userId,
                organisationId,
                agentName,
            );
        const flowResponse = await this.callFlowAiApi(
            content,
            conversationInfo.flowConversationId,
            conversationInfo.variables,
            userId,
        );
        if (isNewConversation) {
            await this.updateFlowConversationId(
                conversationInfo.id,
                flowResponse.conversationId,
            );
        }
        const newConversationInfo = await this.getConversationById(
            conversationInfo.id,
        );
        const latestToolOutput = newConversationInfo.latestToolOutput;
        await this.storeMessages(conversationInfo.id, content, flowResponse);

        return {
            conversationId: conversationInfo.id,
            messages: this.formatMessages(
                flowResponse.content,
                latestToolOutput,
            ),
            agentName: 'AI Assistant',
        };
    }

    private async handleConversation(
        conversationId: string,
        userId: string,
        organisationId: string,
        agentName: string,
    ) {
        if (conversationId === '1') {
            const newId = uuidV1();
            const conversation = await this.prisma.conversation.create({
                data: {
                    id: newId,
                    userId,
                    organisationId,
                    variables: {
                        agentName,
                        conversationId: newId,
                        currentTime: new Date().toISOString(),
                    },
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            });
            return { conversationInfo: conversation, isNewConversation: true };
        }
        const conversation = await this.prisma.conversation.update({
            where: { id: conversationId },
            data: { latestToolOutput: null, updatedAt: new Date() },
        });
        return { conversationInfo: conversation, isNewConversation: false };
    }

    private async updateFlowConversationId(
        conversationId: string,
        flowConversationId: string,
    ) {
        await this.prisma.conversation.update({
            where: { id: conversationId },
            data: { flowConversationId, updatedAt: new Date() },
        });
    }

    private async storeMessages(
        conversationId: string,
        userContent: string,
        flowResponse: any,
    ) {
        const messages = [
            {
                id: uuidV1(),
                conversationId,
                type: 'user',
                content: userContent,
            },
            {
                id: uuidV1(),
                conversationId,
                type: 'ai',
                content: flowResponse.content,
                totalTokens: flowResponse.tokenUsage?.total,
                promptTokens: flowResponse.tokenUsage?.prompt,
                completionTokens: flowResponse.tokenUsage?.completion,
            },
        ];

        await Promise.all(
            messages.map((msg) =>
                this.prisma.message.create({
                    data: {
                        ...msg,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    },
                }),
            ),
        );
    }

    private formatMessages(aiContent: string, latestToolOutput?: any) {
        const messages: any = [{ type: MessageType.TEXT, content: aiContent }];

        if (!latestToolOutput) {
            return messages;
        }

        const toolOutputConfig = {
            [ToolAction.REPORT_ISSUE]: {
                type: MessageType.ACTION,
                uiType: WidgetUIType.DETAIL,
                modelType: ModelType.PROPERTY_ISSUE,
                getData: (output) => output?.[ToolAction.REPORT_ISSUE]?.detail,
            },
            [ToolAction.ADD_TASK]: {
                type: MessageType.ACTION,
                uiType: WidgetUIType.DETAIL,
                modelType: ModelType.TASK,
                getData: (output) => output?.[ToolAction.ADD_TASK]?.detail,
            },
            [ToolAction.SEND_MESSAGE]: {
                type: MessageType.ACTION,
                uiType: WidgetUIType.DETAIL,
                modelType: ModelType.MESSAGE,
                getData: (output) => output?.[ToolAction.SEND_MESSAGE]?.detail,
            },
            [ToolAction.SEND_EMAIL]: {
                type: MessageType.ACTION,
                uiType: WidgetUIType.DETAIL,
                modelType: ModelType.EMAIL,
                getData: (output) => output?.[ToolAction.SEND_EMAIL]?.detail,
            },
            [`${ToolAction.SEARCH_PROPERTY},${ToolAction.SEARCH_TENANCY}`]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.PROPERTY_TENANCY,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_PROPERTY]?.[
                        ListType.PROPERTY_LIST
                    ],
                validate: (output) => {
                    const actions = [
                        ToolAction.SEARCH_PROPERTY,
                        ToolAction.SEARCH_TENANCY,
                    ];
                    return actions.every((action) => output?.[action]);
                },
            },
            [ToolAction.SEARCH_EOT]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.EOT,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_EOT]?.[ListType.EOT_LIST],
            },
            [ToolAction.SEARCH_PROPERTY]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.PROPERTY,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_PROPERTY]?.[
                        ListType.PROPERTY_LIST
                    ],
            },
            [ToolAction.SEARCH_TENANCY]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.TENANCY,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_TENANCY]?.[
                        ListType.TENANCY_LIST
                    ],
            },
            [ToolAction.SEARCH_TASKS]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.TASK,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_TASKS]?.[ListType.TASK_LIST],
            },
            [ToolAction.SEARCH_CONVERSATIONS]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.CONVERSATION,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_CONVERSATIONS]?.[
                        ListType.CONVERSATION_LIST
                    ],
            },
            [ToolAction.SEARCH_CONTACT]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.CONTACT,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_CONTACT]?.[
                        ListType.CONTACT_LIST
                    ],
            },
            [ToolAction.SEARCH_COMPLIANCE]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.COMPLIANCE,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_COMPLIANCE]?.[
                        ListType.COMPLIANCE_LIST
                    ],
            },
            [ToolAction.SEARCH_DOCUMENT]: {
                type: MessageType.WIDGET,
                uiType: WidgetUIType.LIST,
                modelType: ModelType.DOCUMENT,
                getData: (output) =>
                    output?.[ToolAction.SEARCH_DOCUMENT]?.[
                        ListType.DOCUMENT_LIST
                    ],
            },
        };

        for (const [, config] of Object.entries(toolOutputConfig)) {
            if ('validate' in config && !config.validate(latestToolOutput)) {
                continue;
            }
            const data = config.getData(latestToolOutput);
            if (!data) {
                continue;
            }

            messages.push({
                type: config.type,
                data: {
                    uiType: config.uiType,
                    modelType: config.modelType,
                    [config.uiType === WidgetUIType.DETAIL ? 'detail' : 'list']:
                        data,
                },
            });
            break;
        }

        return messages;
    }

    private async callFlowAiApi(
        userMessage: string,
        conversationId: string,
        variables: any = {},
        userId: string,
    ) {
        const FLOW_URL = process.env.FLOW_URL;
        try {
            this.httpService.axiosRef.defaults.headers.common['accessKey'] =
                process.env.FLOW_ACCESS_KEY;
            const response = await this.httpService.axiosRef.post(FLOW_URL, {
                conversationId: conversationId ?? '1',
                question: userMessage,
                variables: variables,
                email: userId,
            });

            const data = response.data;
            return {
                content: data.content,
                conversationId: data.conversation_id,
                tokenUsage: {
                    total: data.response_metadata.token_usage.total_tokens,
                    prompt: data.response_metadata.token_usage.prompt_tokens,
                    completion:
                        data.response_metadata.token_usage.completion_tokens,
                },
            };
        } catch (error) {
            this.logger.error('call flow AI API Error:', error);
            throw new Error('Failed to call AI API');
        }
    }

    async getConversationById(conversationId: string) {
        try {
            return await this.prisma.conversation.findUnique({
                where: {
                    id: conversationId,
                },
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db select fails');
        }
    }

    async updateConversation(conversationId: string, data) {
        try {
            return await this.prisma.conversation.update({
                where: {
                    id: conversationId,
                },
                data,
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error('db update fails');
        }
    }
}
