export enum MessageType {
    TEXT = 'TEXT',
    WIDGET = 'WIDGET',
    ACTION = 'ACTION',
}

export enum WidgetUIType {
    LIST = 'list',
    DETAIL = 'detail',
}

export enum ModelType {
    EOT = 'eot',
    PROPERTY = 'property',
    TENANCY = 'tenancy',
    TASK = 'task',
    CONVERSATION = 'conversation',
    CONTACT = 'contact',
    PROPERTY_TENANCY = 'propertyTenancy',
    PROPERTY_ISSUE = 'propertyIssue',
    COMPLIANCE = 'compliance',
    DOCUMENT = 'document',
    MESSAGE = 'message',
    EMAIL = 'email',
}

export enum ToolAction {
    SEARCH_EOT = 'searchEndOfTenancy',
    SEARCH_PROPERTY = 'searchProperty',
    SEARCH_TENANCY = 'searchTenancy',
    SEARCH_TASKS = 'searchMyTasks',
    SEARCH_CONVERSATIONS = 'searchMyConversations',
    SEARCH_CONTACT = 'searchContact',
    SEARCH_PROPERTY_CONTRACT = 'searchPropertyContract',
    REPORT_ISSUE = 'reportIssue',
    SEARCH_COMPLIANCE = 'searchCompliance',
    SEARCH_DOCUMENT = 'searchDocument',
    ADD_TASK = 'addTask',
    SEND_MESSAGE = 'sendMessage',
    SEND_EMAIL = 'sendEmail',
}

export enum ListType {
    EOT_LIST = 'eotList',
    PROPERTY_LIST = 'propertyList',
    TENANCY_LIST = 'tenancyList',
    TASK_LIST = 'taskList',
    CONVERSATION_LIST = 'conversationList',
    CONTACT_LIST = 'contactList',
    COMPLIANCE_LIST = 'complianceList',
    DOCUMENT_LIST = 'documentList',
}
