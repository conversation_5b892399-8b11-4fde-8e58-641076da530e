import { <PERSON>, Post, Body, Res, Req } from '@nestjs/common';
import { LwRequest, responseOk } from '../../../common/util/requestUtil';
import { ChatbotService } from '../../service/chatbot.service';
import { Response } from 'express';

@Controller('api/v1/chatbot')
export class ChatbotController {
    constructor(private readonly chatbotService: ChatbotService) {}

    @Post()
    async chatWithAi(
        @Body() body: { conversationId: string; content: string },
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        const currentOrgId = req.user['custom:organisationId'];
        const currentUserId = req.user['sub'];
        responseOk(
            res,
            await this.chatbotService.chat(
                body.conversationId,
                body.content,
                currentUserId,
                currentOrgId,
            ),
        );
    }
}
