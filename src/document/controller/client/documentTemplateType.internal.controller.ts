import { Body, Controller, Delete, Get, Logger, Param, Post, Put, Query, Res } from "@nestjs/common";
import { Response } from 'express';
import { DocumentTemplateType } from "@prisma/client";
import { responseError, responseOk } from "src/common/util/requestUtil";
import { DocumentTemplateTypeService } from "src/document/service/documentTemplateType.service";
import { DocumentTemplateService } from "src/document/service/documentTemplate.service";
import { ResponseCode } from "src/common/constant/responseCode";

@Controller('documentTemplateType')
export class DocumentTemplateTypeInternalController {
    private logger = new Logger('DocumentInternalController', {
            timestamp: true,
        });
    constructor(
        private documentTemplateTypeService: DocumentTemplateTypeService,
        private documentTemplateService: DocumentTemplateService
    ) {}

    @Post('organisation/:orgId/documentTemplateType')
    async createDocumentTemplateType(
        @Param('orgId') orgId: string,
        @Body() templateType: DocumentTemplateType,
        @Res() res: Response
    ) {
        templateType.organisationId = orgId;
        responseOk(res, await this.documentTemplateTypeService.createDocumentTemplateType(templateType));
    }

    @Get('organisation/:orgId/documentTemplateType')
    async getDocumentTemplateTypes(
        @Param('orgId') orgId: string,
        @Res() res: Response
    ) {
        responseOk(res, await this.documentTemplateTypeService.getDocumentTemplateTypes(orgId));
    }

    @Delete('organisation/:orgId/documentTemplateType')
    async deleteDocumentTemplateType(
        @Param('orgId') orgId: string,
        @Query('id') typeId: string, 
        @Res() res: Response
    ) {
        if (typeId === "all") {
            await this.documentTemplateTypeService.deleteOrgAllDocumentTemplateType(orgId);
            responseOk(res);
            return;
        }

        const docTemplateType = await this.documentTemplateTypeService.getDocumentTemplateTypeById(orgId, typeId);
        if (!docTemplateType) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
        }
        await this.documentTemplateService.deleteType(docTemplateType.name);
        const result = await this.documentTemplateTypeService.deleteOrgDocumentTemplateType(orgId, docTemplateType);
        responseOk(res, result);
    }

    @Put('organisation/:orgId/order')
    async updateDocumentTemplateTypeOrder(
        @Param('orgId') orgId: string,
        @Query('typeId') typeId: string,
        @Query('newIndex') newIndex: string,
        @Res() res: Response
    ) {
        const result = await this.documentTemplateTypeService.updateTypeOrder(orgId, typeId, parseInt(newIndex, 10));
        responseOk(res, result);
    }

}