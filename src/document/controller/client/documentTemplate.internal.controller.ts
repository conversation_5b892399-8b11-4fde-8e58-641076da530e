import { Controller, Delete, Get, Param, Res } from '@nestjs/common';
import { Response } from 'express';
import {
    responseOk,
} from '../../../common/util/requestUtil';
import { DocumentTemplateService } from '../../service/documentTemplate.service';
import { Logger } from '@nestjs/common';

@Controller('documentTemplate')
export class DocumentTemplateInternalController {
    private logger = new Logger('DocumentTemplateInternalController', {
        timestamp: true,
    });
    constructor(private documentTemplateService: DocumentTemplateService) {}

    @Get('/organisation/:orgId/documentTemplate')
    public async getDocumentTemplate(
        @Param('orgId') orgId: string,
        @Res() res: Response,
    ) {
        const templates = await this.documentTemplateService.getDocTemplates(orgId);
        responseOk(res, templates);
    }

    @Get('/user/:userId/documentTemplate')
    public async getUserDocumentTemplates(
        @Param('userId') userId: string,
        @Res() res: Response,
    ) {
        const templates = await this.documentTemplateService.getUserDocTemplates(userId)
        responseOk(res, templates);
    }

    @Delete('/organisation/:orgId/documentTemplate')
    public async deleteOrganisationAllDocumentTemplate(
        @Param('orgId') orgId: string,
        @Res() res: Response,
    ) {
        const templates = await this.documentTemplateService.deleteOrganisationAllTemplates(orgId);
        responseOk(res, templates.count);
    }
}
