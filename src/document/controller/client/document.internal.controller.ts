import {
    Body,
    Controller,
    Delete,
    Get,
    Logger,
    Param,
    Post,
    Put,
    Query,
    Res,
} from '@nestjs/common';
import e, { Response } from 'express';
import { DocumentService } from '../../service/document.service';
import { responseError, responseOk } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { Document } from '@prisma/client';
import { v1 as uuidV1 } from 'uuid';
import {
    ApiBody,
    ApiOperation,
    ApiParam,
    ApiQuery,
    ApiResponse,
} from '@nestjs/swagger';
import { DocumentCreateDTO, DocumentDTO, DocumentUpdateDTO } from 'src/document/model/document.model';
import { getUser } from 'src/common/service/ddb.service';

@Controller('document')
@ApiResponse({ status: 200, description: 'Success' })
@ApiResponse({ status: 400, description: 'Bad Request' })
@ApiResponse({ status: 500, description: 'Server Error' })
export class DocumentInternalController {
    private logger = new Logger('DocumentInternalController', {
        timestamp: true,
    });
    constructor(private documentService: DocumentService) {}

    @ApiOperation({ summary: 'Create document' })
    @ApiQuery({
        name: 'userId',
        description: 'User ID',
    })
    @ApiBody({
        type: DocumentCreateDTO,
        description: 'Data transfer object to create document',
    })
    @Post('')
    public async createDocument(
        @Body() createDTO: DocumentDTO,
        @Res() res: Response,
        @Query('userId') userId?: string,
    ) {
        const uuid = uuidV1();
        let doc: Document = {} as Document;
        doc = { ...doc, ...createDTO };
        doc.id = uuid;
        if(!!userId) {
            doc.documentUserId = userId;
            const user = await getUser(userId);
            doc.documentOrganisationId = user.currentOrganisation;
        }
        const create = await this.documentService.createDocument(doc);
        responseOk(res, create);
    }


    @Get('/:documentId')
    public async getDocumentById(@Query('documentId') documentId: string, @Res() res: Response) {
        if (!documentId) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            var document = await this.documentService.getDocumentById(documentId);
            responseOk(res, document);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Archive document' })
    @ApiQuery({
        name: 'key',
        description: 'Document key',
    })
    @ApiQuery({
        name: 'id',
        description: 'Document ID',
    })
    @Put('archive')
    public async archiveDocument(
        @Query('orgId') orgId: string,
        @Res() res: Response,
        @Query('key') docKey?: string,
        @Query('id') docId?: string,
    ) {
        if(!orgId || (!docKey && !docId)) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        if(docId) {
            const update = await this.documentService.archiveDocumentById(orgId, docId);
            responseOk(res, update.count);
        }else {
            const update = await this.documentService.archiveDocumentByKey(orgId, docKey);
            responseOk(res, update.count);
        }
    }

    @ApiOperation({ summary: 'Get organisation documents' })
    @ApiParam({
        name: 'orgId',
        description: 'Organisation ID',
    })
    @Get('organisation/:orgId/document')
    public async getOrganisationDocuments(
        @Param('orgId') organisationId: string,
        @Res() res: Response,
        @Query('archived') archived?: string,
        @Query('startDate') startDate?: string,
        @Query('endDate') endDate?: string,
    ) {
        try {
            const documents =
                await this.documentService.getOrganisationDocuments(
                    organisationId,
                    archived,
                    startDate,
                    endDate
                );
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get user documents' })
    @ApiParam({
        name: 'orgId',
        description: 'Organisation ID',
    })
    @ApiParam({
        name: 'userId',
        description: 'User ID',
    })
    @Get('organisation/:orgId/user/:userId/document')
    public async getUserDocuments(
        @Param('orgId') organisationId: string,
        @Param('userId') userId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getUserDocuments(
                organisationId,
                userId,
            );
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get organisation documents with expire date' })
    @ApiParam({
        name: 'orgId',
        description: 'Organisation ID',
    })
    @ApiQuery({
        name: 'expireAt',
        description: 'Expire date',
    })
    @Get('organisation/:orgId/document/withExpiry')
    public async getOrganisationDocumentsWithExpiry(
        @Param('orgId') organisationId: string,
        @Query('expireAt') expireDate: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getOrganisationDocumentsWithExpiry(
                    organisationId,
                    expireDate,
                );
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get conversation documents' })
    @ApiParam({
        name: 'conversationId',
        description: 'Conversation ID',
    })
    @Get('conversation/:conversationId/document')
    public async getConversationDocuments(
        @Param('conversationId') conversationId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getConversationDocuments(conversationId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get tenancy documents' })
    @ApiParam({
        name: 'tenancyId',
        description: 'Tenancy ID',
    })
    @Get('tenancy/:tenancyId/document')
    public async getTenancyDocuments(
        @Param('tenancyId') tenancyId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getTenancyDocuments(tenancyId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get property documents' })
    @ApiParam({
        name: 'property',
        description: 'Property ID',
    })
    @Get('property/:propertyId/document')
    public async getPropertyDocuments(
        @Param('propertyId') propertyId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getPropertyDocuments(propertyId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get task images' })
    @ApiParam({
        name: 'taskId',
        description: 'Task ID',
    })
    @Get('task/:taskId/image')
    public async getTaskImages(
        @Param('taskId') taskId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getTaskImages(taskId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get task document' })
    @ApiParam({
        name: 'taskId',
        description: 'Task ID',
    })
    @Get('task/:taskId/document')
    public async getTaskDocuments(
        @Param('taskId') taskId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getTaskDocuments(taskId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get supplier organisation documents' })
    @ApiParam({
        name: 'orgId',
        description: 'Organisation ID',
    })
    @Get('supplierOrganisation/:orgId/document')
    public async getSupplierOrganisationDocuments(
        @Param('orgId') orgId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getSupplierOrganisationDocuments(orgId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get parent property documents' })
    @ApiParam({
        name: 'parentPropertyId',
        description: 'Parent property ID',
    })
    @Get('parentProperty/:parentPropertyId/document')
    public async getParentPropertyDocuments(
        @Param('parentPropertyId') propertyId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getParentPropertyEntityDocuments(propertyId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update document by id' })
    @ApiParam({
        name: 'id',
        description: 'Document ID',
    })
    @Put(':id')
    public async updateDocument(
        @Param('id') docId: string,
        @Body() documentUpdateDTO: DocumentUpdateDTO,
        @Res() res: Response,
    ) {
        documentUpdateDTO.id = docId;
        let document = { ...documentUpdateDTO } as Document;
        if ('expiry' in document && document.expiry) {
            document.expiry = new Date(document.expiry);
        }
        const update = await this.documentService.updateDocument(document);
        responseOk(res, update);
    }

    @Get('emailAttachment/:attachmentId/document')
    public async getEmailAttachmentDocument(
        @Param('attachmentId') attachmentId: string,
        @Res() res: Response,
    ) {
        try {
            const document = await this.documentService.getEmailAttachmentDocument(attachmentId);
            responseOk(res, document);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Get('image/:imageOrgId/document')
    public async getImageDocuments(
        @Param('imageOrgId') imageOrgId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getImageDocuments(imageOrgId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Get('lineItem/:lineItemId/document')
    public async getLineItemDocuments(
        @Param('lineItemId') lineItemId: string,
        @Res() res: Response,
    ) {
        try {
            const documents = await this.documentService.getImageDocuments(lineItemId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Delete('organisation/:orgId/document')
    public async deleteOrganisationAllDocuments(
        @Param('orgId') orgId: string,
        @Res() res: Response,
    ) {
        try {
            const result = await this.documentService.deleteAllOrganisationDocuments(orgId);
            responseOk(res, result.count);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
