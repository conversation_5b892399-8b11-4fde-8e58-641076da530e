import { Controller, Get, Param, Res } from '@nestjs/common';
import { Response } from 'express';
import {
    responseOk,
} from '../../../common/util/requestUtil';
import { Logger } from '@nestjs/common';
import { PermissionService } from 'src/document/service/permission.service';

@Controller('permission')
export class PermissionInternalController {
    private logger = new Logger('PermissionInternalController', {
        timestamp: true,
    });
    constructor(private permissionService: PermissionService) {}

    @Get('/:id')
    public async getPermission(
        @Param('id') id: string,
        @Res() res: Response,
    ) {
        const permission = await this.permissionService.getPermissionById(id);
        responseOk(res, permission);
    }
}
