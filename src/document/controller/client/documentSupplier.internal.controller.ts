import { <PERSON>, Delete, Get, Logger, Param, Res } from '@nestjs/common';
import { Response } from 'express';
import { DocumentSupplierService } from '../../service/documentSupplier.service';
import { responseOk, responseError } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';

@Controller("documentSupplier")
export class DocumentSupplierInternalController {
    private logger = new Logger('DocumentSupplierInternalController', { timestamp: true });
    constructor (private documentSupplierService: DocumentSupplierService) {
    }

    @Delete("organisation/:orgId/documentSupplier")
    public async deleteOrganisationAllDocumentSuppliers(@Param("orgId") orgId: string, @Res() res: Response) {
        try {
            const result = await this.documentSupplierService.deleteOrganisationAllDocSuppliers(orgId);
            responseOk(res, result.count);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Get('/organisation/:orgId/documentSupplier')
    public async getDocumentSuppliers(
        @Param('orgId') orgId: string,
        @Res() res: Response,
    ) {
        const templates = await this.documentSupplierService.getOrgDocumentSuppliers(orgId);
        responseOk(res, templates);
    }

    @Get('/user/:userId/documentSupplier')
    public async getUserDocumentSuppliers(
        @Param('userId') userId: string,
        @Res() res: Response,
    ) {
        const templates = await this.documentSupplierService.getUserDocumentSuppliers(userId)
        responseOk(res, templates);
    }
}

