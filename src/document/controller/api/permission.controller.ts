import { Body, Controller, Delete, Get, Logger, Post, Put, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { responseOk, responseError, LwRequest, BaseRes } from '../../../common/util/requestUtil';
import { Permission } from '@prisma/client';
import { ResponseCode } from '../../../common/constant/responseCode';
import { ApiBody, ApiExtraModels, ApiOkResponse, ApiOperation, ApiQuery, getSchemaPath } from '@nestjs/swagger';
import { PermissionService } from 'src/document/service/permission.service';
import { v1 as uuidV1 } from 'uuid';
import { Permission as PermissionModel, PermissionDTO, PermissionUpdateDTO } from 'src/document/model/permission.model';

@Controller("api/v1/permission")
@ApiExtraModels(BaseRes, PermissionModel, PermissionUpdateDTO)
export class PermissionController {
    private logger = new Logger('PermissionController', { timestamp: true });
    constructor (private permissionService: PermissionService) {
    }
    
    @ApiOperation({ summary: 'Add permission' })
    @ApiBody({ type: PermissionDTO })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { type: 'object', properties: { id: { type: 'string' } }},
                    },
                },
            ],
        },
    })
    @Post()
    public async addPermission(@Body() permissionDTO: PermissionDTO, @Res() res: Response) {
        try {
            if (!permissionDTO.roles) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            const id = uuidV1();
            let permission: Permission = {} as Permission;
            permission = { 
                ...permission,
                id: id,
                groups: permissionDTO.roles,
                itemType: permissionDTO.itemType
            };
            this.permissionService.createPermission(permission);
            responseOk(res, { id: id });
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get permission by id' })
    @ApiQuery({ name: 'permissionId', type: 'string', required: true })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(PermissionModel) },
                    },
                },
            ],
        },
    })
    @Get()
    public async getPermissionById(@Query('permissionId') permissionId: string, @Res() res: Response) {
        try {
            if (!permissionId) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            const permission = await this.permissionService.getPermissionById(permissionId);
            responseOk(res, permission);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update permission by id' })
    @ApiBody({ type: PermissionUpdateDTO })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(PermissionModel) },
                    },
                },
            ],
        },
    })
    @Put()
    public async updatePermission(@Body() permissionUpdateDTO: PermissionUpdateDTO, @Res() res: Response) {
        try {
            if (!permissionUpdateDTO.id) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            let permission: Permission = { ...permissionUpdateDTO } as Permission;
            const update = await this.permissionService.updatePermission(permission);
            responseOk(res, update);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}

