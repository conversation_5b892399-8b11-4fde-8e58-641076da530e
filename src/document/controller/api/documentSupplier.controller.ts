import { Body, Controller, Delete, Logger, Post, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { DocumentSupplierService } from '../../service/documentSupplier.service';
import { responseOk, responseError, LwRequest, BaseRes } from '../../../common/util/requestUtil';
import { DocumentSupplier } from '@prisma/client';
import { ResponseCode } from '../../../common/constant/responseCode';
import { ApiBody, ApiOkResponse, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { DocumentSupplierDTO } from 'src/document/model/documentSupplier.model';

@Controller("api/v1/documentSupplier")
export class DocumentSupplierController {
    private logger = new Logger('DocumentSupplierController', { timestamp: true });
    constructor (private documentSupplierService: DocumentSupplierService) {
    }

    @ApiOperation({ summary: 'Add document supplier' })
    @ApiBody({ type: DocumentSupplierDTO })
    @ApiOkResponse({ type: BaseRes })
    @Post()
    public async addDocumentSupplier(@Body() documentSupplier: DocumentSupplier, @Req() req: LwRequest, @Res() res: Response) {
        if (!!documentSupplier) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            documentSupplier.organisationId = req.user['custom:organisationId'];
            var result = await this.documentSupplierService.createDocSupplier(documentSupplier);
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete document supplier by id' })
    @ApiQuery({ name: 'supplierId', description: 'Document Supplier ID' })
    @ApiOkResponse({ type: BaseRes })
    @Delete()
    public async deleteDocumentSupplier(@Query("supplierId") id: string, @Req() req: LwRequest, @Res() res: Response) {
        if (!id) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }

        try {
            var result = await this.documentSupplierService.deleteDocSupplier(id, req.user['custom:organisationId']);
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}

