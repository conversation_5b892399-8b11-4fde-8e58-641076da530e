import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { responseOk, responseError, LwRequest, BaseRes } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { DocumentTemplateService } from '../../service/documentTemplate.service';
import { DocumentTemplate } from '@prisma/client';
import { DocumentTemplateCreateDTO, DocumentTemplateVO,  DocumentTemplate as DocumentTemplateModel, DocumentTemplatePageQueryDTO, DocumentTemplateUpdateDTO } from '../../model/documentTemplate.model';
import { Logger } from '@nestjs/common';
import { ApiBody, ApiExtraModels, ApiOkResponse, ApiOperation, ApiQuery, getSchemaPath } from '@nestjs/swagger';
import { getDocTempUser, getDocTempUserList } from 'src/common/service/ddb.service';

@Controller("api/v1/documentTemplate")
@ApiExtraModels(BaseRes, DocumentTemplateModel, DocumentTemplateVO)
export class DocumentTemplateController {
    private logger = new Logger('DocumentTemplateController', { timestamp: true });
    constructor(private documentTemplateService: DocumentTemplateService) {
    }

    @ApiOperation({ summary: 'Get document template by id' })
    @ApiQuery({
            name: 'templateId',
            description: 'Document Template ID',
        })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(DocumentTemplateModel) },
                    },
                },
            ],
        },
    })
    @Get()
    public async getDocumentTemplate(@Query("templateId") id: string, @Req() req: LwRequest, @Res() res: Response) {
        if (!id) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            var documentTemplate = await this.documentTemplateService.getDocTemplateById(id, req.user['custom:organisationId']);
            responseOk(res, documentTemplate);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Add document template' })
    @ApiBody({ type: DocumentTemplateCreateDTO })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(DocumentTemplateVO) },
                    },
                },
            ],
        },
    })
    @Post()
    public async addDocumentTemplate(@Body() createDTO: DocumentTemplateCreateDTO, @Req() req: LwRequest, @Res() res: Response) {
        try {
            let documentTemplate = { 
                id: createDTO.id,
                title: createDTO.title,
                other: createDTO.other,
                status: createDTO.status,
                type: createDTO.type,
                contractTypes: createDTO.contractTypes,
                organisationId: req.user['custom:organisationId'],
                userId: createDTO.documentTemplateUserId,
             } as DocumentTemplate;
            const create = await this.documentTemplateService.createDocTemplate(documentTemplate);
            let docTempCreateVO = {
                id: create.id,
                title: create.title,
                other: create.other,
                status: create.status,
                type: create.type,
                contractTypes: create.contractTypes,
                createdAt: create.createdAt,
                updatedAt: create.updatedAt,
            } as DocumentTemplateVO;
            const user = await getDocTempUser(create.userId);
            if(user) {
                docTempCreateVO.user = user;
            }
            responseOk(res, docTempCreateVO);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get document template page' })
    @ApiQuery({ name: 'page', description: 'Page number' })
    @ApiQuery({ name: 'size', description: 'Page size' })
    @ApiQuery({ name: 'title', description: 'Document Template Title', required: false })
    @ApiQuery({ name: 'type', description: 'Document Template Type', required: false })
    @ApiQuery({ name: 'contractTypes', description: 'Document Template Contract Types', required: false, type: 'array' })
    @ApiQuery({ name: 'sort', description: 'Sort field', required: false, enum: ['title', 'type', 'status'] })
    @ApiQuery({ name: 'order', description: 'Sort order', required: false, enum: ['ASC', 'DESC'] })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { type: 'array', items: { $ref: getSchemaPath(DocumentTemplateVO) } },
                    },
                },
            ],
        },
    })
    @Get("page")
    public async getDocumentTemplatePage(
        @Query('page') page: string,
        @Query('size') size: string,
        @Req() req: LwRequest, 
        @Res() res: Response,
        @Query('title') title: string,
        @Query('type') type: string,
        @Query('contractTypes') contractTypes: string,
        @Query('sort') sort: string,
        @Query('order') order: string
    ) {
        try {
            if (!page || !size) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            let queryDTO = {
                organisationId: req.user['custom:organisationId'],
                page: parseInt(page),
                size: parseInt(size),
                title: title,
                type: type,
                contractTypes: contractTypes?.split(","),
                sort: sort,
                order: order
            } as DocumentTemplatePageQueryDTO;
            let templates = await this.documentTemplateService.getDocTemplatePage(queryDTO);
            if (templates.data?.length === 0) {
                responseOk(res, templates);
                return;
            }
            const templateUserIds = Array.from(
                new Set(templates.data.filter(tem => tem.userId).map(tem => tem.userId))
            );
            const userMap = Object.fromEntries(
                (await getDocTempUserList(templateUserIds)).map(user => [user.id, user])
            );
            const templateVOs = templates.data.map(template => {
                let templateVO = {
                    id: template.id,
                    title: template.title,
                    other: template.other,
                    status: template.status,
                    type: template.type,
                    contractTypes: template.contractTypes,
                    createdAt: template.createdAt,
                    updatedAt: template.updatedAt,
                } as DocumentTemplateVO;
                if (template.userId) {
                    templateVO.user = userMap[template.userId];
                }
                return templateVO;
            });
            templates.data = templateVOs;
            responseOk(res, templates);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update document template' })
    @ApiBody({ type: DocumentTemplateUpdateDTO })
    @ApiOkResponse({ type: BaseRes })
    @Put()
    public async updateDocumentTemplate(@Body() updateDTO: DocumentTemplateUpdateDTO, @Req() req: LwRequest, @Res() res: Response) {
        try {
            if (!updateDTO.id) {
                responseError(400, res, ResponseCode.PARAM_INVALID);
                return;
            }
            let updateDocTemp = {
                id: updateDTO.id,
                title: updateDTO.title? updateDTO.title : undefined,
                other: updateDTO.other? updateDTO.other : undefined,
                status: updateDTO.status? updateDTO.status : undefined,
                type: updateDTO.type? updateDTO.type : undefined,
                contractTypes: updateDTO.contractTypes? updateDTO.contractTypes : undefined,
            } as DocumentTemplate;
            
            const updateResult = await this.documentTemplateService.updateDocTemplate(updateDocTemp, req.user['custom:organisationId']);
            let docTempUpdateVO = {
                id: updateResult.id,
                title: updateResult.title,
                other: updateResult.other,
                status: updateResult.status,
                type: updateResult.type,
                contractTypes: updateResult.contractTypes,
                createdAt: updateResult.createdAt,
                updatedAt: updateResult.updatedAt,
            } as DocumentTemplateVO;
            if (updateResult.userId) {
                docTempUpdateVO.user = await getDocTempUser(updateResult.userId);
            }
            responseOk(res, docTempUpdateVO);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete document template by id' })
    @ApiQuery({ name: 'templateId', description: 'Document Template ID' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { type: 'object', properties: { id: { type: 'string' } }},
                    },
                },
            ],
        },
    })
    @Delete(':templateId')
    public async deleteDocumentTemplate(@Param("templateId") id:string, @Req() req: LwRequest, @Res() res: Response) {
        try {
            const deleteDocTemp = await this.documentTemplateService.deleteDocTemplate(id, req.user['custom:organisationId']);
            responseOk(res, { id: deleteDocTemp?.id });
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}