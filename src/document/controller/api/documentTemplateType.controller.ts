import { Body, Controller, Delete, Get, Logger, Param, Post, Put, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { DocumentTemplateTypeService } from '../../service/documentTemplateType.service';
import { BaseRes, LwRequest, responseError, responseOk } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { DocumentTemplateTypeVO, DocumentTemplateType as DocumentTemplateTypeModel, DocumentTemplateTypeCreateDTO } from 'src/document/model/documentTemplateType.model';
import { ApiBody, ApiExtraModels, ApiOkResponse, ApiOperation, getSchemaPath } from '@nestjs/swagger';
import { DocumentTemplateService } from 'src/document/service/documentTemplate.service';
import { DocumentTemplateType } from '@prisma/client';

@Controller("api/v1/documentTemplate/type")
@ApiExtraModels(BaseRes, DocumentTemplateTypeModel, DocumentTemplateTypeVO)
export class DocumentTemplateTypeController{
    private logger = new Logger('DocumentTemplateTypeController', { timestamp: true });
    
    constructor(
      private docTemplTypeService: DocumentTemplateTypeService, 
      private documentTemplateService: DocumentTemplateService
    ) {
    }

    @ApiOperation({ summary: 'Get document template types' })
    @ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(BaseRes) },
            {
              properties: {
                data: { type: 'array', items: { $ref: getSchemaPath(DocumentTemplateTypeVO) } },
              },
            },
          ],
        },
    })
    @Get('list')
    public async getDocumentTemplateTypes(@Req() req: LwRequest, @Res() res: Response) {
        try {
            const templateTypes = await this.docTemplTypeService.getDocumentTemplateTypes(req.user['custom:organisationId']);
            const typeVOs = templateTypes.map(type => {
              return {
                id: type.id,
                name: type.name,
                index: type.index
              } as DocumentTemplateTypeVO;
            })
            responseOk(res, typeVOs);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete document template type with id' })
    @ApiOkResponse({ type: BaseRes })
    @Delete('/:docTemplTypeId')
    public async deleteDocumentTemplateType(
      @Param('docTemplTypeId') docTemplTypeId: string,
      @Req() req: LwRequest, 
      @Res() res: Response
    ) {
      var orgId = req.user['custom:organisationId'];
      const docTemplateType = await this.docTemplTypeService.getDocumentTemplateTypeById(orgId, docTemplTypeId);
      if (!docTemplateType) {
          throw new Error("Document Template Type not found");
      }
      await this.documentTemplateService.deleteType(docTemplateType.name);
      await this.docTemplTypeService.deleteOrgDocumentTemplateType(orgId, docTemplateType);
      responseOk(res);
    }

    @ApiOperation({ summary: 'Update document template type with id' })
    @ApiOkResponse({ type: BaseRes })
    @Put('/:docTemplTypeId/order')
    public async updateTemplateTypeOrder(
      @Param('docTemplTypeId') docTemplTypeId: string,
      @Query("index") index: string,
      @Req() req: LwRequest, 
      @Res() res: Response
    ) {
      var orgId = req.user['custom:organisationId'];
      var newIdx = parseInt(index, 10)
      await this.docTemplTypeService.updateTypeOrder(orgId, docTemplTypeId, newIdx);
      responseOk(res)
    }

    @ApiOperation({ summary: 'Create document template type' })
    @ApiBody({ type: DocumentTemplateTypeCreateDTO })
    @ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(BaseRes) },
          {
            properties: {
              data: { type: 'array', items: { $ref: getSchemaPath(DocumentTemplateTypeVO) } },
            },
          },
        ],
      },
    })
    @Post("")
    public async createDocumentTemplateType(
      @Body() createDTO: DocumentTemplateTypeCreateDTO,
      @Req() req: LwRequest, 
      @Res() res: Response
    ) {
      try {
        const docTemplateType = {
          name: createDTO.name,
          index: createDTO.index,
          organisationId: req.user['custom:organisationId']
        } as DocumentTemplateType;
        const newType = await this.docTemplTypeService.createDocumentTemplateType(docTemplateType);
        let typeVo = {
          id: newType.id,
          name: newType.name,
          index: newType.index
        } as DocumentTemplateTypeVO;
        responseOk(res, typeVo);
      } catch (e) {
        this.logger.error(e);
        responseError(500, res, ResponseCode.SERVER_ERROR);
      }
    }
}

