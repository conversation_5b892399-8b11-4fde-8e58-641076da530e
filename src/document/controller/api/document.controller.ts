import {
    Body,
    Controller,
    Delete,
    Get,
    Logger,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import { Response } from 'express';
import { DocumentService } from '../../service/document.service';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { Document } from '@prisma/client';
import { getDocumentTypeList } from '../../enum/documentType';
import {
    Document as DocumentModel,
    DocumentCreateVO,
    DocumentDTO,
    DocumentPageQueryDTO,
    DocumentPageQueryVO,
    DocumentUpdateDTO,
} from '../../model/document.model';
import {
    ApiBody,
    ApiCreatedResponse,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    ApiQuery,
    getSchemaPath,
} from '@nestjs/swagger';
import { v1 as uuidV1 } from 'uuid';
import {
    getPropertiesBatch,
    getReminder,
    getUser,
} from 'src/common/service/ddb.service';
import { PermissionService } from 'src/document/service/permission.service';

const moment = require('moment');

@Controller('api/v1/document')
@ApiExtraModels(BaseRes, DocumentModel, DocumentCreateVO, DocumentPageQueryVO)
export class DocumentController {
    private logger = new Logger('DocumentController', { timestamp: true });
    constructor(
        private documentService: DocumentService,
        private permissionService: PermissionService,
    ) {}

    @ApiOperation({ summary: 'Get document by id' })
    @ApiQuery({
        name: 'documentId',
        description: 'Document ID',
    })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(DocumentModel) },
                    },
                },
            ],
        },
    })
    @Get()
    public async getDocumentById(
        @Query('documentId') documentId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        if (!documentId) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            var document =
                await this.documentService.getDocumentById(documentId);
            if (
                document.documentOrganisationId !==
                req.user['custom:organisationId']
            ) {
                responseError(404, res, ResponseCode.NOT_FOUND);
                return;
            }
            responseOk(res, document);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Get('/task/:taskId/document')
    public async getTaskDocument(
        @Param('taskId') taskId: string,
        @Res() res: Response,
    ) {
        try {
            const documents =
                await this.documentService.getTaskDocuments(taskId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Get('lineItem/:lineItemId/document')
    public async getLineItemDocuments(
        @Param('lineItemId') lineItemId: string,
        @Res() res: Response,
    ) {
        try {
            const documents =
                await this.documentService.getImageDocuments(lineItemId);
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @Get('emailAttachment/:attachmentId/document')
    public async getEmailAttachmentDocument(
        @Param('attachmentId') attachmentId: string,
        @Res() res: Response,
    ) {
        try {
            const document =
                await this.documentService.getEmailAttachmentDocument(
                    attachmentId,
                );
            responseOk(res, document);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get property documents' })
    @Get('property/:propertyId/document')
    public async getPropertyDocuments(
        @Param('propertyId') propertyId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const organisationId = req.user['custom:organisationId'];
            const documents =
                await this.documentService.getPropertyDocuments(propertyId);
            const reminder = await getReminder(organisationId);
            const result = documents
                .filter((it) => organisationId == it.documentOrganisationId)
                .map((it) => ({
                    ...it,
                    status: this.documentService.resolveStatus(it, reminder),
                }));
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get task documents' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { type: 'array', items: { type: 'string' } },
                    },
                },
            ],
        },
    })
    @Get('types')
    public async getDocumentTypes(@Res() res: Response) {
        try {
            var documentTypes = getDocumentTypeList();
            responseOk(res, documentTypes);
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('get document types fails');
        }
    }

    @ApiOperation({ summary: 'Get document by page' })
    @ApiQuery({
        name: 'page',
        description: 'Page number',
    })
    @ApiQuery({
        name: 'size',
        description: 'Page size',
    })
    @ApiQuery({
        name: 'name',
        description: 'Document name',
        required: false,
    })
    @ApiQuery({
        name: 'types',
        description: 'Document types',
        required: false,
        type: 'array',
    })
    @ApiQuery({
        name: 'propertyIds',
        description: 'Property IDs',
        required: false,
        type: 'array',
    })
    @ApiQuery({
        name: 'fromCreateDate',
        description: 'From create date',
        required: false,
    })
    @ApiQuery({
        name: 'toCreateDate',
        description: 'To create date',
        required: false,
    })
    @ApiQuery({
        name: 'daysRange',
        description: 'Days range',
        required: false,
    })
    @ApiQuery({
        name: 'archived',
        description: 'Archived',
        required: false,
    })
    @ApiQuery({
        name: 'orderDirection',
        description: 'Order by create time direction',
        required: false,
        enum: ['ASC', 'DESC'],
    })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: { $ref: getSchemaPath(DocumentPageQueryVO) },
                        },
                    },
                },
            ],
        },
    })
    @Get('page')
    public async getDocumentByPage(
        @Query('page') page: string,
        @Query('size') size: string,
        @Req() req: LwRequest,
        @Res() res: Response,
        @Query('name') name?: string,
        @Query('types') types?: string,
        @Query('propertyIds') propertyIds?: string,
        @Query('fromCreateDate') fromCreateDate?: string,
        @Query('toCreateDate') toCreateDate?: string,
        @Query('daysRange') daysRange?: string,
        @Query('archived') archived?: string,
        @Query('orderDirection') orderDirection?: string,
    ) {
        if (!page || !size) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            let queryDTO = new DocumentPageQueryDTO();
            queryDTO = {
                organisationId: req.user['custom:organisationId'],
                page: parseInt(page),
                size: parseInt(size),
                name: name,
                types: types ? types.split(',') : undefined,
                propertyIds: propertyIds ? propertyIds.split(',') : undefined,
                fromCreateDate: fromCreateDate,
                toCreateDate: toCreateDate,
                daysRange: daysRange ? parseInt(daysRange) : undefined,
                archived: archived === 'true' ? true : false,
                orderDirection: orderDirection,
            };

            const documents =
                await this.documentService.getDocumentByPage(queryDTO);

            const documentPropertyIds = Array.from(
                new Set(
                    documents.data
                        .filter((doc) => doc.documentPropertyId)
                        .map((doc) => doc.documentPropertyId),
                ),
            );
            const [propertiesBatch, reminder]: [any, any] = await Promise.all([
                getPropertiesBatch(documentPropertyIds),
                getReminder(req.user['custom:organisationId']),
            ]);

            const propertiesMap = Object.fromEntries(
                propertiesBatch.map((pro) => [pro.id, pro]),
            );

            const documentPermissionIds = Array.from(
                new Set(
                    documents.data
                        .filter((doc) => doc.documentPermissionId)
                        .map((doc) => doc.documentPermissionId),
                ),
            );
            const documentPermissionMap = Object.fromEntries(
                (
                    await this.permissionService.getPermissionsByIds(
                        documentPermissionIds,
                    )
                ).map((doc) => [doc.id, doc]),
            );

            const documentPageQueryVOs = [] as DocumentPageQueryVO[];

            for (let i = 0; i < documents.data?.length; i++) {
                const document = documents.data[i];
                let documentPageQueryVO = {} as DocumentPageQueryVO;
                documentPageQueryVO = {
                    ...DocumentPageQueryVO,
                    ...document,
                    permission:
                        documentPermissionMap[document.documentPermissionId],
                    property: propertiesMap[document.documentPropertyId],
                };
                documentPageQueryVO.listType =
                    this.documentService.assignListType(document.type);
                documentPageQueryVO.status = this.documentService.resolveStatus(
                    document,
                    reminder,
                );
                if (document.expiry) {
                    try {
                        const day = moment(document.expiry);
                        documentPageQueryVO.daysLeft = day.diff(
                            moment().utc(),
                            'days',
                        );
                    } catch (e) {
                        this.logger.error(
                            `get days left fails: ${e}, document: ${document.id}`,
                        );
                    }
                }

                documentPageQueryVOs.push(documentPageQueryVO);
            }
            documents.data = documentPageQueryVOs;
            responseOk(res, documents);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Create document' })
    @ApiBody({
        type: DocumentDTO,
        description: 'Data transfer object to create document',
    })
    @ApiCreatedResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(DocumentCreateVO) },
                    },
                },
            ],
        },
    })
    @Post()
    public async addDocument(
        @Body() documentDTO: DocumentDTO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        if (!documentDTO) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        const uuid = uuidV1();
        let newDocument = {} as Document;
        newDocument = {
            ...newDocument,
            ...documentDTO,
            id: uuid,
            documentOrganisationId: req.user['custom:organisationId'],
            mutator: req.user['custom:organisationId'],
        };
        try {
            const document =
                await this.documentService.createDocument(newDocument);
            let documentCreateVO = {} as DocumentCreateVO;
            documentCreateVO = {
                ...documentCreateVO,
                ...document,
            };
            if (document.documentUserId) {
                try {
                    const user = await getUser(document.documentUserId);
                    documentCreateVO.user = {
                        id: user.id,
                        email: user.email,
                    };
                } catch (e) {
                    this.logger.error(
                        `get user [${document.documentUserId}] fails: ${e}`,
                    );
                }
            }
            if (document.documentTenancyId) {
                documentCreateVO.tenancy = {
                    id: document.documentTenancyId,
                };
            }
            if (document.documentPropertyId) {
                documentCreateVO.property = {
                    id: document.documentPropertyId,
                };
            }
            responseOk(res, documentCreateVO);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update document' })
    @ApiBody({
        type: DocumentUpdateDTO,
        description: 'Data transfer object to update document',
    })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(DocumentModel) },
                    },
                },
            ],
        },
    })
    @Put()
    public async updateDocument(
        @Body() documentUpdateDTO: DocumentUpdateDTO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        if (!documentUpdateDTO?.id) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }

        try {
            const item = await this.documentService.getDocumentById(
                documentUpdateDTO.id,
            );
            if (
                item.documentOrganisationId !==
                req.user['custom:organisationId']
            ) {
                responseError(404, res, ResponseCode.NOT_FOUND);
            }
            let document = { ...documentUpdateDTO } as Document;
            document.mutator = req.user['custom:organisationId'];
            if ('expiry' in document && document.expiry) {
                document.expiry = new Date(document.expiry);
            }
            const update = await this.documentService.updateDocument(document);
            responseOk(res, update);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete document by id' })
    @ApiQuery({
        name: 'documentId',
        description: 'Document ID',
    })
    @ApiOkResponse({ type: BaseRes })
    @Delete()
    public async deleteDocument(
        @Query('documentId') documentId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        if (!documentId) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }

        try {
            await this.documentService.deleteDocument(
                documentId,
                req.user['custom:organisationId'],
            );
            responseOk(res);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
