export enum TenancyType {
    NA = "NA",
    RESIDENTIAL = "RESIDENTI<PERSON>",
    FIRM_TERM = "FIRM_TERM",
    MONTH_TO_MONTH = "MONTH_TO_MONTH",
    SUBLET = "SUBLET",
    VACATION_STAY = "VACATION_STAY",
    RENEWAL = "RENEWAL",
    JOINT = "JOINT",
    RENT_TO_OWN = "RENT_TO_OWN",
    DPS_CUSTODIAL = "DPS_CUSTODIAL",
    PROJECT = "PROJECT",
    APARTMENT = "APARTMENT",
    RESIDENTIAL_CONTRACT = "RESIDENTIAL_CONTRACT",
    SECTION_8 = "SECTION_8",
    DPS_INSURANCE = "DPS_INSURANCE",
    MY_DEPOSITS = "MY_DEPOSITS",
    TDS_CUSTODIAL = "TDS_CUSTODIAL",
    TDS_INSURANCE = "TDS_INSURANCE",
    REPOSIT = "REPOSIT",
    DISPUTE_SERVICE_CUSTODIAL = "DISPUTE_SERVICE_CUSTODIAL",
    INSURANCE = "INSURANCE",
    HELD_BY_AGENT = "HELD_BY_AGENT",
    HELD_BY_LANDLORD = "HELD_BY_LANDLORD",
    AST = "AST",
    ASSURED = "ASSURED",
    CONTRACTUAL = "CONTRACTUAL",
    COMMON_LAW = "COMMON_LAW",
    LICENSE = "LICENSE",
    COMMERCIAL = "COMMERCIAL",
    SERVICE_CHARGE = "SERVICE_CHARGE",
    HOLIDAYLET = "HOLIDAYLET",
    NONHOUSINGACT = "NONHOUSINGACT",
    SALE = "SALE",
    LEASE = "LEASE",
    GROUND_RENT = "GROUND_RENT"
}

export function getTenancyType(key: string): TenancyType | undefined {
    return TenancyType[key as keyof typeof TenancyType];
  }