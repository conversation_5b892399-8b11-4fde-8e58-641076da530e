export enum DocumentType {
    // Compliance Documents - Core
    GAS_SAFETY_CERTIFICATE = "GAS_SAFETY_CERTIFICATE",
    EICR = "EICR",
    EPC = "EPC",
    SMOKE_ALARM = "SMOKE_ALARM",
    LEGIONELLA = "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    FIRE_RISK_ASSESSMENT = "FIRE_RISK_ASSESSMENT",
    PAT = "PAT",

    // Compliance Documents - Additional
    CARBON_MONOXIDE_ALARM = "CARBON_MONOXIDE_ALARM",
    BOILER_SERVICE = "BOILER_SERVICE",
    INSURANCE_CERTIFICATES = "INSURANCE_CERTIFICATES",
    BUILDING_REGULATIONS = "BUILDING_REGULATIONS",
    PROPERTY_LICENSING = "PROPERTY_LICENSING",
    FENSA = "FENSA",
    SEWAGE_DRAINAGE = "SEWAGE_DRAINAGE",
    ASBESTOS_SURVEY = "ASBESTOS_SURVEY",
    ASBESTOS_MANAGEMENT = "ASBESTOS_MANAGEMENT",
    FIRE_DOOR_INSPECTION = "FIRE_DOOR_INSPECTION",
    EMERGENCY_LIGHTING = "EMERGENCY_LIGHTING",
    SECURITY_SYSTEM = "SECURITY_SYSTEM",
    SOUNDPROOFING = "SOUNDPROOFING",
    RENEWABLE_ENERGY = "RENEWABLE_ENERGY",

    // UK - General
    METER_READING = "METER_READING",
    TENANT_RIGHTS = "TENANT_RIGHTS",
    GUIDE = "GUIDE",
    ID = "ID",
    TS_AND_CS = "TS_AND_CS",
    PROOF_OF_ADDRESS = "PROOF_OF_ADDRESS",
    PROOF_OF_OWNERSHIP = "PROOF_OF_OWNERSHIP",
    APPLICATION_FOR_TENANCY = "APPLICATION_FOR_TENANCY",
    REFERENCE = "REFERENCE",
    RIGHT_TO_RENT = "RIGHT_TO_RENT",
    EMPLOYMENT_CONFIRMATION = "EMPLOYMENT_CONFIRMATION",
    MANAGEMENT_CONTRACT = "MANAGEMENT_CONTRACT",
    STUDIES_CONFIRMATION = "STUDIES_CONFIRMATION",
    LANDLORD_REFERENCE = "LANDLORD_REFERENCE",
    CHARACTER_REFERENCE = "CHARACTER_REFERENCE",
    WORK_ORDER = "WORK_ORDER",
    QUOTE = "QUOTE",
    DEED_OF_ASSIGNMENT = "DEED_OF_ASSIGNMENT",
    LICENSE_ALTERATION = "LICENSE_ALTERATION",
    RENT_DEED_DEPOSIT = "RENT_DEED_DEPOSIT",
    LICENSE_TO_ASSIGN = "LICENSE_TO_ASSIGN",
    INSPECTION = "INSPECTION",
    GAS_SAFETY = "GAS_SAFETY",
    PHOTO = "PHOTO",
    CHECK_IN = "CHECK_IN",
    CHECK_OUT = "CHECK_OUT",
    INSURANCE = "INSURANCE",
    STATEMENT = "STATEMENT",
    BROCHURE = "BROCHURE",
    ESTIMATE = "ESTIMATE",
    HEALTH_AND_SAFETY_REPORT = "HEALTH_AND_SAFETY_REPORT",
    RENTAL_AGREEMENT = "RENTAL_AGREEMENT",
    GAS_CERTIFICATE = "GAS_CERTIFICATE",
    ELECTRICITY_CERTIFICATE = "ELECTRICITY_CERTIFICATE",
    ENERGY_CERTIFICATE = "ENERGY_CERTIFICATE",
    INVENTORY_INSPECTION = "INVENTORY_INSPECTION",
    PROPERTY_BROCHURE = "PROPERTY_BROCHURE",
    WORK_ESTIMATE_REPORT = "WORK_ESTIMATE_REPORT",
    ASBESTOS_CHECK = "ASBESTOS_CHECK",
    UTILITY_BILL = "UTILITY_BILL",
    ENGLISH_INVOICE = "ENGLISH_INVOICE",
    FORCE_MAJEURE = "FORCE_MAJEURE",
    MISCELLANEOUS = "MISCELLANEOUS",
    SERVICE_CHARGE_BUDJET = "SERVICE_CHARGE_BUDJET",
    BLOCK_BUDJET = "BLOCK_BUDJET",
    FLOOR_PLAN = "FLOOR_PLAN",
    INSURANCE_DOCUMENT = "INSURANCE_DOCUMENT",
    CONTRACT = "CONTRACT",
    WORKS_ESTIMATE = "WORKS_ESTIMATE",
    ASBESTOS_REPORT = "ASBESTOS_REPORT",
    HEALTH_AND_SAFETY_AUDIT = "HEALTH_AND_SAFETY_AUDIT",
    PROPERTY_DETAILS = "PROPERTY_DETAILS",
    BUDGET = "BUDGET",
    SUPPLIER_INSURANCE = "SUPPLIER_INSURANCE",
    METHOD_STATEMENT = "METHOD_STATEMENT",
    PROPOSAL = "PROPOSAL",
    INSTRUCTIONS = "INSTRUCTIONS",
    WARRANTY_DOCUMENT = "WARRANTY_DOCUMENT",
    CHECKIN_REPORT = "CHECKIN_REPORT",
    CHECKOUT_REPORT = "CHECKOUT_REPORT",
    GUIDANCE_DOCUMENT = "GUIDANCE_DOCUMENT",
    ELECTRICAL_CONDITION = "ELECTRICAL_CONDITION",
    INVOICE = "INVOICE",
    OTHER = "OTHER",
    CHIMNEY_SWEEP = "CHIMNEY_SWEEP",
    CLIENTS_STATEMENT = "CLIENTS_STATEMENT",
    CLIENTS_GUIDE = "CLIENTS_GUIDE",
    TENANTS_INSURANCE = "TENANTS_INSURANCE",
    TENANTS_GUIDE = "TENANTS_GUIDE",
    MARKETING_PHOTO = "MARKETING_PHOTO",
    SUPPLIER_CONTRACT = "SUPPLIER_CONTRACT",
    SUPPLIER_INVOICE = "SUPPLIER_INVOICE",

    // US
    RENTAL_LICENSE = "RENTAL_LICENSE",
    RENTERS_INSURANCE = "RENTERS_INSURANCE",
    HOA_VIOLATION_NOTICE = "HOA_VIOLATION_NOTICE",
    TENANT_IDENTIFICATION = "TENANT_IDENTIFICATION",
    INSPECTION_REPORT = "INSPECTION_REPORT",
    INSURANCE_POLICY = "INSURANCE_POLICY",
    MAINTENANCE_RECORD = "MAINTENANCE_RECORD",
    TITLE_DEED = "TITLE_DEED",
    WARRANTY_INFORMATION = "WARRANTY_INFORMATION",
    RULES_AND_REGULATIONS = "RULES_AND_REGULATIONS",
    LEASE_TEMPLATES = "LEASE_TEMPLATES",
    HISTORIC_MARKETING_MATERIALS = "HISTORIC_MARKETING_MATERIALS",
    UTILITY_INFORMATION = "UTILITY_INFORMATION",
    FINANCIAL_STATEMENTS = "FINANCIAL_STATEMENTS",
    TAX_DOCUMENTS = "TAX_DOCUMENTS",
    VENDOR_CONTRACTS = "VENDOR_CONTRACTS",
    LANDLORD_INSURANCE_POLICY = "LANDLORD_INSURANCE_POLICY",
    LANDLORD_TENANT_COMMUNICATIONS = "LANDLORD_TENANT_COMMUNICATIONS",
    LEGAL_DOCUMENTS = "LEGAL_DOCUMENTS",
    LEASE_AGREEMENT = "LEASE_AGREEMENT",
    MOVE_IN_INSPECTION_CHECKLIST = "MOVE_IN_INSPECTION_CHECKLIST",
    MOVE_OUT_INSPECTION_CHECKLIST = "MOVE_OUT_INSPECTION_CHECKLIST",
    RENT_PAYMENT_RECEIPTS = "RENT_PAYMENT_RECEIPTS",
    NOTICE_OF_RENT_INCREASES = "NOTICE_OF_RENT_INCREASES",
    SECURITY_DEPOSIT_RECEIPT_AND_STATEMENT = "SECURITY_DEPOSIT_RECEIPT_AND_STATEMENT",
    RULES_AND_REGULATION_FOR_TENANTS = "RULES_AND_REGULATION_FOR_TENANTS",
    NOTICES_OF_ENTRY_AND_MAINTENANCE = "NOTICES_OF_ENTRY_AND_MAINTENANCE",
    TENANT_PRIVACY_RIGHTS_INFO = "TENANT_PRIVACY_RIGHTS_INFO",
    EMERGENCY_CONTACT_INFORMATION = "EMERGENCY_CONTACT_INFORMATION",
    UTILITY_BILLING_INFORMATION = "UTILITY_BILLING_INFORMATION",
    TENANT_APPLICATION_FORMS = "TENANT_APPLICATION_FORMS",
    TENANT_BACKGROUND_CHECKS = "TENANT_BACKGROUND_CHECKS",
    MAINTENANCE_REQUESTS_AND_RECORDS = "MAINTENANCE_REQUESTS_AND_RECORDS",
    LEGAL_CONTRACTS_AND_AGREEMENTS = "LEGAL_CONTRACTS_AND_AGREEMENTS",
    PROPERTY_MAINTENANCE_INVOICES = "PROPERTY_MAINTENANCE_INVOICES",
    PROPERTY_INSPECTION_PHOTOS = "PROPERTY_INSPECTION_PHOTOS",
    EVICTION_NOTICES_AND_RECORDS = "EVICTION_NOTICES_AND_RECORDS",
    PROPERTY_PURCHASE_DOCUMENT = "PROPERTY_PURCHASE_DOCUMENT",
    ENVIRONMENTAL_COMPLIANCE_DOCUMENTS = "ENVIRONMENTAL_COMPLIANCE_DOCUMENTS",
    OTHER_CORRESPONDENCE_AND_COMMUNICATIONS = "OTHER_CORRESPONDENCE_AND_COMMUNICATIONS",
    MISCELLANEOUS_PROPERTY_DOCUMENTS = "MISCELLANEOUS_PROPERTY_DOCUMENTS",
    LANDLORD_TAX_DOCUMENTS = "LANDLORD_TAX_DOCUMENTS"
}

export function getDocumentType(key: string): DocumentType | undefined {
    return DocumentType[key as keyof typeof DocumentType];
}

const COMPLIANCE_CERTIFICATES = new Set<DocumentType>([
    DocumentType.GAS_SAFETY_CERTIFICATE,
    DocumentType.EICR,
    DocumentType.EPC,
    DocumentType.SMOKE_ALARM,
    DocumentType.LEGIONELLA,
    DocumentType.FIRE_RISK_ASSESSMENT,
    DocumentType.PAT,
    DocumentType.CARBON_MONOXIDE_ALARM,
    DocumentType.BOILER_SERVICE,
    DocumentType.INSURANCE_CERTIFICATES,
    DocumentType.BUILDING_REGULATIONS,
    DocumentType.PROPERTY_LICENSING,
    DocumentType.FENSA,
    DocumentType.SEWAGE_DRAINAGE,
    DocumentType.ASBESTOS_SURVEY,
    DocumentType.ASBESTOS_MANAGEMENT,
    DocumentType.FIRE_DOOR_INSPECTION,
    DocumentType.EMERGENCY_LIGHTING,
    DocumentType.SECURITY_SYSTEM,
    DocumentType.SOUNDPROOFING,
    DocumentType.RENEWABLE_ENERGY,
]);

export function isComplianceCertificate(certType: DocumentType): boolean {
    return COMPLIANCE_CERTIFICATES.has(certType);
}

export function getDocumentTypeList(): string[] | undefined {
    const enumNames: string[] = [];
    for (let value in DocumentType) {
        enumNames.push(value);
    }
    return enumNames;
}