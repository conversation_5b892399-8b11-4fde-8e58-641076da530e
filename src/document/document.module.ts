import { Module } from '@nestjs/common';
import { DocumentSupplierController } from './controller/api/documentSupplier.controller';
import { DocumentTemplateTypeController } from './controller/api/documentTemplateType.controller';
import { DocumentSupplierService } from './service/documentSupplier.service';
import { DocumentTemplateTypeService } from './service/documentTemplateType.service';
import { DocumentTemplateController } from './controller/api/documentTemplate.controller';
import { DocumentTemplateService } from './service/documentTemplate.service';
import { DocumentService } from './service/document.service';
import { DocumentController } from './controller/api/document.controller';
import { DocumentInternalController } from './controller/client/document.internal.controller';
import { DocumentTemplateTypeInternalController } from './controller/client/documentTemplateType.internal.controller';
import { DocumentTemplateInternalController } from './controller/client/documentTemplate.internal.controller';
import { PermissionController } from './controller/api/permission.controller';
import { PermissionService } from './service/permission.service';
import { PermissionInternalController } from './controller/client/permission.internal.controller';
import { DocumentSupplierInternalController } from './controller/client/documentSupplier.internal.controller';
import { EventsEmitterService } from '../common/service/eventsEmitter.service';

@Module({
    controllers: [
        DocumentSupplierController,
        DocumentTemplateTypeController,
        DocumentTemplateController,
        DocumentController,
        PermissionController,

        DocumentInternalController,
        DocumentSupplierInternalController,
        DocumentTemplateTypeInternalController,
        DocumentTemplateInternalController,
        PermissionInternalController,
    ],
    providers: [
        DocumentSupplierService,
        DocumentTemplateTypeService,
        DocumentTemplateService,
        DocumentService,
        PermissionService,
        EventsEmitterService,
    ],
    exports: [DocumentService],
})
export class DocumentModule {}
