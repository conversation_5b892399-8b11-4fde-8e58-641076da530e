import { ApiProperty } from '@nestjs/swagger';
import { Document as LwDocument } from '@prisma/client';
import { DocumentStatus, getDocumentStatus } from '../enum/documentStatus';
import { getDocumentTypeList } from '../enum/documentType';
import { Permission } from './permission.model';

export class DocumentPageQueryDTO {
    organisationId: string;
    @ApiProperty()
    page: number;
    @ApiProperty()
    size: number;
    @ApiProperty({ required: false })
    name?: string;
    @ApiProperty({
        type: [String],
        enum: getDocumentTypeList(),
        required: false,
    })
    types?: string[];
    @ApiProperty({
        type: [String],
        required: false,
    })
    propertyIds?: string[];
    @ApiProperty({ required: false })
    fromCreateDate?: string;
    @ApiProperty({ required: false })
    toCreateDate?: string;
    @ApiProperty({ required: false })
    daysRange?: number;
    @ApiProperty({ required: false })
    archived?: boolean;
    @ApiProperty({ required: false })
    orderDirection?: string;
}

export class CreateConversationAttachmentDTO {
    conversationId: string;
    cognitoUserId: string;
    document: LwDocument;
}

export class DocumentDTO {
    @ApiProperty()
    key: string;

    @ApiProperty({ required: false })
    name?: string;

    @ApiProperty({ required: false })
    type?: string;

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    description?: string;

    @ApiProperty()
    mimeType: string;

    @ApiProperty({ required: false })
    archived?: boolean;

    @ApiProperty({ required: false })
    expiry?: Date;

    @ApiProperty({ required: false })
    validFrom?: Date;

    @ApiProperty({ required: false })
    done?: boolean;

    @ApiProperty({ required: false })
    documentTenancyId?: string;

    @ApiProperty({ required: false })
    documentPropertyId?: string;

    @ApiProperty({ required: false })
    documentSupplierOrganisationId?: string;

    @ApiProperty({ required: false })
    documentOrganisationId?: string;

    @ApiProperty({ required: false })
    documentUserId?: string;

    @ApiProperty({ required: false })
    documentConversationId?: string;

    @ApiProperty({ required: false })
    documentEmailAttachmentId?: string;

    @ApiProperty({ required: false })
    imageTaskId?: string;

    @ApiProperty({ required: false })
    generalDocument?: boolean;

    @ApiProperty({ required: false })
    isParentPropertyDocumentShared?: boolean;

    @ApiProperty({ required: false })
    imageOrganisationId?: string;

    @ApiProperty({ required: false })
    parentPropertyEntityId?: string;

    @ApiProperty({ required: false })
    documentPermissionId?: string;

    @ApiProperty({ required: false })
    documentTaskId?: string;

    @ApiProperty({ required: false })
    documentLineItemId?: string;
}

export class DocumentCreateDTO {
    @ApiProperty({
        required: true,
        description: 'Document object',
        type: DocumentDTO,
    })
    document: DocumentDTO;
    @ApiProperty({
        type: String,
        required: false,
        description: 'Conversation ID',
    })
    conversationId?: string;
}

export class Document {
    @ApiProperty()
    id: string;

    @ApiProperty()
    key: string;

    @ApiProperty({ required: false })
    name?: string;

    @ApiProperty({ required: false })
    type?: string;

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    description?: string;

    @ApiProperty()
    mimeType: string;

    @ApiProperty({ required: false })
    archived?: boolean;

    @ApiProperty({ required: false })
    createdAt?: Date;

    @ApiProperty({ required: false })
    updatedAt?: Date;

    @ApiProperty({ required: false })
    expiry?: Date;

    @ApiProperty({ required: false })
    validFrom?: Date;

    @ApiProperty({ required: false })
    done?: boolean;

    @ApiProperty({ required: false })
    documentTenancyId?: string;

    @ApiProperty({ required: false })
    documentPropertyId?: string;

    @ApiProperty({ required: false })
    documentSupplierOrganisationId?: string;

    @ApiProperty({ required: false })
    documentOrganisationId?: string;

    @ApiProperty({ required: false })
    documentUserId?: string;

    @ApiProperty({ required: false })
    documentConversationId?: string;

    @ApiProperty({ required: false })
    documentEmailAttachmentId?: string;

    @ApiProperty({ required: false })
    imageTaskId?: string;

    @ApiProperty({ required: false })
    generalDocument?: boolean;

    @ApiProperty({ required: false })
    isParentPropertyDocumentShared?: boolean;

    @ApiProperty({ required: false })
    imageOrganisationId?: string;

    @ApiProperty({ required: false })
    parentPropertyEntityId?: string;

    @ApiProperty({ required: false })
    documentPermissionId?: string;

    @ApiProperty({ required: false })
    documentTaskId?: string;

    @ApiProperty({ required: false })
    documentLineItemId?: string;
}

export class DocumentUser {
    @ApiProperty()
    id: string;

    @ApiProperty( { required: false })
    email?: string;
}

export class DocumentProperty {
    @ApiProperty()
    id: string;
}

export class DocumentTenancy {
    @ApiProperty()
    id: string;
}

export class DocumentCreateVO extends Document {
    @ApiProperty()
    user: DocumentUser;

    @ApiProperty()
    property: DocumentProperty;

    @ApiProperty()
    tenancy: DocumentTenancy;
}

export class Property {
    @ApiProperty()
    id: string;

    @ApiProperty()
    addressLine1: string;

    @ApiProperty()
    addressLine2: string;

    @ApiProperty()
    addressLine3: string;

    @ApiProperty()
    associatedUserId: string;

    @ApiProperty()
    city: string;

    @ApiProperty()
    country: string;

    @ApiProperty()
    postcode: string;

    @ApiProperty()
    propertyOrganisationId: string;

    @ApiProperty()
    propertyOwnerId: string;

    @ApiProperty()
    reference: string;

    @ApiProperty()
    status: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    updatedAt: Date;
}


export class DocumentPageQueryVO extends Document {
    @ApiProperty({ required: false })
    daysLeft?: number;

    @ApiProperty()
    listType: string;

    @ApiProperty()
    permission?: Permission;

    @ApiProperty()
    property?: Property;
}

export class DocumentUpdateDTO {
    @ApiProperty()
    id: string;

    @ApiProperty({ required: false })
    key: string;

    @ApiProperty({ required: false })
    name?: string;

    @ApiProperty({ required: false })
    type?: string;

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    description?: string;

    @ApiProperty({ required: false })
    mimeType: string;

    @ApiProperty({ required: false })
    archived?: boolean;

    @ApiProperty({ required: false })
    expiry?: Date;

    @ApiProperty({ required: false })
    validFrom?: Date;

    @ApiProperty({ required: false })
    done?: boolean;

    @ApiProperty({ required: false })
    documentTenancyId?: string;

    @ApiProperty({ required: false })
    documentPropertyId?: string;

    @ApiProperty({ required: false })
    documentSupplierOrganisationId?: string;

    @ApiProperty({ required: false })
    documentOrganisationId?: string;

    @ApiProperty({ required: false })
    documentUserId?: string;

    @ApiProperty({ required: false })
    documentConversationId?: string;

    @ApiProperty({ required: false })
    documentEmailAttachmentId?: string;

    @ApiProperty({ required: false })
    imageTaskId?: string;

    @ApiProperty({ required: false })
    generalDocument?: boolean;

    @ApiProperty({ required: false })
    isParentPropertyDocumentShared?: boolean;

    @ApiProperty({ required: false })
    imageOrganisationId?: string;

    @ApiProperty({ required: false })
    parentPropertyEntityId?: string;

    @ApiProperty({ required: false })
    documentPermissionId?: string;

    @ApiProperty({ required: false })
    documentTaskId?: string;

    @ApiProperty({ required: false })
    documentLineItemId?: string;
}