import { ApiProperty } from '@nestjs/swagger';

export class DocumentTemplateType {
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    index: number;

    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    createdAt?: Date;

    @ApiProperty()
    updatedAt?: Date;
}

export class DocumentTemplateTypeVO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    index: number;
}

export class DocumentTemplateTypeCreateDTO {
    @ApiProperty()
    name: string;

    @ApiProperty()
    index: number;
}