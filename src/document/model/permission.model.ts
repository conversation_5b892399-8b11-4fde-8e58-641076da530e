import { ApiProperty } from "@nestjs/swagger";
import { Role } from "../enum/role";

export class PermissionDTO {
    @ApiProperty({ required: false })
    itemType?: string;

    @ApiProperty( { enum: Role})
    roles: string[];
}

export class Permission {
    @ApiProperty()
    id: string;
    
    @ApiProperty()
    itemType?: string;
    
    @ApiProperty()
    groups: string[];
    
    @ApiProperty()
    createdAt?: Date;

    @ApiProperty()
    updatedAt?: Date;
}

export class PermissionUpdateDTO {
    @ApiProperty()
    id: string;
    
    @ApiProperty({ required: false })
    itemType?: string;
    
    @ApiProperty({ required: false })
    groups?: string[];
}