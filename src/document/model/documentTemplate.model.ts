import { ApiProperty } from '@nestjs/swagger';

export class DocumentTemplatePageQueryDTO {
    organisationId: string;
    @ApiProperty()
    page: number;
    @ApiProperty()
    size: number;
    @ApiProperty({ required: false })
    title?: string;
    @ApiProperty({ required: false })
    type?: string;
    @ApiProperty({
        type: [String],
        required: false,
    })
    contractTypes?: string[];
    sort: string;
    order: string;
}

export class DocumentTemplate {
    @ApiProperty()
    id: string;

    @ApiProperty()
    title: string;

    @ApiProperty()
    type?: string;

    @ApiProperty()
    other: string[];

    @ApiProperty()
    status: string;

    @ApiProperty()
    contractTypes: string[];

    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    userId?: string;

    @ApiProperty()
    docusignTemplateId?: string;

    @ApiProperty()
    createAt?: Date;

    @ApiProperty()
    updatedAt?: Date;
}

export class DocumentTemplateCreateDTO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    title: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    other: string[];

    @ApiProperty()
    status: string;

    @ApiProperty()
    contractTypes: string[];

    @ApiProperty()
    documentTemplateUserId: string;
}

export class DocumentTemplateUser {
    @ApiProperty()
    id: string;

    @ApiProperty()
    fname: string;

    @ApiProperty()
    sname: string;

    @ApiProperty()
    cognitoEmail: string;

    @ApiProperty({
        type: 'array',
        items: { type: 'object', properties: { email: { type: 'string' } } },
    })
    emails: object[];
}

export class DocumentTemplateVO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    title: string;

    @ApiProperty()
    type: string;

    @ApiProperty()
    other: string[];

    @ApiProperty()
    status: string;

    @ApiProperty()
    contractTypes: string[];

    @ApiProperty()
    user: DocumentTemplateUser;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    updatedAt: Date;
}

export class DocumentTemplateUpdateDTO {
    @ApiProperty()
    id: string;

    @ApiProperty({ required: false })
    title: string;

    @ApiProperty({ required: false })
    type: string;

    @ApiProperty({ required: false })
    other: string[];

    @ApiProperty({ required: false })
    status: string;

    @ApiProperty({ required: false })
    contractTypes: string[];
}