import { DocumentTemplate, Prisma } from "@prisma/client";
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from "src/common/service/prisma.service";
import { DocumentTemplatePageQueryDTO } from "../model/documentTemplate.model";
import { Page } from "src/common/util/requestUtil";

@Injectable()
export class DocumentTemplateService {
    private logger = new Logger('DocumentTemplateService', { timestamp: true });
    private TABLE_NAME = Prisma.raw("document_template")
    constructor(private prisma: PrismaService) {
    }

    async createDocTemplate(documentTemplate: DocumentTemplate): Promise<DocumentTemplate> {
        try {
            return await this.prisma.documentTemplate.create({
                data: documentTemplate
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db insert fails");
        }
    }

    async deleteDocTemplate(documentTemplateId: string, organisationId: string): Promise<DocumentTemplate> {
        try {
            return await this.prisma.documentTemplate.delete({
                where: {
                    id: documentTemplateId,
                    organisationId: organisationId
                }
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db delete fails");
        }
    }

    async getDocTemplateById(documentTemplateId: string, organisationId: string): Promise<DocumentTemplate> {
        try {
            return await this.prisma.documentTemplate.findUnique({
                where: {
                    id: documentTemplateId,
                    organisationId: organisationId
                }
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db query fails");
        }
    }

    async updateDocTemplate(documentTemplate: DocumentTemplate, organisationId: string): Promise<DocumentTemplate> {
        try {
            return await this.prisma.documentTemplate.update({
                where: {
                    id: documentTemplate.id,
                    organisationId: organisationId
                },
                data: documentTemplate
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db update fails");
        }
    }

    async getDocTemplatePage(query: DocumentTemplatePageQueryDTO): Promise<Page> {
        try {
            const filters: any = {};
            filters.organisationId = {
                equals: query.organisationId
            }
            if (query.title) {
                filters.title = {
                    contains: query.title
                }
            }
            if (query.type) {
                filters.type = {
                    equals: query.type
                }
            }
            if (query.contractTypes?.length > 0) {
                filters.contractTypes = {
                    hasSome: query.contractTypes
                }
            }
            const orderDirection = query.order === "asc" ? "asc" : "desc";
            const orderBy = query.sort ? query.sort : "createdAt"; 

            const [data, total] = await Promise.all([
                this.prisma.documentTemplate.findMany({
                    skip: (query.page - 1) * query.size,
                    take: query.size,
                    where: filters,
                    orderBy: {
                        [orderBy]: orderDirection
                    }
                }),
                this.prisma.documentTemplate.count({
                    where: filters
                })
            ]);
            return new Page(total, data);
        } catch (e) {
            this.logger.error(e);
            throw new Error("db query fails");
        }
    }

    async getDocTemplates(organisationId: string): Promise<DocumentTemplate[]> {
        try {
            return await this.prisma.documentTemplate.findMany({
                where: {
                    organisationId: organisationId
                }
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db query fails");
        }
    }

    async getUserDocTemplates(userId: string): Promise<DocumentTemplate[]> {
        try {
            return await this.prisma.documentTemplate.findMany({
                where: {
                    userId: userId
                }
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db query fails");
        }
    }

    async deleteType(typeName: string): Promise<any> {
        try {
            return await this.prisma.documentTemplate.updateMany({
                where: {
                    type : typeName
                },
                data: {
                    type: null
                }
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db delete fails");
        }
    }

    async deleteOrganisationAllTemplates(orgId: string): Promise<any> {
        try {
            return await this.prisma.documentTemplate.deleteMany({
                where: {
                    organisationId: orgId
                }
            });
        } catch (e) {
            this.logger.error(e);
            throw new Error("db delete fails");
        }
    }
}