import { DocumentSupplier } from "@prisma/client";
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from "src/common/service/prisma.service";

@Injectable()
export class DocumentSupplierService {
    private logger = new Logger('DocumentSupplierService', { timestamp: true });
    constructor(private prisma: PrismaService) {
    }

    async createDocSupplier(documentSupplier: DocumentSupplier): Promise<DocumentSupplier> {
        try {
            return await this.prisma.documentSupplier.create({
                data: {
                    id: documentSupplier.id,
                    userId: documentSupplier.userId,
                    documentId: documentSupplier.documentId,
                    organisationId: documentSupplier.organisationId
                }
            });
        } catch (e) {
            this.logger.error(e.stack)
            throw new Error("db insert fails");
        }
    }
    
    async deleteDocSupplier(documentSupplierId: string, organisationId: string): Promise<DocumentSupplier> {
        try {
            return await this.prisma.documentSupplier.delete({
                where: {
                    id: documentSupplierId,
                    organisationId: organisationId
                }
            });
        } catch (e) {
            this.logger.error(e.stack)
            throw new Error("db delete fails");
        }
    }

    async deleteOrganisationAllDocSuppliers(organisationId: string): Promise<any> {
        try {
            return await this.prisma.documentSupplier.deleteMany({
                where: {
                    organisationId: organisationId
                }
            });
        } catch (e) {
            this.logger.error(e.stack)
            throw new Error("db delete fails");
        }
    }

    async getOrgDocumentSuppliers(orgId: string): Promise<DocumentSupplier[]> {
        try {
            return await this.prisma.documentSupplier.findMany({
                where: {
                    organisationId: orgId
                }
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error("db query fails");
        }
    }

    async getUserDocumentSuppliers(userId: string): Promise<DocumentSupplier[]> {
        try {
            return await this.prisma.documentSupplier.findMany({
                where: {
                    userId: userId
                }
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error("db query fails");
        }
    }
}