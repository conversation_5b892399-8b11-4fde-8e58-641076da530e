import { Document } from '@prisma/client';
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/common/service/prisma.service';
import { DocumentPageQueryDTO } from '../model/document.model';
import { Page } from 'src/common/util/requestUtil';
import { DocumentStatus } from '../enum/documentStatus';
import {
    DocumentType,
    getDocumentType,
    isComplianceCertificate,
} from '../enum/documentType';
import {
    EventEmitterSource,
    EventsEmitterService,
} from '../../common/service/eventsEmitter.service';
import {
    createComplianceCertificateAddedEvent,
    createComplianceCertificateExpiredEvent,
    createComplianceCertificateUpdatedEvent,
    createDocumentCreatedEvent,
    createDocumentDeletedEvent,
    createDocumentUpdatedEvent,
} from '@rentancy-com/loftyworks-events';

const moment = require('moment');

@Injectable()
export class DocumentService {
    private logger = new Logger('DocumentService', { timestamp: true });

    constructor(
        private prisma: PrismaService,
        private eventsEmitter: EventsEmitterService,
    ) {}

    async getDocumentById(documentId: string): Promise<Document> {
        try {
            return await this.prisma.document.findUnique({
                where: {
                    id: documentId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async createDocument(document: Document): Promise<Document> {
        try {
            const domainEvents = [];
            const newDocument = await this.prisma.document.create({
                data: document,
            });

            if (this.isComplianceDocument(document)) {
                const complianceCertificateAddedEvent =
                    createComplianceCertificateAddedEvent({
                        propertyId: newDocument.documentPropertyId,
                        organisationId: newDocument.documentOrganisationId,
                        targetId: newDocument.id,
                        actorId: document.mutator,
                        complianceType: newDocument.type,
                        ...(newDocument.expiry && {
                            expiryDate: newDocument.expiry.toISOString(),
                        }),
                    });
                domainEvents.push(complianceCertificateAddedEvent);
            }

            domainEvents.push(
                createDocumentCreatedEvent({
                    targetId: newDocument.id,
                    actorId: newDocument.documentUserId,
                    organisationId: newDocument.documentOrganisationId,
                    status: newDocument.status,
                    ...(newDocument.expiry && {
                        expiryDate: newDocument.expiry.toString(),
                    }),
                    ...(newDocument.type && { documentType: newDocument.type }),
                    ...(newDocument.mimeType && {
                        mimeType: newDocument.mimeType,
                    }),
                }),
            );

            await this.eventsEmitter.putEvents(
                domainEvents,
                EventEmitterSource.DOCUMENTS,
            );
            return newDocument;
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db insert fails');
        }
    }

    private isComplianceDocument(document: Document): boolean {
        const documentType: DocumentType = getDocumentType(document.type);
        return isComplianceCertificate(documentType);
    }

    async updateDocument(document: Document): Promise<Document> {
        try {
            const documentUpdated = await this.prisma.document.update({
                where: {
                    id: document.id,
                },
                data: document,
            });

            this.logger.log(
                `Updating document ${document.id} of type ${document.type}...`,
            );
            const domainEvents = [];
            if (this.isComplianceDocument(documentUpdated)) {
                domainEvents.push(
                    createComplianceCertificateUpdatedEvent({
                        propertyId: documentUpdated.documentPropertyId,
                        targetId: documentUpdated.id,
                        organisationId: documentUpdated.documentOrganisationId,
                        actorId: document.mutator,
                        complianceType: documentUpdated.type,
                        ...(documentUpdated.expiry && {
                            expiryDate: documentUpdated.expiry.toISOString(),
                        }),
                    }),
                );

                if (document.status === DocumentStatus.EXPIRED) {
                    domainEvents.push(
                        createComplianceCertificateExpiredEvent({
                            propertyId: documentUpdated.documentPropertyId,
                            organisationId:
                                documentUpdated.documentOrganisationId,
                            targetId: documentUpdated.id,
                            complianceType: documentUpdated.type,
                        }),
                    );
                }
            }
            domainEvents.push(
                createDocumentUpdatedEvent({
                    targetId: documentUpdated.id,
                    actorId: documentUpdated.documentUserId,
                    organisationId: documentUpdated.documentOrganisationId,
                    status: documentUpdated.status,
                    ...(documentUpdated.expiry && {
                        expiryDate: documentUpdated.expiry.toString(),
                    }),
                    ...(documentUpdated.type && {
                        documentType: documentUpdated.type,
                    }),
                }),
            );

            await this.eventsEmitter.putEvents(
                domainEvents,
                EventEmitterSource.DOCUMENTS,
            );
            return documentUpdated;
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db update fails');
        }
    }

    async deleteDocument(
        documentId: string,
        organisationId: string,
    ): Promise<Document> {
        try {
            const document = await this.getDocumentById(documentId);

            if (!document) {
                throw new Error('Document not found');
            }

            const deletedDocument = await this.prisma.document.delete({
                where: {
                    id: documentId,
                    documentOrganisationId: organisationId,
                },
            });

            this.eventsEmitter.putEvents(
                [
                    createDocumentDeletedEvent({
                        targetId: documentId,
                        actorId: document.documentUserId,
                        organisationId: document.documentOrganisationId,
                        status: document.status,
                        ...(document.expiry && {
                            expiryDate: document.expiry.toString(),
                        }),
                        ...(document.type && { documentType: document.type }),
                    }),
                ],
                EventEmitterSource.DOCUMENTS,
            );
            return deletedDocument;
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db delete fails');
        }
    }

    async deleteAllOrganisationDocuments(organisationId: string): Promise<any> {
        try {
            const documentsToDelete =
                await this.getOrganisationDocuments(organisationId);

            const deletedDocumentsDBResponse =
                await this.prisma.document.deleteMany({
                    where: {
                        documentOrganisationId: organisationId,
                    },
                });

            documentsToDelete.forEach((document) => {
                this.eventsEmitter.putEvents([
                    createDocumentDeletedEvent({
                        targetId: document.id,
                        actorId: document.documentUserId,
                        organisationId: document.documentOrganisationId,
                        status: document.status,
                        ...(document.expiry && {
                            expiryDate: document.expiry.toString(),
                        }),
                        ...(document.type && { documentType: document.type }),
                    }),
                ]);
            }, EventEmitterSource.DOCUMENTS);
            return deletedDocumentsDBResponse;
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db delete all document fails');
        }
    }

    async getTenancyDocuments(tenancyId: string): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentTenancyId: tenancyId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getPropertyDocuments(propertyId: string): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentPropertyId: propertyId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getPropertyCompliances(
        propertyIds: string[],
        types: string[],
    ): Promise<any[]> {
        try {
            const filters: any = {};
            filters.documentPropertyId = {
                in: propertyIds,
            };
            filters.type = {
                in: types,
            };
            return await this.prisma.document.findMany({
                where: filters,
                select: {
                    documentPropertyId: true,
                    type: true,
                    expiry: true,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getOrganisationDocuments(
        organisationId: string,
        archived?: string,
        startDate?: string,
        endDate?: string,
    ): Promise<Document[]> {
        const where: any = {
            documentOrganisationId: organisationId,
        };
        if (archived) {
            where.archived = archived === 'true';
        }
        if (startDate) {
            where.createdAt = {
                gte: new Date(startDate),
            };
        }
        if (endDate) {
            where.createdAt = { ...where.createdAt, lte: new Date(endDate) };
        }

        try {
            return await this.prisma.document.findMany({
                where: where,
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getOrganisationDocumentsWithExpiry(
        organisationId: string,
        expireAt: string,
    ): Promise<Document[]> {
        const startOfDay = new Date(expireAt);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(expireAt);
        endOfDay.setHours(23, 59, 59, 999);

        try {
            return await this.prisma.document.findMany({
                where: {
                    documentOrganisationId: organisationId,
                    expiry: {
                        gte: startOfDay,
                        lte: endOfDay,
                    },
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getUserDocuments(
        organisationId: string,
        userId: string,
    ): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentOrganisationId: organisationId,
                    documentUserId: userId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getConversationDocuments(
        conversationId: string,
    ): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentConversationId: conversationId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getTaskImages(taskId: string): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    imageTaskId: taskId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getTaskDocuments(taskId: string): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentTaskId: taskId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getDocumentByPage(queryDTO: DocumentPageQueryDTO): Promise<Page> {
        try {
            const filters: any = {};
            filters.documentOrganisationId = {
                equals: queryDTO.organisationId,
            };
            if (queryDTO.name) {
                filters.name = {
                    contains: queryDTO.name,
                };
            }
            if (queryDTO.types) {
                filters.type = {
                    in: queryDTO.types,
                };
            }
            if (queryDTO.propertyIds) {
                filters.documentPropertyId = {
                    in: queryDTO.propertyIds,
                };
            }
            if (queryDTO.fromCreateDate) {
                filters.createdAt = {
                    gte: new Date(queryDTO.fromCreateDate),
                };
            }
            if (queryDTO.toCreateDate) {
                filters.createdAt = {
                    ...filters.createdAt,
                    lte: new Date(queryDTO.toCreateDate),
                };
            }
            if (queryDTO.daysRange) {
                // filters.status = DocumentStatus.EXPIRING;
                filters.expiry = {
                    not: null,
                    gte: new Date(),
                    lte: new Date(
                        new Date().getTime() +
                            queryDTO.daysRange * 24 * 60 * 60 * 1000,
                    ),
                };
            } else if (queryDTO.archived) {
                filters.archived = true;
            }
            let orderBy = {};
            if (queryDTO.orderDirection) {
                orderBy = {
                    createdAt:
                        queryDTO.orderDirection === 'ASC' ? 'asc' : 'desc',
                };
            } else {
                orderBy = {
                    expiry: {
                        sort: 'asc',
                        nulls: 'last',
                    },
                };
            }
            const [total, data] = await Promise.all([
                this.prisma.document.count({
                    where: filters,
                }),
                this.prisma.document.findMany({
                    where: filters,
                    skip: (queryDTO.page - 1) * queryDTO.size,
                    take: queryDTO.size,
                    orderBy: orderBy,
                }),
            ]);
            return new Page(total, data);
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getSupplierOrganisationDocuments(orgId: string): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentSupplierOrganisationId: orgId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getParentPropertyEntityDocuments(
        parentPropertyId: string,
    ): Promise<Document[]> {
        try {
            return await this.prisma.document.findMany({
                where: {
                    parentPropertyEntityId: parentPropertyId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async archiveDocumentByKey(orgId: string, docKey: string) {
        try {
            return await this.prisma.document.updateMany({
                where: {
                    key: docKey,
                    documentOrganisationId: orgId,
                },
                data: {
                    archived: true,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db update fails');
        }
    }

    async archiveDocumentById(orgId: string, docId: string) {
        try {
            return await this.prisma.document.updateMany({
                where: {
                    id: docId,
                    documentOrganisationId: orgId,
                },
                data: {
                    archived: true,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db update fails');
        }
    }

    async getEmailAttachmentDocument(
        emailAttachmentId: string,
    ): Promise<Document> {
        try {
            return await this.prisma.document.findFirst({
                where: {
                    documentEmailAttachmentId: emailAttachmentId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getImageDocuments(imageOrgId: string) {
        try {
            return await this.prisma.document.findMany({
                where: {
                    imageOrganisationId: imageOrgId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    async getLineItemDocuments(lineItemId: string) {
        try {
            return await this.prisma.document.findMany({
                where: {
                    documentLineItemId: lineItemId,
                },
            });
        } catch (e) {
            this.logger.error(e.stack);
            throw new Error('db select fails');
        }
    }

    assignListType(documentType: string) {
        const documentObject = {
            property: [
                'GAS_SAFETY_CERTIFICATE',
                'EICR',
                'PAT',
                'EPC',
                'INSPECTION',
                'METER_READING',
                'PHOTO',
                'LEGIONELLA',
                'CHIMNEY_SWEEP',
            ],
            contract: [
                'CONTRACT',
                'TENANT_RIGHTS',
                'CHECK_IN',
                'CHECK_OUT',
                'INSURANCE',
                'INSTRUCTIONS',
                'GUIDE',
                'MISCELLANEOUS',
                'INVOICE',
                'STATEMENT',
            ],
            clients: [
                'MANAGEMENT_CONTRACT',
                'PROOF_OF_OWNERSHIP',
                'TAX_RELATED',
                'CLIENTS_STATEMENT',
                'CLIENTS_GUIDE',
                'TS_AND_CS',
            ],
            tenants: [
                'ID',
                'REFERENCE',
                'RIGHT_TO_RENT',
                'TENANTS_INSURANCE',
                'TENANTS_GUIDE',
                'APPLICATION_FOR_TENANCY',
                'PROOF_OF_ADDRESS',
                'EMPLOYMENT_CONFIRMATION',
                'STUDIES_CONFIRMATION',
                'LANDLORD_REFERENCE',
                'CHARACTER_REFERENCE',
            ],
            marketing: [
                'PROPERTY_DETAILS',
                'FLOOR_PLAN',
                'BROCHURE',
                'MARKETING_PHOTO',
            ],
            supplier: [
                'SUPPLIER_CONTRACT',
                'SUPPLIER_INSURANCE',
                'METHOD_STATEMENT',
                'WORK_ORDER',
                'ESTIMATE',
                'QUOTE',
                'SUPPLIER_INVOICE',
            ],
            other: [
                'FIRE_RISK_ASSESSMENT',
                'HEALTH_AND_SAFETY_REPORT',
                'ASBESTOS_REPORT',
                'WARRANTY_DOCUMENT',
                'DEED_OF_ASSIGNMENT',
                'LICENSE_ALTERATION',
                'RENT_DEED_DEPOSIT',
                'LICENSE_TO_ASSIGN',
                'OTHER',
            ],
        };

        return Object.keys(documentObject).find((key) =>
            documentObject[key].includes(documentType),
        );
    }

    resolveStatus(document: Document, reminder) {
        const documentExpiryDays = this.getDocumentExpiryDays(reminder);
        let documentStatus = 'EXPIRING';

        if (documentExpiryDays && document.expiry) {
            if (
                moment()
                    .utc()
                    .add(documentExpiryDays, 'days')
                    .isBefore(moment(document.expiry).utc())
            ) {
                documentStatus = 'VALID';
            }

            if (moment().utc().isAfter(moment(document.expiry).utc())) {
                documentStatus = 'EXPIRED';
            }
        } else {
            documentStatus = 'UNCERTAIN';
        }

        if (document.type === undefined) {
            document.type = 'MISCELLANEOUS';
        }

        if (document.status === 'FAILED') {
            documentStatus = 'FAILED';
        }

        return documentStatus;
    }

    private getDocumentExpiryDays(reminder) {
        if (
            reminder &&
            reminder.notifyDocumentExpiry &&
            reminder.documentExpiryNotificationDays &&
            reminder.documentExpiryNotificationDays.length > 0
        ) {
            return reminder.documentExpiryNotificationDays[0];
        }

        return undefined;
    }
}
