import { Permission } from "@prisma/client";
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from "src/common/service/prisma.service";

@Injectable()
export class PermissionService {
    private logger = new Logger('PermissionService', { timestamp: true });
    constructor(private prisma: PrismaService) {
    }

    async createPermission(permission: Permission): Promise<boolean> {
        try {
            return await this.prisma.permission.create({
                data: permission
            }) !== null;
        } catch (e) {
            this.logger.error(e)
            throw new Error("db insert fails");
        }
    }

    async getPermissionById(permissionId: string): Promise<Permission> {
        try {
            return await this.prisma.permission.findUnique({
                where: {
                    id: permissionId
                }
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db select fails");
        }
    }

    async deletePermission(permissionId: string): Promise<void> {
        try {
            await this.prisma.permission.delete({
                where: {
                    id: permissionId
                }
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db delete fails");
        }
    }

    async updatePermission(permission: Permission): Promise<Permission> {
        try {
            return await this.prisma.permission.update({
                where: {
                    id: permission.id
                },
                data: permission
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db update fails");
        }
    }

    async getPermissionsByIds(permissionIds: string[]): Promise<Permission[]> {
        try {
            return await this.prisma.permission.findMany({
                where: {
                    id: {
                        in: permissionIds
                    }
                }
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db select fails");
        }
    }
}