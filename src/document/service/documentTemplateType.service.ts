import { Injectable, Logger } from '@nestjs/common';
import { DocumentTemplateType } from "@prisma/client";
import { PrismaService } from 'src/common/service/prisma.service';
import { v1 as uuidV1 } from 'uuid';


@Injectable()
export class DocumentTemplateTypeService {
    private logger = new Logger('DocumentTemplateTypeService', { timestamp: true });
    constructor(private prisma: PrismaService) {
    }

    async getDocumentTemplateTypes(organisationId: string): Promise<DocumentTemplateType[] | null> {
        try {
            return await this.prisma.documentTemplateType.findMany({
                where: {
                    organisationId: organisationId
                },
                orderBy: {
                    index: 'asc'
                }
            })
        } catch (e) {
            this.logger.error(e)
            throw new Error("db query fails");
        }
    }

    async createDocumentTemplateType(docTemplateType: DocumentTemplateType): Promise<DocumentTemplateType> {
        docTemplateType.id = uuidV1();
        try {
            return await this.prisma.documentTemplateType.create({
                data: docTemplateType
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db insert fails");
        }
    }

    async getDocumentTemplateTypeById(orgId: string, id: string): Promise<DocumentTemplateType> {
        try {
            return await this.prisma.documentTemplateType.findUnique({
                where: {
                    id: id,
                    organisationId: orgId
                }
            });
        } catch (e) {
            this.logger.error(e)
            throw new Error("db query fails");
        }
    }

    async deleteOrgDocumentTemplateType(orgId: string, type: DocumentTemplateType): Promise<boolean> {
        try {
            await this.prisma.documentTemplateType.updateMany({
                where: {
                    organisationId: orgId,
                    index: {
                        gt: type.index
                    }
                },
                data: {
                    index: {
                        decrement: 1
                    }
                }
            })
            const del = await this.prisma.documentTemplateType.delete({
                where: {
                    id: type.id,
                    organisationId: orgId
                }
            });
            return !!del;
        } catch (e) {
            this.logger.error(e)
            throw new Error("db delete fails");
        }
    }

    async deleteOrgAllDocumentTemplateType(orgId: string): Promise<void> {
        try {
            await this.prisma.documentTemplateType.deleteMany({
                where: {
                    organisationId: orgId,
                }
            })
        } catch (e) {
            this.logger.error(e)
            throw new Error("db delete fails");
        }
    }

    async updateTypeOrder(orgId: string, typeId: string, newIndex: number): Promise<boolean> {
        try {
            const oldType = await this.getDocumentTemplateTypeById(orgId, typeId);
            if(!oldType) {
                throw new Error("Document Template Type not found");
            }
            if(oldType.index === newIndex) {
                return true;
            }
            const result = await this.prisma.documentTemplateType.update({
                where: {
                    id: typeId
                },
                data: {
                    index: newIndex
                }
            });
            const indexWhere: any = {};
            const indexData: any = {};
            if(oldType.index < newIndex) {
                indexWhere.gte = oldType.index;
                indexWhere.lte = newIndex;
                indexData.decrement = 1;
            } else {
                indexWhere.gte = newIndex;
                indexWhere.lte = oldType.index;
                indexData.increment = 1;
            }
            await this.prisma.documentTemplateType.updateMany({
                where:{
                    id: {
                        not: typeId
                    },
                    index: indexWhere
                },
                data: {
                    index: indexData
                }
            })
            return !!result;
        } catch (e) {
            this.logger.error(e)
            throw new Error("db update fails");
        }
    }
}

