import { Injectable, Logger } from '@nestjs/common';
import { TaskStatus } from '../../common/enum/task';
import { BoardRepository } from '../../common/repository/board';
import { TaskLabelRepository } from '../../common/repository/taskLabel';
import { ColumnRepository } from '../../common/repository/column';
import { TaskRepository } from '../../common/repository/task';
import { UserRepository } from '../../common/repository/user';
import { findBoardTasks } from '../../common/service/ddb.service';
import { ListTaskVO, TaskSearchDTO, TaskVO } from '../model/task.model';
import { getTableName } from '../../common/constant/dynamodb.constants';
import { GenericService } from '../../common/service/generic.service';
import { DynamodbService } from '../../common/service/dynamodb.service';
import { DocumentService } from '../../document/service/document.service';
import { UserService } from '../../user/service/user.service';
import { BoardService } from '../../board/service/board.service';

@Injectable()
export class TaskService {
    private logger = new Logger('TaskService', { timestamp: true });

    constructor(
        private readonly boardRepository: BoardRepository,
        private readonly taskLabelRepository: TaskLabelRepository,
        private readonly columnRepository: ColumnRepository,
        private readonly taskRepository: TaskRepository,
        private readonly userRepository: UserRepository,
        private readonly genericService: GenericService,
        private readonly dynamodbService: DynamodbService,
        private readonly documentService: DocumentService,
        private readonly userService: UserService,
        private readonly boardService: BoardService,
    ) {}

    async getTasksForDashboard(organisationId: string, cognitoUserId: string) {
        try {
            const boards = await this.listBoards(organisationId, cognitoUserId);
            const taskBoardId: string | null = boards[0] ? boards[0].id : null;

            const [tasks, columns] = await Promise.all([
                this.taskRepository.findByBoardId(taskBoardId),
                this.columnRepository.findByBoardId(taskBoardId),
            ]);

            const doneColumnId: string | undefined = columns.find(
                (column) =>
                    column.name &&
                    column.name.toUpperCase() === TaskStatus.DONE,
            )?.id;

            const todoColumnId: string | undefined = columns.find(
                (column) =>
                    column.name &&
                    column.name.toUpperCase() === TaskStatus.TODO,
            )?.id;

            const inProgressColumnId: string | undefined = columns.find(
                (column) =>
                    column.name &&
                    column.name.toUpperCase() === TaskStatus.IN_PROGRESS,
            )?.id;

            const filteredTasks = tasks.filter(
                (task) => task.taskColumnId !== doneColumnId,
            );

            return await Promise.all(
                filteredTasks.map(async (task) => {
                    const updatedTask = { ...task };
                    if (task.taskColumnId === todoColumnId) {
                        updatedTask.status = TaskStatus.TODO;
                    } else if (task.taskColumnId === inProgressColumnId) {
                        updatedTask.status = TaskStatus.IN_PROGRESS;
                    } else {
                        updatedTask.status = undefined;
                    }

                    const taskLabel = task.taskLabelId
                        ? await this.taskLabelRepository.findById(
                              task.taskLabelId,
                          )
                        : null;

                    return {
                        title: updatedTask.name || '',
                        sub: taskLabel ? taskLabel.name || '' : '',
                        status: updatedTask.status,
                        taskId: task.id,
                        boardId: task.taskBoardId,
                    };
                }),
            );
        } catch (e) {
            this.logger.error(`Error getting tasks for dashboard: ${e}`);
            throw new Error('Getting tasks fails');
        }
    }

    async listBoards(organisationId: string, cognitoUserId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoUserId);
        const userOrganisation: string = user.currentOrganisation;
        this.checkOrganisation(userOrganisation, organisationId);

        return (
            await this.boardRepository.findByOrganisationId(organisationId)
        ).map((board) => {
            return {
                id: board.id,
                name: board.name,
            };
        });
    }

    async searchTasks({
        boardId,
        taskUserId,
        columnId,
        labelId,
        parentId,
    }: TaskSearchDTO) {
        const tasks = await findBoardTasks(
            boardId,
            null,
            taskUserId,
            columnId,
            labelId,
            parentId,
        );
        return tasks.map((task) => {
            return {
                id: task.id,
                taskBoardId: task.taskBoardId,
                name: task.name,
                parentType: task.parentType,
                description: task.description,
            };
        });
    }

    private checkOrganisation(
        userOrganisation: string,
        organisationId: string,
    ): void {
        if (userOrganisation !== organisationId) {
            throw new Error(
                'User does not belong to the specified organisation',
            );
        }
    }

    /*
parentPropertyEntity → @function(name: "boardService-${env}")                           【boardService#parentPropertyEntity】
property → @function(name: "boardService-${env}")                                       【boardService#property】
    primaryLandlord → @function(name: "usersService-${env}")                            【usersService#primaryLandlord】
user → @connection(name: "UserTasks")                                               【taskUserId】
    image → @connection(name: "UserProfile")                                        【User userImageId】
label → @connection(name: "TaskLabels")                                             【taskLabelId】
checklists → @connection(name: "taskChecklists")                                    【TaskChecklist taskChecklistTaskId】
    organisationTaskChecklist → @connection(name: "organisationTaskChecklists")     【TaskChecklist taskChecklistOrganisationTaskChecklistId】
comments → @connection(name: "taskComment")                                         【TaskComment taskCommentTaskId】
    user → @connection(name: "UserTaskComments")                                    【TaskComment taskCommentUserId】
        image → @connection(name: "UserProfile")                                    【User userImageId】
images → @function(name: "attachmentsService-${env}")                                   【attachmentsService#images】
worksOrders → @connection(name: "worksOrdersTask")                                  【WorksOrder worksOrderTaskId】
    supplierContact → @connection(name: "workOrderUser")                            【WorksOrder worksOrderSupplierContactId】
        postalAddress → @function(name: "userService-${env}")                           【userService#postalAddress】
    reportedBy → @connection(name: "workOrderReporterUser")                         【WorksOrder worksOrderReportedById】
    */
    async getTaskById(info: any) {
        const task = await this.dynamodbService.getItem(
            getTableName('Task'),
            info.id,
        );
        this.logger.log(
            `getTaskById:info=${JSON.stringify(info)},task=${JSON.stringify(task)}`,
        );
        const taskDto = {};
        info.fieldNames
            .filter((field: any) => typeof field === 'string')
            .forEach((field: any) => (taskDto[field] = task[field]));
        //await this.getTaskParentPropertyEntity(info, task, taskDto);
        //await this.getTaskProperty(info, task, taskDto);
        await this.getTaskUser(info, task, taskDto);
        await this.getTaskLabel(info, task, taskDto);
        await this.getTaskChecklists(info, task, taskDto);
        await this.getTaskComments(info, task, taskDto);
        await this.getTaskImages(info, task, taskDto);
        await this.getTaskWorkOrders(info, task, taskDto);
        this.logger.log(
            `getTaskById:info=${JSON.stringify(info)},task=${JSON.stringify(task)},taskDto=${JSON.stringify(taskDto)}`,
        );
        return taskDto;
    }

    private async getTaskParentPropertyEntity(
        info: any,
        task: any,
        taskDto: any,
    ) {
        if (
            info['parentPropertyEntity'] &&
            typeof info['parentPropertyEntity']['fieldNames'] === 'object' &&
            task.parentId &&
            task.parentType
        ) {
            const parentPropertyEntity = {}; //await this.boardService.parentPropertyEntity(task.parentId, task.parentType);
            const parentPropertyEntityDto = {};
            info['parentPropertyEntity']['fieldNames']
                .filter((field: any) => typeof field === 'string')
                .forEach(
                    (field: any) =>
                        (parentPropertyEntityDto[field] =
                            parentPropertyEntity[field]),
                );
            taskDto['parentPropertyEntity'] = parentPropertyEntityDto;
        }
    }

    private async getTaskProperty(info: any, task: any, taskDto: any) {
        if (
            info['property'] &&
            typeof info['property']['fieldNames'] === 'object' &&
            task.parentId &&
            task.parentType
        ) {
            const property = { primaryLandlordId: '' }; //await this.boardService.property(task.parentId, task.parentType);
            const propertyDto = {};
            info['property']['fieldNames']
                .filter((field: any) => typeof field === 'string')
                .forEach(
                    (field: any) => (propertyDto[field] = property[field]),
                );
            if (
                info['property']['primaryLandlord'] &&
                typeof info['property']['primaryLandlord']['fieldNames'] ===
                    'object' &&
                property.primaryLandlordId
            ) {
                const primaryLandlord = await this.genericService.getItem(
                    {
                        id: property.primaryLandlordId,
                        fieldNames:
                            info['property']['primaryLandlord']['fieldNames'],
                    },
                    'User',
                );
                const primaryLandlordDto = {};
                info['property']['primaryLandlord']['fieldNames']
                    .filter((field: any) => typeof field === 'string')
                    .forEach(
                        (field: any) =>
                            (primaryLandlordDto[field] =
                                primaryLandlord[field]),
                    );
                propertyDto['primaryLandlord'] = primaryLandlordDto;
            }
            taskDto['property'] = propertyDto;
        }
    }

    private async getTaskUser(info: any, task: any, taskDto: any) {
        if (
            info['user'] &&
            typeof info['user']['fieldNames'] === 'object' &&
            task.taskUserId
        ) {
            const userImageFlag =
                info['user']['image'] &&
                typeof info['user']['image']['fieldNames'] === 'object';
            if (userImageFlag) {
                info['user']['fieldNames'].push('userImageId');
            }
            const user = await this.genericService.getItem(
                { id: task.taskUserId, fieldNames: info['user']['fieldNames'] },
                'User',
            );
            const userDto = {};
            info['user']['fieldNames']
                .filter((field: any) => typeof field === 'string')
                .forEach((field: any) => (userDto[field] = user[field]));
            if (userImageFlag && user.userImageId) {
                const userImage = await this.genericService.getItem(
                    {
                        id: user.userImageId,
                        fieldNames: info['user']['image']['fieldNames'],
                    },
                    'File',
                );
                const userImageDto = {};
                info['user']['image']['fieldNames']
                    .filter((field: any) => typeof field === 'string')
                    .forEach(
                        (field: any) =>
                            (userImageDto[field] = userImage[field]),
                    );
                userDto['image'] = userImageDto;
            }
            taskDto['user'] = userDto;
        }
    }

    private async getTaskLabel(info: any, task: any, taskDto: any) {
        if (
            info['label'] &&
            typeof info['label']['fieldNames'] === 'object' &&
            task.taskLabelId
        ) {
            const taskLabel = await this.genericService.getItem(
                {
                    id: task.taskLabelId,
                    fieldNames: info['label']['fieldNames'],
                },
                'TaskLabel',
            );
            const taskLabelDto = {};
            info['label']['fieldNames']
                .filter((field: any) => typeof field === 'string')
                .forEach(
                    (field: any) => (taskLabelDto[field] = taskLabel[field]),
                );
            taskDto['label'] = taskLabelDto;
        }
    }

    private async getTaskChecklists(info: any, task: any, taskDto: any) {
        if (
            info['checklists'] &&
            typeof info['checklists']['fieldNames'] === 'object'
        ) {
            const taskChecklists = await this.dynamodbService.scan(
                getTableName('TaskChecklist'),
                'taskChecklistTaskId = :taskChecklistTaskId',
                { ':taskChecklistTaskId': task.id },
            );
            const taskChecklistsDto = [];
            for (const taskChecklist of taskChecklists) {
                const taskChecklistDto = {};
                info['checklists']['fieldNames']
                    .filter((field: any) => typeof field === 'string')
                    .forEach(
                        (field: any) =>
                            (taskChecklistDto[field] = taskChecklist[field]),
                    );
                if (
                    info['checklists']['organisationTaskChecklist'] &&
                    typeof info['checklists']['organisationTaskChecklist'][
                        'fieldNames'
                    ] === 'object' &&
                    taskChecklist.taskChecklistOrganisationTaskChecklistId
                ) {
                    const organisationTaskChecklist =
                        await this.genericService.getItem(
                            {
                                id: taskChecklist.taskChecklistOrganisationTaskChecklistId,
                                fieldNames:
                                    info['checklists'][
                                        'organisationTaskChecklist'
                                    ]['fieldNames'],
                            },
                            'OrganisationTaskChecklist',
                        );
                    const organisationTaskChecklistDto = {};
                    info['checklists']['organisationTaskChecklist'][
                        'fieldNames'
                    ]
                        .filter((field: any) => typeof field === 'string')
                        .forEach(
                            (field: any) =>
                                (organisationTaskChecklistDto[field] =
                                    organisationTaskChecklist[field]),
                        );
                    taskChecklistDto['organisationTaskChecklist'] =
                        organisationTaskChecklistDto;
                }
                taskChecklistsDto.push(taskChecklistDto);
            }
            taskDto['checklists'] = taskChecklistsDto;
        }
    }

    private async getTaskComments(info: any, task: any, taskDto: any) {
        if (
            info['comments'] &&
            typeof info['comments']['fieldNames'] === 'object'
        ) {
            const taskComments = await this.dynamodbService.scan(
                getTableName('TaskComment'),
                'taskCommentTaskId = :taskCommentTaskId',
                { ':taskCommentTaskId': task.id },
            );
            const taskCommentsDto = [];
            for (const taskComment of taskComments) {
                const taskCommentDto = {};
                info['comments']['fieldNames']
                    .filter((field: any) => typeof field === 'string')
                    .forEach(
                        (field: any) =>
                            (taskCommentDto[field] = taskComment[field]),
                    );
                if (
                    info['comments']['user'] &&
                    typeof info['comments']['user']['fieldNames'] ===
                        'object' &&
                    taskComment.taskCommentUserId
                ) {
                    const userImageFlag =
                        info['comments']['user']['image'] &&
                        typeof info['comments']['user']['image'][
                            'fieldNames'
                        ] === 'object';
                    if (
                        userImageFlag &&
                        !info['comments']['user']['fieldNames'].includes(
                            'userImageId',
                        )
                    ) {
                        info['comments']['user']['fieldNames'].push(
                            'userImageId',
                        );
                    }
                    const user = await this.genericService.getItem(
                        {
                            id: taskComment.taskCommentUserId,
                            fieldNames: info['comments']['user']['fieldNames'],
                        },
                        'User',
                    );
                    const userDto = {};
                    info['comments']['user']['fieldNames']
                        .filter((field: any) => typeof field === 'string')
                        .forEach(
                            (field: any) => (userDto[field] = user[field]),
                        );
                    if (userImageFlag && user.userImageId) {
                        const userImage = await this.genericService.getItem(
                            {
                                id: user.userImageId,
                                fieldNames:
                                    info['comments']['user']['image'][
                                        'fieldNames'
                                    ],
                            },
                            'File',
                        );
                        const userImageDto = {};
                        info['comments']['user']['image']['fieldNames']
                            .filter((field: any) => typeof field === 'string')
                            .forEach(
                                (field: any) =>
                                    (userImageDto[field] = userImage[field]),
                            );
                        userDto['image'] = userImageDto;
                    }
                    taskCommentDto['user'] = userDto;
                }
                taskCommentsDto.push(taskCommentDto);
            }
            taskDto['comments'] = taskCommentsDto;
        }
    }

    private async getTaskImages(info: any, task: any, taskDto: any) {
        if (
            info['images'] &&
            typeof info['images']['fieldNames'] === 'object'
        ) {
            const taskImages = await this.documentService.getTaskImages(
                task.id,
            );
            const taskImagesDto = [];
            for (const taskImage of taskImages) {
                const taskImageDto = {};
                info['images']['fieldNames']
                    .filter((field: any) => typeof field === 'string')
                    .forEach(
                        (field: any) =>
                            (taskImageDto[field] = taskImage[field]),
                    );
                taskImagesDto.push(taskImageDto);
            }
            taskDto['images'] = taskImagesDto;
        }
    }

    private async getTaskWorkOrders(info: any, task: any, taskDto: any) {
        if (
            info['worksOrders'] &&
            typeof info['worksOrders']['fieldNames'] === 'object'
        ) {
            const worksOrders = await this.dynamodbService.scan(
                getTableName('WorksOrder'),
                'worksOrderTaskId = :worksOrderTaskId',
                { ':worksOrderTaskId': task.id },
            );
            const worksOrdersDto = [];
            for (const worksOrder of worksOrders) {
                const worksOrderDto = {};
                info['worksOrders']['fieldNames']
                    .filter((field: any) => typeof field === 'string')
                    .forEach(
                        (field: any) =>
                            (worksOrderDto[field] = worksOrder[field]),
                    );
                if (
                    info['worksOrders']['supplierContact']['fieldNames'] &&
                    typeof info['worksOrders']['supplierContact'][
                        'fieldNames'
                    ] === 'object' &&
                    worksOrder.worksOrderSupplierContactId
                ) {
                    const supplierContact = await this.genericService.getItem(
                        {
                            id: worksOrder.worksOrderSupplierContactId,
                            fieldNames:
                                info['worksOrders']['supplierContact'][
                                    'fieldNames'
                                ],
                        },
                        'User',
                    );
                    const supplierContactDto = {};
                    info['worksOrders']['supplierContact']['fieldNames']
                        .filter((field: any) => typeof field === 'string')
                        .forEach(
                            (field: any) =>
                                (supplierContactDto[field] =
                                    supplierContact[field]),
                        );
                    if (
                        info['worksOrders']['supplierContact'][
                            'postalAddress'
                        ] &&
                        typeof info['worksOrders']['supplierContact'][
                            'postalAddress'
                        ]['fieldNames'] === 'object'
                    ) {
                        const postalAddress =
                            await this.userService.postalAddress(
                                supplierContact.id,
                            );
                        const postalAddressDto = {};
                        info['worksOrders']['supplierContact']['postalAddress'][
                            'fieldNames'
                        ]
                            .filter((field: any) => typeof field === 'string')
                            .forEach(
                                (field: any) =>
                                    (postalAddressDto[field] =
                                        postalAddress[field]),
                            );
                        supplierContactDto['postalAddress'] = postalAddressDto;
                    }
                    worksOrderDto['supplierContact'] = supplierContactDto;
                }
                if (
                    info['worksOrders']['reportedBy'] &&
                    typeof info['worksOrders']['reportedBy']['fieldNames'] ===
                        'object' &&
                    worksOrder.worksOrderReportedById
                ) {
                    const reportedBy = await this.genericService.getItem(
                        {
                            id: worksOrder.worksOrderReportedById,
                            fieldNames:
                                info['worksOrders']['reportedBy']['fieldNames'],
                        },
                        'User',
                    );
                    const reportedByDto = {};
                    info['worksOrders']['reportedBy']['fieldNames']
                        .filter((field: any) => typeof field === 'string')
                        .forEach(
                            (field: any) =>
                                (reportedByDto[field] = reportedBy[field]),
                        );
                    worksOrderDto['reportedBy'] = reportedByDto;
                }
                worksOrdersDto.push(worksOrderDto);
            }
            taskDto['worksOrders'] = worksOrdersDto;
        }
    }

    async listTasks(
        organisationId: string,
        nextToken: string,
        limit: number = 20,
    ): Promise<ListTaskVO> {
        try {
            const pageTaskDto =
                await this.taskRepository.pageTaskByOrganisationId(
                    organisationId,
                    nextToken,
                    limit,
                );
            const taskVOs = pageTaskDto.items.map((task) => {
                return {
                    id: task.id,
                    name: task.name,
                    taskBoardId: task.taskBoardId,
                    status: task.status,
                    taskOrganisationId: task.taskOrganisationId,
                } as TaskVO;
            });
            return {
                nextToken: pageTaskDto.nextToken,
                size: taskVOs.length,
                items: taskVOs,
            };
        } catch (error) {
            console.error('DynamoDB query error:', error);
            throw new Error('Failed to list tasks');
        }
    }
}
