import { Modu<PERSON> } from '@nestjs/common';
import { TaskController } from './controller/api/task.controller';
import { TaskService } from './service/task.service';
import { GenericService } from '../common/service/generic.service';
import { DynamodbService } from '../common/service/dynamodb.service';
import { DocumentService } from '../document/service/document.service';
import { UserService } from '../user/service/user.service';
import { BoardService } from '../board/service/board.service';
import { BoardRepository } from '../common/repository/board';
import { TaskLabelRepository } from '../common/repository/taskLabel';
import { ColumnRepository } from '../common/repository/column';
import { TaskRepository } from '../common/repository/task';

@Module({
    providers: [
        DocumentService,
        DynamodbService,
        GenericService,
        UserService,
        BoardService,
        TaskService,
        BoardRepository,
        TaskLabelRepository,
        ColumnRepository,
        TaskRepository,
    ],
    exports: [TaskService],
    controllers: [TaskController],
})
export class TaskModule {}
