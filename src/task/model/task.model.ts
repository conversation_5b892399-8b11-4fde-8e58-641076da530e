import { ApiProperty } from '@nestjs/swagger';

export class TaskSearchDTO {
    @ApiProperty()
    boardId: string;

    @ApiProperty()
    taskUserId: string;

    @ApiProperty()
    columnId: string;

    @ApiProperty()
    labelId: string;

    @ApiProperty()
    parentId: string;
}

export class ListTaskVO {
    @ApiProperty()
    nextToken?: string;
    @ApiProperty()
    size: number;
    @ApiProperty({ type: () => [TaskVO] })
    items: TaskVO[];
}

export class TaskVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    taskBoardId: string;
    @ApiProperty()
    status: string;
    @ApiProperty()
    taskOrganisationId: string;
}
