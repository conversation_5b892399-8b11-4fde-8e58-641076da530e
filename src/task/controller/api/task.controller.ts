import {
    Body,
    Controller,
    Get,
    Logger,
    Post,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiOkResponse,
    ApiOperation,
    ApiQuery,
    getSchemaPath,
} from '@nestjs/swagger';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { Response } from 'express';
import { ResponseCode } from '../../../common/constant/responseCode';
import { TaskService } from '../../service/task.service';
import { ListTaskVO } from '../../model/task.model';

@Controller('api/v1/task')
@ApiBearerAuth()
export class TaskController {
    private readonly logger = new Logger('TaskController', { timestamp: true });

    constructor(private readonly taskService: TaskService) {}

    /**
{
    "id": "f87d5630-0ad9-11f0-96f4-1929391709dd",
    "fieldNames": ["id","name"],
    "label": {
        "fieldNames": ["id"]
    },
    "user": {
        "fieldNames": ["id"],
        "image": {
            "fieldNames": ["id"]
        }
    },
    "worksOrders": {
        "fieldNames": ["id"],
        "supplierContact": {
            "fieldNames": ["id"],
            "postalAddress": {
                "fieldNames": ["id"]
            }
        }
    }
}
    */
    @ApiOperation({ summary: 'Get task by id' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'object',
                        },
                    },
                },
            ],
        },
    })
    @Post('getTaskById')
    public async getTaskById(@Body() info: any, @Res() res: Response) {
        if (!info || !info.id || !info.fieldNames) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            const task = await this.taskService.getTaskById(info);
            responseOk(res, task);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'List tasks' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(ListTaskVO) },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'nextToken',
        required: false,
    })
    @ApiQuery({
        name: 'limit',
        required: false,
    })
    @Get('/list')
    public async listTasks(
        @Query('nextToken') nextToken: string,
        @Query('limit') limit: number,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const task = await this.taskService.listTasks(
                req.user['custom:organisationId'],
                nextToken,
                limit,
            );
            responseOk(res, task);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
