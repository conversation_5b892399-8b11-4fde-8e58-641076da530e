import { <PERSON>, <PERSON>, Lo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { CategoryDocumentService } from '../../service/categoryDocument.service';
import { CategoryDocument } from '../../../common/model/categoryDocument';

@Controller('api/v1/category-document')
@ApiExtraModels(BaseRes, CategoryDocument)
@ApiBearerAuth()
export class CategoryDocumentController {
    private readonly logger = new Logger('CategoryDocumentController', {
        timestamp: true,
    });

    constructor(
        private readonly categoryDocumentService: CategoryDocumentService,
    ) {}

    @ApiOperation({ summary: 'Get CategoryDocument' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(CategoryDocument),
                        },
                    },
                },
            ],
        },
    })
    @Get('/:categoryId')
    public async getCategoryDocument(
        @Param('categoryId') categoryId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.categoryDocumentService.getCategoryDocument(
                    categoryId,
                    req.user['custom:organisationId'],
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
