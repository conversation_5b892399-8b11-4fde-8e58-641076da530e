import { Injectable, Logger } from '@nestjs/common';
import { CategoryDocumentRepository } from '../../common/repository/categoryDocument';
import { CategoryDocument } from '../../common/model/categoryDocument';

@Injectable()
export class CategoryDocumentService {
    private readonly logger = new Logger('CategoryDocumentService', {
        timestamp: true,
    });

    constructor(
        private readonly categoryDocumentRepository: CategoryDocumentRepository,
    ) {}

    async getCategoryDocument(
        categoryId: string,
        organisationId: string,
    ): Promise<CategoryDocument> {
        return this.categoryDocumentRepository.getCategoryDocument(
            organisationId,
            categoryId,
        );
    }
}
