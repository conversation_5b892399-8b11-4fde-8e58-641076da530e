import { Injectable, Logger } from '@nestjs/common';
import { getUser, getUserByCognitoId } from '../../common/service/ddb.service';
import { ConversationQueryDTO } from '../model/conversation.model';
import { search as osSearch } from '../../common/service/opensearch.service';

@Injectable()
export class CommunicationService {
    private logger = new Logger('CommunicationService', { timestamp: true });

    async queryConversationsDashboard(
        input: ConversationQueryDTO,
        cognitoId: string,
    ) {
        if (!this.validConversationInput(input, cognitoId)) {
            return [];
        }

        const user = await getUserByCognitoId(cognitoId);
        if (!user) {
            this.logger.error(`User of cognitoId ${cognitoId} not found !!`);
            return;
        }

        const userId: string = user.id;
        const { sortType } = input;

        const { hits } = await this.searchConversations(
            {
                ...input,
                userId,
            },
            true,
        );

        const conversations = hits.map((x: any) => {
            x.lastMessageTime = x.lastMessageTime
                ? x.lastMessageTime
                : x.createdAt;
            return {
                conversation: x,
            };
        });

        await Promise.all(
            conversations.map(async (x: any) => {
                let userIds: string[] = x.conversation.members;
                userIds = userIds.filter((userId) => userId !== user.id);
                userIds = userIds.slice(0, 1);
                const memberUsers = await Promise.all(
                    userIds
                        .filter((userId) => userId !== user.id)
                        .map(
                            async (userId: string) =>
                                await getUser(
                                    userId,
                                    'id, fname, sname, #userType, emails, phones',
                                    { '#userType': 'type' },
                                ),
                        ),
                );
                if(x.conversation.conversationType === 'EMAIL'){
                    x.conversation.sender = x.conversation.name;
                }else{
                    x.conversation.sender = memberUsers
                    .filter((user) => user && user.fname && user.sname)
                    .map((user) => `${user.fname} ${user.sname}`)
                    .join(', ');
                }
            }),
        );

        this.sortConversations(conversations, sortType);

        return conversations
            .filter(
                (item) =>
                    item.conversation.lastMessage &&
                    item.conversation.lastMessage.trim() !== '',
            )
            .map((item) => {
                const conversation = item.conversation;
                return {
                    lastMessage: conversation.lastMessage,
                    lastMessageTime: conversation.lastMessageTime,
                    conversationId: conversation.id,
                    sender: conversation.sender,
                    type: conversation.conversationType,
                };
            });
    }

    private validConversationInput(
        input: ConversationQueryDTO,
        cognitoId: string,
    ): boolean {
        if (!input) {
            this.logger.log(`queryConversations:input is empty!`);
            return false;
        }

        if (!cognitoId) {
            this.logger.error(`queryConversations no cognitoId in session!`);
            return false;
        }
        return true;
    }

    private sortConversations(conversations: any[], sortType: string): void {
        conversations.sort((a, b) => {
            if (
                a.conversation.lastMessageTime &&
                b.conversation.lastMessageTime
            ) {
                if (sortType !== 'Oldest') {
                    return b.conversation.lastMessageTime.localeCompare(
                        a.conversation.lastMessageTime,
                    );
                } else {
                    return a.conversation.lastMessageTime.localeCompare(
                        b.conversation.lastMessageTime,
                    );
                }
            } else {
                return a.conversation.lastMessageTime ? -1 : 1;
            }
        });
    }

    private async searchConversations(
        input: any,
        forDashboard: boolean = false,
    ): Promise<{ hits: any[] }> {
        const {
            organisationId,
            userId,
            fullMatchUserIdFlag,
            startDate,
            endDate,
            unread,
            tag,
            conversationTypes,
            conversationCategory,
            memberIds,
            randomMemberIds,
            parentPropertyId,
            propertyId,
            mainEmailAddresses,
            sortType,
        } = input;

        try {
            const must: any[] = [];
            must.push(
                this.getQueryItem(
                    'term',
                    'conversationOrganisationId.keyword',
                    organisationId,
                ),
            );

            if (userId) {
                const boolMustNot: any = {
                    bool: {
                        must_not: [
                            { terms: { 'deletedMembers.keyword': [userId] } },
                        ],
                    },
                };

                const mustNot = boolMustNot.bool.must_not;
                must.push(boolMustNot);

                if (unread) {
                    mustNot.push(
                        this.getQueryItem('terms', 'readMembers.keyword', [
                            userId,
                        ]),
                    );
                }

                const boolShould: any = {
                    bool: {
                        should: [{ terms: { 'members.keyword': [userId] } }],
                    },
                };

                const should = boolShould.bool.should;
                if (!fullMatchUserIdFlag) {
                    should.push({
                        term: { 'conversationCategory.keyword': 'SHARED' },
                    });
                    should.push({
                        bool: {
                            must: [
                                {
                                    term: {
                                        'conversationCategory.keyword':
                                            'ARCHIVED',
                                    },
                                },
                                {
                                    term: {
                                        'oldConversationCategory.keyword':
                                            'SHARED',
                                    },
                                },
                            ],
                        },
                    });
                }
                must.push(boolShould);
            }

            if (memberIds && memberIds.length < 10) {
                memberIds.forEach((x) =>
                    must.push(
                        this.getQueryItem('terms', 'members.keyword', [x]),
                    ),
                );
            }

            if (randomMemberIds && randomMemberIds.length > 0) {
                const boolShould: any = {
                    bool: {
                        should: [],
                    },
                };
                randomMemberIds.forEach((x) =>
                    boolShould.bool.should.push(
                        this.getQueryItem('terms', 'members.keyword', [x]),
                    ),
                );
                must.push(boolShould);
            }

            if (forDashboard) {
                must.push({
                    bool: {
                        should: [
                            this.getQueryItem(
                                'term',
                                'conversationCategory.keyword',
                                'SHARED',
                            ),
                            this.getQueryItem(
                                'term',
                                'conversationCategory.keyword',
                                'PERSONAL',
                            ),
                        ],
                    },
                });
            } else if (conversationCategory) {
                must.push(
                    this.getQueryItem(
                        'term',
                        'conversationCategory.keyword',
                        conversationCategory,
                    ),
                );
            }

            if (tag) {
                must.push(
                    this.getQueryItem('terms', 'selectedTagIds.keyword', [tag]),
                );
            }

            if (parentPropertyId) {
                must.push(
                    this.getQueryItem('terms', 'parentPropertyIds.keyword', [
                        parentPropertyId,
                    ]),
                );
            }

            if (propertyId) {
                must.push(
                    this.getQueryItem('terms', 'propertyIds.keyword', [
                        propertyId,
                    ]),
                );
            }

            if (startDate || endDate) {
                const dateParam: { gte?: string; lte?: string } = {};
                if (startDate) {
                    dateParam.gte = startDate;
                }
                if (endDate) {
                    dateParam.lte = endDate;
                }
                must.push({
                    range: {
                        lastMessageTime: dateParam,
                    },
                });
            }

            if (conversationTypes && conversationTypes.length > 0) {
                const boolShould: any = {
                    bool: {
                        should: [],
                    },
                };
                conversationTypes.forEach((x) => {
                    const filterEmail =
                        mainEmailAddresses && mainEmailAddresses.length > 0;
                    if (x !== 'EMAIL' || !filterEmail) {
                        boolShould.bool.should.push(
                            this.getQueryItem(
                                'term',
                                'conversationType.keyword',
                                x,
                            ),
                        );
                    } else {
                        const boolShouldEmail: any = {
                            bool: {
                                should: [],
                            },
                        };
                        mainEmailAddresses.forEach((x) =>
                            boolShouldEmail.bool.should.push(
                                this.getQueryItem(
                                    'term',
                                    'mainEmailAddress.keyword',
                                    x,
                                ),
                            ),
                        );
                        const boolMust: any = {
                            bool: {
                                must: [],
                            },
                        };
                        boolMust.bool.must.push(boolShouldEmail);
                        boolMust.bool.must.push(
                            this.getQueryItem(
                                'term',
                                'conversationType.keyword',
                                'EMAIL',
                            ),
                        );
                        boolShould.bool.should.push(boolMust);
                    }
                });
                must.push(boolShould);
            }

            // Build the query object for OpenSearch's search function
            const query = {
                index: 'conversation',
                body: {
                    from: 0,
                    size: 100,
                    query: {
                        bool: {
                            must: must,
                        },
                    },
                    sort: [
                        {
                            lastMessageTime: {
                                order: sortType === 'Oldest' ? 'asc' : 'desc',
                            },
                        },
                    ],
                    _source: forDashboard
                        ? [
                              'id',
                              'name',
                              'lastMessageTime',
                              'lastMessage',
                              'conversationType',
                              'members',
                              'createdAt',
                          ]
                        : undefined,
                },
            };

            this.logger.log(
                `Searching dashboard conversations for organisationId=${organisationId}`,
            );

            // Call the search function from OpenSearch service
            const hits = await osSearch(query);

            return { hits };
        } catch (e) {
            this.logger.error(`Error searching conversations: ${e}`);
            throw new Error('es query conversations fails');
        }
    }

    private getQueryItem(queryType: string, field: string, value: any): any {
        return {
            [queryType]: {
                [field]: value,
            },
        };
    }

    async checkExistingConversation(
        organisationId: string,
        memberIds: string[],
        category: string,
        isEmail?: boolean,
    ): Promise<{ exists: boolean; conversation?: any }> {
        try {
            const searchParams: any = {
                organisationId,
                memberIds,
                conversationCategory: category,
                fullMatchUserIdFlag: true,
            };

            if (isEmail) {
                searchParams.conversationTypes = ['EMAIL'];
            }

            const { hits } = await this.searchConversations(
                searchParams,
                false,
            );

            if (hits && hits.length > 0) {
                return {
                    exists: true,
                    conversation: {
                        id: hits[0].id,
                        type: hits[0].conversationType,
                        lastMessageTime: hits[0].lastMessageTime,
                    },
                };
            }

            return { exists: false };
        } catch (error) {
            this.logger.error(`Error checking existing conversation: ${error}`);
            throw new Error('Failed to check existing conversation');
        }
    }
}
