import { ApiProperty } from '@nestjs/swagger';

export class ConversationQueryDTO {
    @ApiProperty({ required: true })
    organisationId: string;

    @ApiProperty({ required: false })
    userId?: string;

    @ApiProperty({ required: false })
    fullMatchUserIdFlag?: boolean;

    @ApiProperty({ required: false })
    startDate?: string;

    @ApiProperty({ required: false })
    endDate?: string;

    @ApiProperty({ required: false })
    unread?: boolean;

    @ApiProperty({ required: false })
    recipient?: string;

    @ApiProperty({ required: false })
    tag?: string;

    @ApiProperty({ required: false, type: [String] })
    conversationTypes?: string[];

    @ApiProperty({ required: false })
    sortType?: string;

    @ApiProperty({ required: false })
    conversationCategory?: string;

    @ApiProperty({ required: false, type: [String] })
    memberIds?: string[];

    @ApiProperty({ required: false, type: [String] })
    randomMemberIds?: string[];

    @ApiProperty({ required: false })
    parentPropertyId?: string;

    @ApiProperty({ required: false })
    propertyId?: string;

    @ApiProperty({ required: false, type: [String] })
    mainEmailAddresses?: string[];

    @ApiProperty({ required: false })
    needMemberUserFlag?: boolean;
}
