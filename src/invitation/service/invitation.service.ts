import { Injectable, Logger } from '@nestjs/common';
import {
    CreateInvitationInput,
    CreateTenantInvitationInput,
    IdentityClaims,
} from '../model/invitation.model';
import { checkOrganisation } from '../../common/util/authorization';
import { UserRepository } from '../../common/repository/user';
import {
    InvitationError,
    InvitationErrorMessages,
} from '../../common/util/invitationError';
import { v1 as UUID } from 'uuid';
import { User } from '../../common/model/user';
import { OrganisationRepository } from '../../common/repository/organisation';
import { InvitationRepository } from '../../common/repository/invitation';
import { ENV_CONFIG } from '../../common/constant/config.constant';
import { PropertyRepository } from '../../common/repository/property';
import { TenancyRepository } from '../../common/repository/tenancy';
import { ConversationRepository } from '../../common/repository/conversation';
import { SesUtil } from '../../common/util/sesUtil';
import { OrganisationUserMetaDataRepository } from '../../common/repository/organisationUserMetaData';

@Injectable()
export class InvitationService {
    private readonly logger = new Logger('InvitationService', {
        timestamp: true,
    });

    TEMPLATES = {
        invitation: 'INVITATION',
        invitation_without_logo: 'INVITATION_WITHOUT_LOGO',
        tenancy_invitation: 'TENANCY_INVITATION',
        conversation_invitation: 'CONVERSATION_INVITATION',
        tenantportal_invitation: 'TENANTPORTAL_INVITATION',
        landlordportal_invitation: 'LANDLORDPORTAL_INVITATION',
    };

    SOURCE_EMAIL = `hello@${ENV_CONFIG.MAIL_DOMAIN}`;

    constructor(
        private readonly userRepository: UserRepository,
        private readonly organisationRepository: OrganisationRepository,
        private readonly invitationRepository: InvitationRepository,
        private readonly propertyRepository: PropertyRepository,
        private readonly tenancyRepository: TenancyRepository,
        private readonly conversationRepository: ConversationRepository,
        private readonly organisationUserMetaDataRepository: OrganisationUserMetaDataRepository,
    ) {}

    async createInvitation(
        inviterIdentity: IdentityClaims,
        input: CreateInvitationInput,
    ) {
        const {
            tenancyId,
            conversationId,
            role,
            userId,
            resendManually,
            invitedUserEmail,
        } = input;

        this.verifyInvitationRights(inviterIdentity, role);
        const inviteeOrganisationId = input.organisationId;
        checkOrganisation(
            inviterIdentity.organisationId,
            inviteeOrganisationId,
        );
        this.logger.log(
            `User will be invited to the organisation: ${inviteeOrganisationId}`,
        );
        if (role === 'TENANT') {
            const tenantInput = new CreateTenantInvitationInput(input);
            tenantInput.tenantId = userId;
            return await this.createTenantInvitation(
                inviterIdentity,
                tenantInput,
            );
        }
        const inviter = await this.userRepository.findByCognitoId(
            inviterIdentity.cognitoId,
        );
        this.logger.log(
            `Inviter: [id=${inviter?.id}, cognitoId=${inviterIdentity.cognitoId}, organisation=${inviterIdentity.organisationId}]`,
        );

        return await this.doCreateInvitation(
            inviter?.id,
            invitedUserEmail?.toLowerCase(),
            tenancyId,
            conversationId,
            role,
            userId,
            inviteeOrganisationId,
            resendManually,
        );
    }

    async doCreateInvitation(
        inviterId,
        invitedUserEmail,
        tenancyId,
        conversationId,
        role,
        userId,
        organisationId,
        resendManually,
    ) {
        if (role === 'NEW' || role === 'APPLICANT') {
            this.logger.log(
                `Unable to invite with role - ${role}, ${invitedUserEmail}`,
            );
            throw new InvitationError(InvitationErrorMessages.E0004U);
        }
        const uuid = UUID();

        const user = await this.userRepository.findById(inviterId);
        const senderName = this.getSenderName(user);
        const senderRole = this.getSenderRole(user);
        const senderOrganisationId = this.getSenderOrganisation(
            user,
            organisationId,
        );
        const senderOrganisation =
            await this.organisationRepository.findById(senderOrganisationId);

        this.logger.log(`Conversation id: ${conversationId}`);
        this.logger.log(
            `Organisations is: ${JSON.stringify(senderOrganisation)}`,
        );
        const organisationName = senderOrganisation.name
            ? senderOrganisation.name
            : 'Rentancy';
        const logo = senderOrganisation.logo
            ? senderOrganisation.logo
            : undefined;

        const invitations =
            await this.invitationRepository.listByEmailAndOrganisation(
                invitedUserEmail,
                senderOrganisationId,
            );
        if (invitations && invitations.length > 0) {
            let changedEmail;
            if (userId) {
                const user = await this.userRepository.findById(userId);
                this.logger.log(`User - ${JSON.stringify(user)}`);
                const { emails } = user;
                if (emails && emails.length) {
                    changedEmail = emails[0].email;
                }
            }
            this.logger.log(`Updated email - ${changedEmail}`);
            for (const invitation of invitations) {
                if (
                    invitation.invitationOrganisationId !== senderOrganisationId
                ) {
                    throw new InvitationError(InvitationErrorMessages.E0004U);
                }
                if (invitation.verified === true) {
                    throw new InvitationError(
                        InvitationErrorMessages.INVITATION_ALREADY_VERIFIED(
                            invitedUserEmail,
                        ),
                    );
                }

                if (!resendManually) {
                    throw new InvitationError(
                        InvitationErrorMessages.INVITATION_ALREADY_EXISTS(
                            invitedUserEmail,
                        ),
                    );
                }

                await this.invitationRepository.updateInvitation(
                    invitation,
                    tenancyId,
                    conversationId,
                    role,
                    changedEmail,
                );
            }
            await this.sendEmailV2(
                changedEmail ? changedEmail : invitedUserEmail,
                senderName,
                senderOrganisationId,
                organisationName,
                tenancyId,
                conversationId,
                senderRole,
                logo,
                role,
                userId,
            );
            return { inviterId: inviterId };
        }

        // TODO if user exists update it with invitation id
        let id;
        const existedUser =
            await this.userRepository.findByCognitoEmail(invitedUserEmail);
        this.logger.log(`Existed user - ${JSON.stringify(existedUser)}`);
        if (!userId && existedUser) {
            userId = existedUser.id;
        }
        if (userId) {
            this.logger.log(`Updating user`);
            id = userId;
            await this.userRepository.updateUserInvitationId(userId, uuid);
        } else {
            this.logger.log(`Creating user`);
            id = UUID();
            await this.createUser(
                id,
                uuid,
                role,
                invitedUserEmail,
                senderOrganisationId,
            );
        }
        await this.organisationUserMetaDataRepository.createOrganisationUserMetaData(
            senderOrganisationId,
            id,
        );

        await this.putItem(
            invitedUserEmail,
            inviterId,
            senderName,
            uuid,
            senderOrganisationId,
            tenancyId,
            conversationId,
            role,
            id,
        );
        await this.sendEmailV2(
            invitedUserEmail,
            senderName,
            senderOrganisationId,
            organisationName,
            tenancyId,
            conversationId,
            senderRole,
            logo,
            role,
            userId,
        );

        return { inviterId: inviterId };
    }

    async putItem(
        invitedUserEmail,
        inviterId,
        inviterName,
        uuid,
        organisationId,
        tenancyId,
        conversationId,
        role,
        userId,
    ) {
        const invitation = {
            id: uuid,
            email: invitedUserEmail,
            verified: false,
            inviterId: inviterId,
            inviterName: inviterName,
            invitationOrganisationId: organisationId,
            invitationUserId: userId,
            role: 'AGENT',
            owner: uuid,
            createdAt: new Date().toISOString(),
        };
        if (tenancyId !== undefined) {
            invitation['tenancyIds'] = [tenancyId];
            invitation['role'] = 'TENANT';
        }

        if (conversationId) {
            invitation['conversationIds'] = [conversationId];
            invitation['role'] = 'TENANT';
        }

        if (role && role !== '') {
            invitation['role'] = role;
        }
        await this.invitationRepository.createItem(invitation);
    }

    async sendEmailV2(
        invitedUserEmail,
        senderName,
        senderOrganisationId,
        senderOrganisation,
        tenancyId,
        conversationId,
        senderRole,
        logo,
        role,
        invitedUserId,
    ) {
        const actualLogo = logo
            ? `${ENV_CONFIG.PUBLIC_ORIGIN_DOMAIN}/public/${logo.startsWith('/') ? logo.slice(1) : logo}`
            : senderOrganisation;
        this.logger.log(`Actual logo - ${actualLogo}`);
        let data = {
            recipientEmail: invitedUserEmail,
            recipientName: 'Dear customer',
            senderName: senderName,
            senderOrganisation: senderOrganisation,
            senderOrganisationLogo: actualLogo,
            link: `${ENV_CONFIG.INVITATION_LINK}/${senderOrganisationId}?email=${encodeURIComponent(invitedUserEmail)}`,
            senderRole: senderRole,
        } as any;
        const request = {
            Source: `${senderName} (${senderRole}) <${this.SOURCE_EMAIL}>`,
            Destination: {
                ToAddresses: [invitedUserEmail],
            },
            Template: this.TEMPLATES.invitation,
            TemplateData: JSON.stringify(data),
        };
        if (role === 'LANDLORD') {
            data = {
                landlordName: 'customer',
                link: `${ENV_CONFIG.INVITATION_LINK}/${senderOrganisationId}?email=${encodeURIComponent(invitedUserEmail)}`,
                senderName,
                senderOrganisation,
            } as any;
            if (invitedUserId) {
                const landlord =
                    await this.userRepository.findById(invitedUserId);
                data.landlordName = [landlord.fname, landlord.sname]
                    .filter(Boolean)
                    .join(' ');
            }
            request.Template = this.TEMPLATES.landlordportal_invitation;
            request.TemplateData = JSON.stringify(data);
        } else {
            this.logger.log(`Sending email with data: ${JSON.stringify(data)}`);
            if (actualLogo === senderOrganisation) {
                request.Template = this.TEMPLATES.invitation_without_logo;
            }

            if (tenancyId !== undefined) {
                const tenancy =
                    await this.tenancyRepository.findById(tenancyId);
                const property = await this.propertyRepository.findById(
                    tenancy.tenancyPropertyId,
                );
                data.tenancyAddress = property.addressLine1;
                request.Template = this.TEMPLATES.tenancy_invitation;
                request.TemplateData = JSON.stringify(data);
            }

            if (conversationId) {
                const conversation =
                    await this.conversationRepository.findById(conversationId);
                data.conversationName = conversation.name;
                request.Template = this.TEMPLATES.conversation_invitation;
                request.TemplateData = JSON.stringify(data);
            }
        }

        this.logger.log('Sending email - ', request);

        await SesUtil.sendTemplatedEmail(request);

        this.logger.log('Sent');
    }

    getSenderOrganisation(user: User, organisationId: string): string {
        if (organisationId) {
            return organisationId;
        } else {
            return user.currentOrganisation;
        }
    }

    getSenderName(user: User) {
        const fname = user.fname;
        const sname = user.sname;
        return fname + ' ' + sname;
    }

    getSenderRole(user: User) {
        const roles = user.roles ? user.roles : ['AGENT'];
        this.logger.log(`Getting sender roles: ${JSON.stringify(roles)}`);
        if (roles.includes('HELPDESK')) {
            return 'Rentancy Administrator';
        } else if (roles.includes('ADMIN_AGENT')) {
            return 'Admin Agent';
        } else if (roles.includes('AGENT')) {
            return 'Agent';
        } else if (roles.includes('LANDLORD')) {
            return 'Landlord';
        } else if (roles.includes('TENANT')) {
            return 'Tenant';
        } else if (roles.includes('GUARANTOR')) {
            return 'Guarantor';
        } else if (roles.includes('MANAGER')) {
            return 'Manager';
        } else {
            return 'Rentancy';
        }
    }

    async createTenantInvitation(
        inviterIdentity: IdentityClaims,
        input: CreateTenantInvitationInput,
    ) {
        const {
            tenancyId,
            tenantId,
            conversationId,
            role,
            userId,
            resendManually,
            invitedUserEmail,
        } = input;

        this.verifyInvitationRights(inviterIdentity, role);
        const inviteeOrganisationId = input.organisationId;
        checkOrganisation(
            inviterIdentity.organisationId,
            inviteeOrganisationId,
        );

        const inviter = await this.userRepository.findByCognitoId(
            inviterIdentity.cognitoId,
        );
        this.logger.log(
            `Inviter: [id=${inviter?.id}, cognitoId=${inviterIdentity.cognitoId}, organisation=${inviterIdentity.organisationId}]`,
        );
        return await this.doCreateTenantInvitation(
            inviter?.id,
            invitedUserEmail?.toLowerCase(),
            tenancyId,
            tenantId,
            conversationId,
            role,
            userId,
            inviteeOrganisationId,
            resendManually,
        );
    }

    async doCreateTenantInvitation(
        inviterId,
        invitedUserEmail,
        tenancyId,
        tenantId,
        conversationId,
        role,
        userId,
        organisationId,
        resendManually,
    ) {
        if (role === 'NEW' || role === 'APPLICANT') {
            this.logger.log(
                `Unable to invite with role - ${role}, ${invitedUserEmail}`,
            );
            throw new InvitationError(InvitationErrorMessages.E0004U);
        }
        const uuid = UUID();

        const user = await this.userRepository.findById(inviterId);
        const senderName = this.getSenderName(user);
        const senderRole = this.getSenderRole(user);
        const senderOrganisationId = this.getSenderOrganisation(
            user,
            organisationId,
        );
        const senderOrganisation =
            await this.organisationRepository.findById(senderOrganisationId);

        this.logger.log(`Conversation id: ${conversationId}`);
        this.logger.log(
            `Organisations is: ${JSON.stringify(senderOrganisation)}`,
        );
        const organisationName = senderOrganisation.name
            ? senderOrganisation.name
            : 'Rentancy';
        const logo = senderOrganisation.logo
            ? senderOrganisation.logo
            : undefined;

        const invitations =
            await this.invitationRepository.listByEmailAndOrganisation(
                invitedUserEmail,
                senderOrganisationId,
            );
        if (invitations && invitations.length > 0) {
            let changedEmail;
            if (userId) {
                const user = await this.userRepository.findById(userId);
                this.logger.log(`User - ${JSON.stringify(user)}`);
                const { emails } = user;
                if (emails && emails.length) {
                    changedEmail = emails[0].email;
                }
            }
            this.logger.log(`Updated email - ${changedEmail}`);
            for (const invitation of invitations) {
                if (
                    invitation.invitationOrganisationId !== senderOrganisationId
                ) {
                    throw new InvitationError(InvitationErrorMessages.E0004U);
                }
                if (invitation.verified === true) {
                    throw new InvitationError(
                        InvitationErrorMessages.INVITATION_ALREADY_VERIFIED(
                            invitedUserEmail,
                        ),
                    );
                }

                if (!resendManually) {
                    throw new InvitationError(
                        InvitationErrorMessages.INVITATION_ALREADY_EXISTS(
                            invitedUserEmail,
                        ),
                    );
                }

                await this.invitationRepository.updateInvitation(
                    invitation,
                    tenancyId,
                    conversationId,
                    role,
                    changedEmail,
                );
            }
            await this.sendTenantInvitationEmail(
                changedEmail ? changedEmail : invitedUserEmail,
                senderName,
                senderOrganisationId,
                organisationName,
                tenantId,
                conversationId,
                senderRole,
                logo,
            );
            return { inviterId: inviterId };
        }

        // TODO if user exists update it with invitation id
        let id;
        const existedUser =
            await this.userRepository.findByCognitoEmail(invitedUserEmail);
        this.logger.log(`Existed user - ${JSON.stringify(existedUser)}`);
        if (!userId && existedUser) {
            userId = existedUser.id;
        }
        if (userId) {
            this.logger.log(`Updating user`);
            id = userId;
            await this.userRepository.updateUserInvitationId(userId, uuid);
        } else {
            this.logger.log(`Creating user`);
            id = UUID();
            await this.createUser(
                id,
                uuid,
                role,
                invitedUserEmail,
                senderOrganisationId,
            );
        }

        await this.organisationUserMetaDataRepository.createOrganisationUserMetaData(
            senderOrganisationId,
            id,
        );

        await this.putItem(
            invitedUserEmail,
            inviterId,
            senderName,
            uuid,
            senderOrganisationId,
            tenancyId,
            conversationId,
            role,
            id,
        );
        await this.sendTenantInvitationEmail(
            invitedUserEmail,
            senderName,
            senderOrganisationId,
            organisationName,
            tenantId,
            conversationId,
            senderRole,
            logo,
        );

        return { inviterId: inviterId };
    }

    async createUser(id, invitationId, role, email, organisationId) {
        const user = {
            id: id,
            public: true,
            type: role,
            emails: [{ type: 'Work', email: email }],
            userInvitationId: invitationId,
            currentOrganisation: organisationId,
            createdDate: new Date().toISOString(),
        } as User;
        await this.userRepository.createItem(user);
    }

    async sendTenantInvitationEmail(
        invitedUserEmail,
        senderName,
        senderOrganisationId,
        senderOrganisation,
        tenantId,
        conversationId,
        senderRole,
        logo,
    ) {
        const actualLogo = logo
            ? `${ENV_CONFIG.PUBLIC_ORIGIN_DOMAIN}/public/${logo.startsWith('/') ? logo.slice(1) : logo}`
            : senderOrganisation;
        this.logger.log(`Actual logo - ${actualLogo}`);

        this.logger.log('tenantId: ' + tenantId);
        const data = {
            senderOrganisation: senderOrganisation,
            senderName: senderName,
            tenantUrl: `${ENV_CONFIG.INVITATION_LINK}/${senderOrganisationId}?email=${encodeURIComponent(invitedUserEmail)}`,
        };
        this.logger.log(`Sending email with data: ${JSON.stringify(data)}`);
        const request = {
            Source: `${senderName} (${senderRole}) <${this.SOURCE_EMAIL}>`,
            Destination: {
                ToAddresses: [invitedUserEmail],
            },
            Template: this.TEMPLATES.tenantportal_invitation,
            TemplateData: JSON.stringify(data),
        };

        this.logger.log('Sending email - ', request);
        await SesUtil.sendTemplatedEmail(request);
        this.logger.log('Sent');
    }

    verifyInvitationRights(
        inviterIdentity: IdentityClaims,
        inviteeRole: string,
    ) {
        // assuming that we have appropriate amplify authorization setup
        if (inviterIdentity.roles.includes('ADMIN_AGENT')) {
            return;
        }

        if (inviteeRole === 'ADMIN_AGENT') {
            throw new Error(
                `User with roles: ${inviterIdentity.roles} cannot invite admin user`,
            );
        }
    }
}
