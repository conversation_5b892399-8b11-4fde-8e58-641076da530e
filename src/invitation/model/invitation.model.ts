import { ApiProperty } from '@nestjs/swagger';

export class CreateInvitationInput {
    @ApiProperty()
    userId: string;
    @ApiProperty()
    invitedUserEmail: string;
    @ApiProperty()
    tenancyId: string;
    @ApiProperty()
    conversationId: string;
    @ApiProperty()
    organisationId: string;
    @ApiProperty()
    role: string;
    @ApiProperty()
    resendManually: boolean;
}

export class CreateTenantInvitationInput extends CreateInvitationInput {
    constructor(createInvitationInput: CreateInvitationInput) {
        super();
        this.userId = createInvitationInput.userId;
        this.invitedUserEmail = createInvitationInput.invitedUserEmail;
        this.tenancyId = createInvitationInput.tenancyId;
        this.conversationId = createInvitationInput.conversationId;
        this.organisationId = createInvitationInput.organisationId;
        this.role = createInvitationInput.role;
        this.resendManually = createInvitationInput.resendManually;
    }
    @ApiProperty()
    tenantId: string;
}

export class IdentityClaims {
    @ApiProperty()
    organisationId: string;
    @ApiProperty()
    cognitoId: string;
    @ApiProperty()
    roles: string[];
}
