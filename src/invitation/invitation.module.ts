import { Module } from '@nestjs/common';
import { EventsEmitterService } from '../common/service/eventsEmitter.service';
import { InvitationService } from './service/invitation.service';
import { InvitationController } from './controller/api/invitation.controller';

@Module({
    controllers: [InvitationController],
    providers: [InvitationService, EventsEmitterService],
    exports: [InvitationService],
})
export class InvitationModule {}
