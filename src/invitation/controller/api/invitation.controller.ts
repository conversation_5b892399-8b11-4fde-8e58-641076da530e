import { Body, Controller, Logger, Put, Req, Res } from '@nestjs/common';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import {
    ApiBearerAuth,
    ApiBody,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    getSchemaPath,
} from '@nestjs/swagger';
import { InvitationService } from '../../service/invitation.service';
import { Response } from 'express';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    CreateInvitationInput,
    IdentityClaims,
} from '../../model/invitation.model';

@Controller('api/v1/invitation')
@ApiExtraModels(BaseRes)
@ApiBearerAuth()
export class InvitationController {
    private readonly logger = new Logger('InvitationController', {
        timestamp: true,
    });

    constructor(private readonly invitationService: InvitationService) {}

    @ApiOperation({ summary: 'Create Invitation' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        type: CreateInvitationInput,
        description: 'Create invitation',
    })
    @Put('/')
    public async createInvitation(
        @Body() input: CreateInvitationInput,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const identityClaims = {
                organisationId: req.user['custom:organisationId'],
                cognitoId: req.user.sub,
                roles: req.user['cognito:groups'],
            } as IdentityClaims;
            const result = await this.invitationService.createInvitation(
                identityClaims,
                input,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
