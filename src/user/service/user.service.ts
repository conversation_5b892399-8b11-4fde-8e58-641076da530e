import { Injectable, Logger } from '@nestjs/common';
import { UserPreferencesVO, UserSearchDTO } from '../model/user.model';
import { searchWithTotal } from 'src/common/service/opensearch.service';
import { AddressRepository } from '../../common/repository/address';
import { UserPreferenceRepository } from '../../common/repository/userPreference';
import { UserRepository } from '../../common/repository/user';
import { OrganisationUserRepository } from '../../common/repository/organisationUser';
import { checkOrganisation } from '../../common/util/authorization';
import { OrganisationRepository } from '../../common/repository/organisation';
import { IntegrationRepository } from '../../common/repository/integration';
import { User } from '../../common/model/user';
import { SessionRepository } from '../../common/repository/session';

@Injectable()
export class UserService {
    private logger = new Logger('UserService', { timestamp: true });

    constructor(
        private readonly addressRepository: AddressRepository,
        private readonly userPreferenceRepository: UserPreferenceRepository,
        private readonly userRepository: UserRepository,
        private readonly organisationUserRepository: OrganisationUserRepository,
        private readonly organisationRepository: OrganisationRepository,
        private readonly integrationRepository: IntegrationRepository,
        private readonly sessionRepository: SessionRepository,
    ) {}

    async searchUsers({
        organisationId,
        name,
        fname,
        sname,
        email,
        phone,
        type,
        types,
    }: UserSearchDTO): Promise<{ total: number; contactList: any[] }> {
        try {
            const query: any = {
                bool: {
                    must: [
                        {
                            term: {
                                'currentOrganisation.keyword': organisationId,
                            },
                        },
                        {
                            exists: {
                                field: 'createdIn',
                            },
                        },
                    ],
                },
            };

            if (name) {
                query.bool.must.push({
                    wildcard: {
                        'companyName.keyword': `*${name}*`,
                    },
                });
            }

            if (fname) {
                query.bool.must.push({
                    wildcard: {
                        'fname.keyword': `*${fname}*`,
                    },
                });
            }

            if (sname) {
                query.bool.must.push({
                    wildcard: {
                        'sname.keyword': `*${sname}*`,
                    },
                });
            }

            if (email) {
                query.bool.must.push({
                    wildcard: {
                        'emails.email.keyword': `*${email}*`,
                    },
                });
            }
            if (phone) {
                query.bool.must.push({
                    wildcard: {
                        'phones.keyword': `*${phone}*`,
                    },
                });
            }

            if (types && types.length > 0) {
                query.bool.must.push({
                    terms: {
                        'type.keyword': types,
                    },
                });
            } else if (type) {
                query.bool.must.push({
                    term: {
                        'type.keyword': type,
                    },
                });
            }

            const result = await searchWithTotal({
                index: 'user',
                body: {
                    query,
                    _source: [
                        'id',
                        'companyName',
                        'fname',
                        'sname',
                        'type',
                        'emails',
                        'phones',
                    ],
                    size: 100,
                    track_total_hits: true,
                },
            });

            return {
                total: result.total,
                contactList: result.hits,
            };
        } catch (e) {
            this.logger.error(e);
            throw new Error('es query fails');
        }
    }

    async postalAddress(userId: string) {
        return await this.findAddress(userId, 'POSTAL');
    }

    async findAddress(userId: string, type: string) {
        const addresses =
            await this.addressRepository.findUserAddresses(userId);
        this.logger.log(`findAddress:${JSON.stringify(addresses)}`);
        return addresses.find((address) => address.type === type);
    }

    async listUserPreferences(userId: string) {
        return (await this.userPreferenceRepository.listByUserId(userId)).map(
            (i) => {
                return {
                    id: i.id,
                    allowedNotifications: i.allowedNotifications,
                };
            },
        ) as UserPreferencesVO[];
    }

    async updateAllowedNotifications(
        id: string,
        notifications: string[],
    ): Promise<UserPreferencesVO> {
        return await this.userPreferenceRepository.updateAllowedNotifications(
            id,
            notifications,
        );
    }

    async findUserByCognitoId(cognitoId: string, email: string) {
        let result = await this.userRepository.findByCognitoId(cognitoId);
        if (!result && email && email !== '') {
            try {
                const user = await this.userRepository.getCognitoUser(email);
                if (user) {
                    result = await this.userRepository.findByCognitoId(
                        user.Username,
                    );
                }
            } catch (e) {
                this.logger.log(`findUserByCognitoId:adminGetUser error ${e}`);
                return {};
            }
        }
        return result ? result : {};
    }

    async findOrganisationUser(cognitoId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        if (user) {
            const organisationUsers =
                await this.organisationUserRepository.findByUserId(user.id);
            if (organisationUsers && organisationUsers.length > 0) {
                const filterResult = organisationUsers.filter(
                    (x) =>
                        x.organisationUserOrganisationId ===
                        user.currentOrganisation,
                );
                if (filterResult && filterResult.length > 0) {
                    return filterResult[0];
                }
            }
        }
        return {};
    }

    async changeUserRole(mutator: string, id: string, newRole: string) {
        this.logger.log(id);
        const user = await this.userRepository.findById(id);
        const mutatorUser = await this.userRepository.findByCognitoId(mutator);
        const oldRole = user.roles.length > 0 ? user.roles[0] : undefined;

        checkOrganisation(
            mutatorUser.currentOrganisation,
            user.currentOrganisation,
        );

        if (oldRole === newRole) {
            return user;
        }

        if (oldRole !== 'ADMIN_AGENT') {
            await this.setUserRole(user, newRole);
            return await this.userRepository.findById(id);
        } else {
            const organisation = await this.organisationRepository.findById(
                user.currentOrganisation,
            );

            if (organisation.adminUser === user.id) {
                const adminUsers = await this.findAdminUsers(organisation.id);

                const newOrganisationAdmin = adminUsers.find(
                    (currentUser) => currentUser.id !== user.id,
                );

                // We can't delete the only admin user from the workspace
                if (!newOrganisationAdmin) {
                    return null;
                }
                await this.organisationRepository.updateItem(organisation.id, {
                    adminUser: newOrganisationAdmin.id,
                });

                const integrations =
                    await this.integrationRepository.findIntegrationByOrganisationId(
                        organisation.id,
                    );
                await Promise.all(
                    integrations.map(async (integration) =>
                        this.integrationRepository.updateItem(integration.id, {
                            userId: newOrganisationAdmin.id,
                        }),
                    ),
                );
                await this.setUserRole(user, newRole);
                return await this.userRepository.findById(id);
            } else {
                await this.setUserRole(user, newRole);
                return await this.userRepository.findById(id);
            }
        }
    }

    async findAdminUsers(organisationId) {
        const userIds = (
            await this.organisationUserRepository.findByOrganisationId(
                organisationId,
            )
        ).map((user) => user.organisationUserUserId);
        const adminUsers = [];

        await Promise.all(
            userIds.map(async (userId) => {
                const user = await this.userRepository.findById(userId);
                if (user && user.roles && user.roles.includes('ADMIN_AGENT')) {
                    adminUsers.push(user);
                }
            }),
        );
        return adminUsers;
    }

    async setUserRole(user, role) {
        await this.userRepository.revokeToken(user.cognitoId);

        await this.userRepository.removeCognitoRole(
            user.cognitoId,
            user.roles[0],
        );
        await this.userRepository.addCognitoRole(user.cognitoId, role);

        const attributes = {
            roles: [role],
            type: this.mapToUserType(role),
            forceSignOut: true,
        };
        await this.userRepository.updateItem(user.id, attributes);
    }

    mapToUserType(role) {
        switch (role) {
            case 'TENANT':
                return 'TENANT';
            case 'LANDLORD':
                return 'LANDLORD';
            case 'THIRD_PARTY':
                return 'SUPPLIER';
            case 'GUARANTOR':
                return 'GUARANTOR';
            case 'UNASSIGNED':
                return 'OTHER';
            case 'OTHER':
                return 'UNASSIGNED';
            case 'AGENT':
                return 'TEAM';
            case 'FINANCE':
                return 'FINANCE';
            case 'ADMIN_AGENT':
                return 'ADMIN';
            default:
                return role;
        }
    }

    async addRoleToUser(mutator, id, newRole) {
        const user = await this.userRepository.findById(id);
        if (!user) {
            throw new Error('User not found');
        }

        this.logger.log(`Adding role ${newRole} to user: ${id}`);
        if (!mutator.groups?.includes('ADMIN_AGENT')) {
            throw new Error('Non-admin users cannot add roles for others');
        }

        if (mutator.organisation !== user?.currentOrganisation) {
            throw new Error(
                'Cannot add role for a user from different organisation',
            );
        }

        const roles = user.roles ? user.roles : [];

        if (roles.includes(newRole)) {
            this.logger.log(
                `Role is already assigned to user - ${newRole}, ${id}`,
            );
        } else {
            roles.push(newRole);
        }

        const attributes = {
            roles: roles,
            forceSignOut: true,
        };
        await Promise.all([
            this.userRepository.addCognitoRole(user.cognitoId, newRole),
            this.userRepository.updateItem(user.id, attributes),
            this.userRepository.revokeToken(user.cognitoId),
        ] as any);

        return await this.userRepository.findById(id);
    }

    async removeRoleFromUser(
        id: string,
        role: string,
        organisationId: string,
    ): Promise<User> {
        const user = await this.userRepository.findById(id);
        checkOrganisation(user.currentOrganisation, organisationId);
        let roles = user.roles ? user.roles : [];

        if (!roles.includes(role)) {
            this.logger.log(`Role isn't assigned to user - ${roles}, ${id}`);
        } else {
            roles = roles.filter((r) => r !== role);
        }

        const attributes = {
            roles: roles,
            forceSignOut: true,
        };

        await Promise.all([
            this.userRepository.removeCognitoRole(user.cognitoId, role),
            this.userRepository.updateItem(user.id, attributes),
            this.userRepository.revokeToken(user.cognitoId),
        ] as any);

        return await this.userRepository.findById(id);
    }

    async updateUserSession(cognitoId: string): Promise<void> {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        const session = await this.sessionRepository.findById(user.id);
        const updatedDate = (Date.now() + 40000).toString();
        const status = true;
        if (session) {
            await this.sessionRepository.updateItem(session.id, {
                updatedDate,
                status,
            });
        } else {
            await this.sessionRepository.createItem({
                id: user.id,
                updatedDate,
                status,
            });
        }
    }
}
