import { Injectable, Logger } from '@nestjs/common';
import { v1 as uuidV1 } from 'uuid';
import {
    ApplicantReportVO,
    ImageVO,
    InvitationOrganisationVO,
    InvitationVO,
    UserBusinessVO,
    UserDetailVO,
    UserPreferencesVO,
    UserSearchDTO,
} from '../model/user.model';
import { RedisService } from 'src/common/service/redis.service';
import { searchWithTotal } from 'src/common/service/opensearch.service';
import { AddressRepository } from '../../common/repository/address';
import { UserPreferenceRepository } from '../../common/repository/userPreference';
import { UserRepository } from '../../common/repository/user';
import { OrganisationUserRepository } from '../../common/repository/organisationUser';
import { checkOrganisation } from '../../common/util/authorization';
import { OrganisationRepository } from '../../common/repository/organisation';
import { IntegrationRepository } from '../../common/repository/integration';
import { User } from '../../common/model/user';
import { SessionRepository } from '../../common/repository/session';
import { BusinessRepository } from '../../common/repository/business';
import { FileRepository } from '../../common/repository/file';
import { InvitationRepository } from '../../common/repository/invitation';
import { ApplicantScheduleViewingRepository } from '../../common/repository/applicantScheduleViewing';
const moment = require('moment');

@Injectable()
export class UserService {
    private readonly logger = new Logger('UserService', { timestamp: true });

    constructor(
        private readonly addressRepository: AddressRepository,
        private readonly userPreferenceRepository: UserPreferenceRepository,
        private readonly userRepository: UserRepository,
        private readonly organisationUserRepository: OrganisationUserRepository,
        private readonly organisationRepository: OrganisationRepository,
        private readonly integrationRepository: IntegrationRepository,
        private readonly sessionRepository: SessionRepository,
        private readonly businessRepository: BusinessRepository,
        private readonly fileRepository: FileRepository,
        private readonly invitationRepository: InvitationRepository,
        private readonly applicantScheduleViewingRepository: ApplicantScheduleViewingRepository,
        private readonly redisService: RedisService,
    ) {}

    async fetchTicketCode(ticket: string) {
        try {
            const uuid = uuidV1();
            this.redisService.set(`ticketCode:${uuid}`, ticket, 60 * 5);
            // return { code: uuid };
            return { code: `_T=${ticket}` };
        } catch (err) {
            this.logger.log(`fetchTicket:error=${JSON.stringify(err)}`);
            return {};
        }
    }

    async searchUsers({
        organisationId,
        name,
        fname,
        sname,
        email,
        phone,
        type,
        types,
    }: UserSearchDTO): Promise<{ total: number; contactList: any[] }> {
        try {
            const query: any = {
                bool: {
                    must: [
                        {
                            term: {
                                'currentOrganisation.keyword': organisationId,
                            },
                        },
                        {
                            exists: {
                                field: 'createdIn',
                            },
                        },
                    ],
                },
            };

            if (name) {
                query.bool.must.push({
                    wildcard: {
                        'companyName.keyword': `*${name}*`,
                    },
                });
            }

            if (fname) {
                query.bool.must.push({
                    wildcard: {
                        'fname.keyword': `*${fname}*`,
                    },
                });
            }

            if (sname) {
                query.bool.must.push({
                    wildcard: {
                        'sname.keyword': `*${sname}*`,
                    },
                });
            }

            if (email) {
                query.bool.must.push({
                    wildcard: {
                        'emails.email.keyword': `*${email}*`,
                    },
                });
            }
            if (phone) {
                query.bool.must.push({
                    wildcard: {
                        'phones.keyword': `*${phone}*`,
                    },
                });
            }

            if (types && types.length > 0) {
                query.bool.must.push({
                    terms: {
                        'type.keyword': types,
                    },
                });
            } else if (type) {
                query.bool.must.push({
                    term: {
                        'type.keyword': type,
                    },
                });
            }

            const result = await searchWithTotal({
                index: 'user',
                body: {
                    query,
                    _source: [
                        'id',
                        'companyName',
                        'fname',
                        'sname',
                        'type',
                        'emails',
                        'phones',
                    ],
                    size: 100,
                    track_total_hits: true,
                },
            });

            return {
                total: result.total,
                contactList: result.hits,
            };
        } catch (e) {
            this.logger.error(e);
            throw new Error('es query fails');
        }
    }

    async postalAddress(userId: string) {
        return await this.findAddress(userId, 'POSTAL');
    }

    async findAddress(userId: string, type: string) {
        const addresses =
            await this.addressRepository.findUserAddresses(userId);
        this.logger.log(`findAddress:${JSON.stringify(addresses)}`);
        return addresses.find((address) => address.type === type);
    }

    async listUserPreferences(userId: string) {
        return (await this.userPreferenceRepository.listByUserId(userId)).map(
            (i) => {
                return {
                    id: i.id,
                    allowedNotifications: i.allowedNotifications,
                };
            },
        ) as UserPreferencesVO[];
    }

    async updateAllowedNotifications(
        id: string,
        notifications: string[],
    ): Promise<UserPreferencesVO> {
        return await this.userPreferenceRepository.updateAllowedNotifications(
            id,
            notifications,
        );
    }

    /**
     * Find user by cognitoId or email
     * @param cognitoId cognitoId from login info
     */
    async findUserByCognitoId(cognitoId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        if (!user) {
            this.logger.warn(`user not found, CognitoId: ${cognitoId}`);
            return {};
        }

        const userDetail = new UserDetailVO(user);
        // noinspection TypeScriptValidateTypes
        const [business, image, invitations] = (await Promise.all([
            // business
            (async () => {
                if (!user.userBusinessId) {
                    return null;
                }
                const business = await this.businessRepository.findById(
                    user.userBusinessId,
                );
                return business ? new UserBusinessVO(business) : null;
            })(),

            // file
            (async () => {
                if (!user.userImageId) return null;
                const file = await this.fileRepository.findById(
                    user.userImageId,
                );
                return file ? new ImageVO(file) : null;
            })(),

            // invitation
            (async () => {
                const invitations =
                    await this.invitationRepository.listByUserId(user.id);
                if (!invitations?.length) return [];

                return Promise.all(
                    invitations.map(async (invitation) => {
                        const invitationVO = new InvitationVO(invitation);
                        if (invitation.invitationOrganisationId) {
                            const org =
                                await this.organisationRepository.findById(
                                    invitation.invitationOrganisationId,
                                );
                            org &&
                                (invitationVO.organisation =
                                    new InvitationOrganisationVO(org));
                        }
                        return invitationVO;
                    }),
                );
            })(),
        ])) as any[];

        business && (userDetail.business = business);
        image && (userDetail.image = image);
        invitations?.length && (userDetail.invitations = invitations);

        return userDetail;
    }

    async findOrganisationUser(cognitoId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        if (user) {
            const organisationUsers =
                await this.organisationUserRepository.findByUserId(user.id);
            if (organisationUsers && organisationUsers.length > 0) {
                const filterResult = organisationUsers.filter(
                    (x) =>
                        x.organisationUserOrganisationId ===
                        user.currentOrganisation,
                );
                if (filterResult && filterResult.length > 0) {
                    return filterResult[0];
                }
            }
        }
        return {};
    }

    async changeUserRole(mutator: string, id: string, newRole: string) {
        this.logger.log(id);
        const user = await this.userRepository.findById(id);
        const mutatorUser = await this.userRepository.findByCognitoId(mutator);
        const oldRole = user.roles.length > 0 ? user.roles[0] : undefined;

        checkOrganisation(
            mutatorUser.currentOrganisation,
            user.currentOrganisation,
        );

        if (oldRole === newRole) {
            return user;
        }

        if (oldRole !== 'ADMIN_AGENT') {
            await this.setUserRole(user, newRole);
            return await this.userRepository.findById(id);
        } else {
            const organisation = await this.organisationRepository.findById(
                user.currentOrganisation,
            );

            if (organisation.adminUser === user.id) {
                const adminUsers = await this.findAdminUsers(organisation.id);

                const newOrganisationAdmin = adminUsers.find(
                    (currentUser) => currentUser.id !== user.id,
                );

                // We can't delete the only admin user from the workspace
                if (!newOrganisationAdmin) {
                    return null;
                }
                await this.organisationRepository.updateItem(organisation.id, {
                    adminUser: newOrganisationAdmin.id,
                });

                const integrations =
                    await this.integrationRepository.findIntegrationByOrganisationId(
                        organisation.id,
                    );
                await Promise.all(
                    integrations.map(async (integration) =>
                        this.integrationRepository.updateItem(integration.id, {
                            userId: newOrganisationAdmin.id,
                        }),
                    ),
                );
                await this.setUserRole(user, newRole);
                return await this.userRepository.findById(id);
            } else {
                await this.setUserRole(user, newRole);
                return await this.userRepository.findById(id);
            }
        }
    }

    async findAdminUsers(organisationId) {
        const userIds = (
            await this.organisationUserRepository.findByOrganisationId(
                organisationId,
            )
        ).map((user) => user.organisationUserUserId);
        const adminUsers = [];

        await Promise.all(
            userIds.map(async (userId) => {
                const user = await this.userRepository.findById(userId);
                if (user && user.roles && user.roles.includes('ADMIN_AGENT')) {
                    adminUsers.push(user);
                }
            }),
        );
        return adminUsers;
    }

    async setUserRole(user, role) {
        await this.userRepository.revokeToken(user.cognitoId);

        await this.userRepository.removeCognitoRole(
            user.cognitoId,
            user.roles[0],
        );
        await this.userRepository.addCognitoRole(user.cognitoId, role);

        const attributes = {
            roles: [role],
            type: this.mapToUserType(role),
            forceSignOut: true,
        };
        await this.userRepository.updateItem(user.id, attributes);
    }

    mapToUserType(role) {
        switch (role) {
            case 'TENANT':
                return 'TENANT';
            case 'LANDLORD':
                return 'LANDLORD';
            case 'THIRD_PARTY':
                return 'SUPPLIER';
            case 'GUARANTOR':
                return 'GUARANTOR';
            case 'UNASSIGNED':
                return 'OTHER';
            case 'OTHER':
                return 'UNASSIGNED';
            case 'AGENT':
                return 'TEAM';
            case 'FINANCE':
                return 'FINANCE';
            case 'ADMIN_AGENT':
                return 'ADMIN';
            default:
                return role;
        }
    }

    async addRoleToUser(mutator, id, newRole) {
        const user = await this.userRepository.findById(id);
        if (!user) {
            throw new Error('User not found');
        }

        this.logger.log(`Adding role ${newRole} to user: ${id}`);
        if (!mutator.groups?.includes('ADMIN_AGENT')) {
            throw new Error('Non-admin users cannot add roles for others');
        }

        if (mutator.organisation !== user?.currentOrganisation) {
            throw new Error(
                'Cannot add role for a user from different organisation',
            );
        }

        const roles = user.roles ? user.roles : [];

        if (roles.includes(newRole)) {
            this.logger.log(
                `Role is already assigned to user - ${newRole}, ${id}`,
            );
        } else {
            roles.push(newRole);
        }

        const attributes = {
            roles: roles,
            forceSignOut: true,
        };
        await Promise.all([
            this.userRepository.addCognitoRole(user.cognitoId, newRole),
            this.userRepository.updateItem(user.id, attributes),
            this.userRepository.revokeToken(user.cognitoId),
        ] as any);

        return await this.userRepository.findById(id);
    }

    async removeRoleFromUser(
        id: string,
        role: string,
        organisationId: string,
    ): Promise<User> {
        const user = await this.userRepository.findById(id);
        checkOrganisation(user.currentOrganisation, organisationId);
        let roles = user.roles ? user.roles : [];

        if (!roles.includes(role)) {
            this.logger.log(`Role isn't assigned to user - ${roles}, ${id}`);
        } else {
            roles = roles.filter((r) => r !== role);
        }

        const attributes = {
            roles: roles,
            forceSignOut: true,
        };

        await Promise.all([
            this.userRepository.removeCognitoRole(user.cognitoId, role),
            this.userRepository.updateItem(user.id, attributes),
            this.userRepository.revokeToken(user.cognitoId),
        ] as any);

        return await this.userRepository.findById(id);
    }

    async updateUserSession(cognitoId: string): Promise<void> {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        const session = await this.sessionRepository.findById(user.id);
        const updatedDate = (Date.now() + 40000).toString();
        const status = true;
        if (session) {
            await this.sessionRepository.updateItem(session.id, {
                updatedDate,
                status,
            });
        } else {
            await this.sessionRepository.createItem({
                id: user.id,
                updatedDate,
                status,
            });
        }
    }

    async getApplicantReport(
        cognitoId: string,
        startDate: string,
        endDate: string,
        sortBy: string,
    ): Promise<ApplicantReportVO[]> {
        this.logger.log(
            `userId=${cognitoId}, startDate=${startDate}, endDate=${endDate}, sortBy=${sortBy}`,
        );
        const user = await this.userRepository.findByCognitoId(cognitoId);
        this.logger.log('user org:' + JSON.stringify(user.currentOrganisation));
        // TODO: The number of applicants may be too large
        const applicants =
            await this.userRepository.findApplicantsByOrganisation(
                user.currentOrganisation,
            );
        this.logger.log('applicants: ' + applicants.length);

        const rows: ApplicantReportVO[] = [];
        for (const item of applicants) {
            const pmNames = [];
            if (
                item['applicantMetaData'] &&
                item['applicantMetaData']['lettingsNegotiators']
            ) {
                const pmList = item['applicantMetaData']['lettingsNegotiators'];
                this.logger.log('pmList.length: ' + pmList.length);
                for (const pmId of pmList) {
                    const pmUser = await this.userRepository.findById(pmId);
                    const tmpName =
                        this.getIfPresent(pmUser, 'fname') +
                        ' ' +
                        this.getIfPresent(pmUser, 'sname');
                    if (tmpName.trim().length > 0) {
                        pmNames.push(tmpName.trim());
                    }
                }
            }
            const pmName = pmNames.join(', ');

            this.logger.log('find views by applicantId: ' + item.id);
            let views =
                await this.applicantScheduleViewingRepository.getApplicantScheduleViewingByApplicantId(
                    item.id,
                );
            let viewingsArranged = 0;
            let nextViewingDate = '';
            if (views && views.length > 0) {
                viewingsArranged = views.length;
                const now = moment();
                const filtered_views = views.filter(
                    (p1) => p1['startDate'] && moment(p1.startDate) > now,
                );
                const sorted_views = filtered_views.sort(
                    (p1, p2) => moment(p1.startDate) - moment(p2.startDate),
                );
                views = sorted_views;
            }
            if (views && views.length > 0) {
                this.logger.log('find views: ' + views.length);
                // viewingsArranged = "0";
                nextViewingDate = this.getIfPresent(views[0], 'startDate');
            }

            const applicantName =
                this.getIfPresent(item, 'fname') +
                ' ' +
                this.getIfPresent(item, 'sname');
            const row = {
                applicantName: applicantName,
                lettingsNegotiator: pmName,
                propertyManager: pmName,
                leadStatus: this.getIfGrandPresent(
                    item,
                    'applicantMetaData',
                    'status',
                ),
                lastUpdatedDate: this.getIfPresent(item, 'updatedAt'),
                viewingsArranged: viewingsArranged,
                nextViewingDate: nextViewingDate,
                date: this.getIfPresent(item, 'createdAt'),
            } as ApplicantReportVO;
            rows.push(row);
        }
        const filtered_rows = rows.filter(
            (p1) =>
                moment(p1.date) >= moment(startDate) &&
                moment(p1.date) <= moment(endDate),
        );
        const sorted_rows = filtered_rows.sort(
            (p1, p2) => moment(p2.date) - moment(p1.date),
        );
        return sorted_rows;
    }

    private getIfPresent(object, field) {
        return object ? (object[field] ? object[field] : '') : '';
    }

    private getIfGrandPresent(object, field, grandField) {
        return object
            ? object[field]
                ? this.getIfPresent(object[field], grandField)
                : ''
            : '';
    }
}
