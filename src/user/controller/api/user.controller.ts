import {
    Body,
    Controller,
    Delete,
    Get,
    Logger,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiBody,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    ApiQuery,
    getSchemaPath,
} from '@nestjs/swagger';
import { UserService } from '../../service/user.service';
import { UserPreferencesVO } from '../../model/user.model';
import { User } from '../../../common/model/user';

@Controller('api/v1/user')
@ApiExtraModels(BaseRes, UserPreferencesVO)
@ApiBearerAuth()
export class UserController {
    private readonly logger = new Logger('UserController', {
        timestamp: true,
    });

    constructor(private readonly userService: UserService) {}

    @ApiOperation({ summary: 'List UserPreferences' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                        },
                        items: {
                            $ref: getSchemaPath(UserPreferencesVO),
                        },
                    },
                },
            ],
        },
    })
    @Get('/user-preferences/:userId')
    public async listUserPreferences(
        @Param('userId') userId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.listUserPreferences(userId);
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update UserPreferences' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(UserPreferencesVO) },
                    },
                },
            ],
        },
    })
    @ApiBody({
        type: 'string',
        isArray: true,
        description: 'AllowedNotifications',
    })
    @Post('/user-preferences/:id')
    public async updateUserPreferences(
        @Body() allowedNotifications: string[],
        @Param('id') id: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.updateAllowedNotifications(
                id,
                allowedNotifications,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'find user by cognitoId' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { type: 'any' },
                    },
                },
            ],
        },
    })
    @Post('/findUserByCognitoId')
    public async findUserByCognitoId(
        @Body() info: any,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.findUserByCognitoId(
                info.cognitoId,
                info.email,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('findUserByCognitoId error:', e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'find organisationUser by cognitoId' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { type: 'any' },
                    },
                },
            ],
        },
    })
    @Post('/findOrganisationUser')
    public async findOrganisationUser(
        @Body() info: any,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.findOrganisationUser(
                info.cognitoId,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error('findOrganisationUser error:', e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Change user role' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(User),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'role',
        required: true,
    })
    @Post('/role/:userId')
    public async changeUserRole(
        @Param('userId') userId: string,
        @Query('role') role: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.changeUserRole(
                req.user['cognito:username'],
                userId,
                role,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Add user role' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(User),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'role',
        required: true,
    })
    @Put('/role/:userId')
    public async addRoleToUser(
        @Param('userId') userId: string,
        @Query('role') role: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.addRoleToUser(
                {
                    groups: req.user['cognito:groups'],
                    organisation: req.user['custom:organisationId'],
                },
                userId,
                role,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Remove user role' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(User),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'role',
        required: true,
    })
    @Delete('/role/:userId')
    public async deleteRoleToUser(
        @Param('userId') userId: string,
        @Query('role') role: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.userService.removeRoleFromUser(
                userId,
                role,
                req.user['custom:organisationId'],
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update user session' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'string',
                        },
                    },
                },
            ],
        },
    })
    @Post('/session')
    public async updateUserSession(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            await this.userService.updateUserSession(
                req.user['cognito:username'],
            );
            responseOk(res, { status: 200 });
        } catch (e) {
            this.logger.error(e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
