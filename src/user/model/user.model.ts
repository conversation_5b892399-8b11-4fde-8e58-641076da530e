import { ApiProperty } from '@nestjs/swagger';

export class UserSearchDTO {
    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    name: string;

    @ApiProperty({ required: false })
    fname?: string;

    @ApiProperty({ required: false })
    sname?: string;

    @ApiProperty({ required: false })
    email?: string;

    @ApiProperty({ required: false })
    phone?: string;

    @ApiProperty({ required: false })
    type?: string;

    @ApiProperty({ required: false, type: [String] })
    types?: string[];
}

export class UserPreferencesVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    allowedNotifications: string[];
}
