import { ApiProperty } from '@nestjs/swagger';
import {
    ContactEmail,
    ContactPhone,
    PinConversation,
    User,
    UserType,
} from '../../common/model/user';
import { File } from '../../common/model/file';
import { Business } from '../../common/model/business';
import { Invitation } from '../../common/model/invitation';
import { Organisation } from '../../common/model/organisation';
import { UserImageVO } from '../../board/model/board.model';

export class UserSearchDTO {
    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    name: string;

    @ApiProperty({ required: false })
    fname?: string;

    @ApiProperty({ required: false })
    sname?: string;

    @ApiProperty({ required: false })
    email?: string;

    @ApiProperty({ required: false })
    phone?: string;

    @ApiProperty({ required: false })
    type?: string;

    @ApiProperty({ required: false, type: [String] })
    types?: string[];
}

export class UserPreferencesVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    allowedNotifications: string[];
}

export class UserBusinessVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    addressLine1: string;
    @ApiProperty()
    website: string;
    @ApiProperty()
    type: string;
    constructor(business: Business) {
        this.id = business.id;
        this.name = business.name;
        this.addressLine1 = business.addressLine1;
        this.website = business.website;
        this.type = business.type;
    }
}

export class InvitationOrganisationVO {
    @ApiProperty()
    id: string;
    constructor(organisation: Organisation) {
        this.id = organisation.id;
    }
}

export class InvitationVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    createdAt: Date;
    @ApiProperty()
    verified: boolean;
    @ApiProperty()
    organisation: InvitationOrganisationVO;
    @ApiProperty()
    email: string;
    @ApiProperty()
    inviterName: string;
    constructor(invitation: Invitation) {
        this.id = invitation.id;
        this.createdAt = invitation.createdAt;
        this.verified = invitation.verified;
        this.email = invitation.email;
        this.inviterName = invitation.inviterName;
    }
}

export class ImageVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    key: string;
    @ApiProperty()
    mimeType: string;

    constructor(file: File) {
        this.id = file.id;
        this.key = file.key;
        this.mimeType = file.mimeType;
    }
}

export class UserDetailVO {
    @ApiProperty()
    fname: string;
    @ApiProperty()
    sname: string;
    @ApiProperty()
    cognitoId: string;
    @ApiProperty()
    cognitoEmail: string;
    @ApiProperty()
    email: string;
    @ApiProperty()
    currentOrganisation: string;
    @ApiProperty()
    roles: string[];
    @ApiProperty()
    nameConfirmed: boolean;
    @ApiProperty()
    loftyAgentBaseId: string;
    @ApiProperty()
    forceSignOut: boolean;
    @ApiProperty()
    companyName: string;
    @ApiProperty()
    addressLine1: string;
    @ApiProperty()
    addressLine2: string;
    @ApiProperty()
    addressLine3: string;
    @ApiProperty()
    locality: string;
    @ApiProperty()
    region: string;
    @ApiProperty()
    socialSecurityNumber: string;
    @ApiProperty()
    employmentStatus: string;
    @ApiProperty()
    dateOfBrithday: Date;
    @ApiProperty()
    income: string;
    @ApiProperty()
    incomeFrequency: string;
    @ApiProperty()
    emails: ContactEmail[];
    @ApiProperty()
    business: UserBusinessVO; // connection
    @ApiProperty()
    image: ImageVO; // connection
    @ApiProperty()
    phones: ContactPhone[];
    @ApiProperty()
    whatsAppNumber: string;
    @ApiProperty()
    onboardingStep: number;
    @ApiProperty()
    onboardingSeen: boolean;
    @ApiProperty()
    type: UserType;
    @ApiProperty()
    title: string;
    @ApiProperty()
    id: string;
    @ApiProperty()
    pinConversations: PinConversation[];
    @ApiProperty()
    invitations: InvitationVO[]; // connection
    constructor(user: User) {
        this.fname = user.fname;
        this.sname = user.sname;
        this.cognitoId = user.cognitoId;
        this.cognitoEmail = user.cognitoEmail;
        this.email = user.email;
        this.currentOrganisation = user.currentOrganisation;
        this.roles = user.roles;
        this.nameConfirmed = user.nameConfirmed;
        this.loftyAgentBaseId = user.loftyAgentBaseId;
        this.forceSignOut = user.forceSignOut;
        this.companyName = user.companyName;
        this.addressLine1 = user.addressLine1;
        this.addressLine2 = user.addressLine2;
        this.addressLine3 = user.addressLine3;
        this.locality = user.locality;
        this.region = user.region;
        this.socialSecurityNumber = user.socialSecurityNumber;
        this.employmentStatus = user.employmentStatus;
        this.dateOfBrithday = user.dateOfBrithday;
        this.income = user.income;
        this.incomeFrequency = user.incomeFrequency;
        this.emails = user.emails;
        this.phones = user.phones;
        this.whatsAppNumber = user.whatsAppNumber;
        this.onboardingStep = user.onboardingStep;
        this.onboardingSeen = user.onboardingSeen;
        this.type = user.type;
        this.title = user.title;
        this.id = user.id;
        this.pinConversations = user.pinConversations;
    }
}

export class ApplicantReportVO {
    @ApiProperty()
    applicantName: string;
    @ApiProperty()
    lettingsNegotiator: string;
    @ApiProperty()
    propertyManager: string;
    @ApiProperty()
    leadStatus: string;
    @ApiProperty()
    lastUpdatedDate: string;
    @ApiProperty()
    viewingsArranged: number;
    @ApiProperty()
    nextViewingDate: string;
    @ApiProperty()
    date: string;
}
