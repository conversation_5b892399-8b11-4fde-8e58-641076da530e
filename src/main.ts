import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, RequestMethod } from '@nestjs/common';
import { LwExceptionsFilter } from './common/filter/lwExceptionsFilter';
import 'dotenv/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

const logger = new Logger('main', { timestamp: true });

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    app.setGlobalPrefix('main', {
        exclude: [{ path: 'health', method: RequestMethod.GET }],
    });
    // Global exception handler
    app.useGlobalFilters(new LwExceptionsFilter());
    app.use((req, res, next) => {
        res.setHeader('Cache-Control', 'no-store'); // Prevents caching
        next();
    });
    // Enable cors
    const domainEnv = process.env.CORS_DOMAINS;
    if (!!domainEnv) {
        const domains = domainEnv.split(',').map((domain) => {
            // Check if the string looks like a regex (starts with / and ends with /)
            if (domain.startsWith('/') && domain.endsWith('/')) {
                return new RegExp(domain.slice(1, -1)); // Convert to RegExp
            }
            return domain;
        });
        logger.log(`allow cors origins: ${domains}`);
        app.enableCors({
            origin: domains,
            methods: 'GET,PUT,POST,DELETE',
            allowedHeaders:
                'Content-Type, Accept, User-Agent, Origin, Authorization',
            credentials: true,
        });
    }

    // swagger
    const config = new DocumentBuilder()
        .setTitle('Loftyworks main service')
        .setDescription('APIs for Loftyworks main service')
        .setVersion('1.0')
        .addTag('Loftyworks')
        .setBasePath('main')
        .addBearerAuth({
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            description: 'Input your token',
        })
        .build();
    const documentFactory = () => SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('main/swagger', app, documentFactory);

    await app.listen(process.env.PORT ?? 3000, () => {
        logger.log(`Loftyworks main service start to run`);
    });
}
bootstrap();
