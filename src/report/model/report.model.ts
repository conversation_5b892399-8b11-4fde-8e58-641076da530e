import { ApiProperty } from '@nestjs/swagger';
import { SortBy } from 'src/common/model';
import { Period, TenancyStatus, TenancyType } from 'src/common/enum/tenancy';

export class BackendDashboardQueryDTO {
    @ApiProperty({
        required: true,
    })
    organisationId: string;

    @ApiProperty({
        required: false,
    })
    contractListType: string;

    @ApiProperty({
        required: false,
    })
    contractDaysRange: number;

    @ApiProperty({
        required: false,
    })
    docsExprDocumentType: string;

    @ApiProperty({
        required: false,
    })
    docsExprDaysRange: number;

    @ApiProperty({
        required: false,
    })
    myTaskUserId: string;

    @ApiProperty({
        required: false,
    })
    myTaskExpDaysRange: number;

    @ApiProperty({
        required: false,
    })
    onlyTasksType: string;
}

export class ContractDashboardQueryDTO {
    @ApiProperty({
        required: false,
    })
    dashboardType?: string;

    @ApiProperty({
        required: true,
    })
    organisationId: string;

    @ApiProperty({
        required: false,
    })
    tenancyType: string;

    @ApiProperty({
        required: false,
    })
    durationDays: number;

    @ApiProperty({
        required: false,
    })
    propertyId?: string;

    @ApiProperty({
        required: false,
        enum: TenancyStatus,
    })
    status: TenancyStatus;

    @ApiProperty({
        required: false,
    })
    landlordId: string;

    @ApiProperty({
        required: false,
    })
    managerId: string;

    @ApiProperty({
        required: false,
    })
    startDateFrom: string;

    @ApiProperty({
        required: false,
    })
    startDateTo: string;

    @ApiProperty({
        required: false,
    })
    endDateFrom: string;

    @ApiProperty({
        required: false,
    })
    endDateTo: string;

    @ApiProperty({
        required: false,
    })
    sortBy?: SortBy;
}

export class BackendDashboardResponseDTO {
    @ApiProperty({
        type: () => [BackendDashboardResponseDTO_ContractDTO],
    })
    contractList: BackendDashboardResponseDTO_ContractDTO[];

    @ApiProperty({
        type: () => [BackendDashboardResponseDTO_DocExpDTO],
    })
    docExpList: BackendDashboardResponseDTO_DocExpDTO[];

    @ApiProperty({
        type: () => [BackendDashboardResponseDTO_MyTaskDTO],
    })
    myTaskList: BackendDashboardResponseDTO_MyTaskDTO[];

    @ApiProperty({
        type: () => [BackendDashboardResponseDTO_TaskDTO],
    })
    taskList: BackendDashboardResponseDTO_TaskDTO[];
}

class BackendDashboardResponseDTO_ContractDTO {
    @ApiProperty()
    date: Date;

    @ApiProperty()
    contract: string;

    @ApiProperty()
    property: string;

    @ApiProperty()
    propertyAddress: string;
}

class BackendDashboardResponseDTO_DocExpDTO {
    @ApiProperty()
    date: Date;

    @ApiProperty()
    propertyId: string;

    @ApiProperty()
    propertyName: string;
}

class BackendDashboardResponseDTO_MyTaskDTO {
    @ApiProperty()
    date: Date;

    @ApiProperty()
    taskId: string;

    @ApiProperty()
    taskName: string;

    @ApiProperty()
    taskBoardId: string;
}

class BackendDashboardResponseDTO_TaskDTO {
    @ApiProperty()
    archived: number;

    @ApiProperty()
    deleted: number;
}

export class ContractDashboardDefaultResponseDTO {
    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    starting: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    noticeGiven: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    periodic: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    renewing: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    draft: ContractDashboardResponseDTO_TenancyDTO[];
}

export class ContractDashboardSubSectionResponseDTO {
    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    archive: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    active: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    noticeGiven: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    periodic: ContractDashboardResponseDTO_TenancyDTO[];

    @ApiProperty({
        type: () => [ContractDashboardResponseDTO_TenancyDTO],
    })
    renewing: ContractDashboardResponseDTO_TenancyDTO[];
}

class ContractDashboardResponseDTO_TenancyDTO {
    @ApiProperty()
    tenancyId: string;

    @ApiProperty()
    daysLeft: number;

    @ApiProperty()
    deadline: Date;

    @ApiProperty()
    type: TenancyType;

    @ApiProperty()
    primaryTenant: string;

    @ApiProperty()
    amount: number;

    @ApiProperty()
    period: Period;

    @ApiProperty()
    startDate: Date;

    @ApiProperty()
    endDate: Date;

    @ApiProperty()
    status: TenancyStatus;

    @ApiProperty()
    propertyAddress: string;

    @ApiProperty()
    tenancyPropertyId: string;

    @ApiProperty()
    reference: string;
}

export class DashboardTenancyWidgetResponseDTO {
    @ApiProperty()
    tenancyStartDateNotificationDays: number;

    @ApiProperty()
    tenancyEndDateNotificationDays: number;

    @ApiProperty()
    tenancyRenewalNotificationDays: number;

    @ApiProperty()
    upcomingScheduledMoveIns: number;

    @ApiProperty()
    upcomingScheduledRenewals: number;

    @ApiProperty()
    upcomingVacatingTenancies: number;
}

export class DocumentCertificationWidgetResponseDTO {
    @ApiProperty()
    expiredEPC: number;

    @ApiProperty()
    expiredGasSafety: number;

    @ApiProperty()
    expiredOther: number;

    @ApiProperty({
        type: () => [
            DocumentCertificationWidgetResponseDTO_UpcomingExpirationsDTO,
        ],
    })
    upcomingExpirations: [];
}

class DocumentCertificationWidgetResponseDTO_UpcomingExpirationsDTO {
    @ApiProperty()
    documentType: string;

    @ApiProperty()
    expires: Date;

    @ApiProperty()
    propertyAddress: string;
}
