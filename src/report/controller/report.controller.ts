import { Response } from 'express';
import {
    Body,
    Controller,
    Get,
    HttpStatus,
    Logger,
    Post,
    Query,
    Res,
} from '@nestjs/common';
import {
    ApiOperation,
    ApiOkResponse,
    ApiQuery,
    getSchemaPath,
    ApiExtraModels,
    ApiBody,
} from '@nestjs/swagger';

import { ResponseCode } from 'src/common/constant/responseCode';
import {
    BaseRes,
    responseError,
    responseOk,
} from 'src/common/util/requestUtil';

import { SortBy } from 'src/common/model';
import { TenancyStatus } from 'src/common/enum/tenancy';

import {
    BackendDashboardQueryDTO,
    ContractDashboardQueryDTO,
    BackendDashboardResponseDTO,
    ContractDashboardDefaultResponseDTO,
    ContractDashboardSubSectionResponseDTO,
    DashboardTenancyWidgetResponseDTO,
    DocumentCertificationWidgetResponseDTO,
} from '../model/report.model';

import { ReportService } from '../service/report.service';

@ApiExtraModels(
    BackendDashboardQueryDTO,
    ContractDashboardQueryDTO,
    BackendDashboardResponseDTO,
    ContractDashboardDefaultResponseDTO,
    ContractDashboardSubSectionResponseDTO,
    DashboardTenancyWidgetResponseDTO,
    DocumentCertificationWidgetResponseDTO,
)
@Controller('api/v1/report')
export class ReportController {
    private logger = new Logger('ReportController', { timestamp: true });

    constructor(private reportService: ReportService) {}

    @ApiOperation({ summary: 'Get Backend Dashboard' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(BackendDashboardResponseDTO),
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            $ref: getSchemaPath(BackendDashboardQueryDTO),
        },
    })
    @Post('/getBackendDashboard')
    public async getBackendDashboard(
        @Body('organisationId') organisationId: string,
        @Body('contractListType') contractListType: string,
        @Body('contractDaysRange') contractDaysRange: number,
        @Body('docsExprDocumentType') docsExprDocumentType: string,
        @Body('docsExprDaysRange') docsExprDaysRange: number,
        @Body('myTaskUserId') myTaskUserId: string,
        @Body('myTaskExpDaysRange') myTaskExpDaysRange: number,
        @Body('onlyTasksType') onlyTasksType: string,
        @Res() response: Response,
    ) {
        if (!organisationId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.reportService.getBackendDashboard({
                organisationId,
                contractListType,
                contractDaysRange,
                docsExprDocumentType,
                docsExprDaysRange,
                myTaskUserId,
                myTaskExpDaysRange,
                onlyTasksType,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Contract Dashboard' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            oneOf: [
                                {
                                    $ref: getSchemaPath(
                                        ContractDashboardDefaultResponseDTO,
                                    ),
                                },
                                {
                                    $ref: getSchemaPath(
                                        ContractDashboardSubSectionResponseDTO,
                                    ),
                                },
                            ],
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        schema: {
            $ref: getSchemaPath(ContractDashboardQueryDTO),
        },
    })
    @Post('/getContractDashboard')
    public async getContractDashboard(
        @Body('dashboardType') dashboardType: string,
        @Body('organisationId') organisationId: string,
        @Body('tenancyType') tenancyType: string,
        @Body('durationDays') durationDays: number,
        @Body('propertyId') propertyId: string,
        @Body('landlordId') landlordId: string,
        @Body('managerId') managerId: string,
        @Body('status') status: TenancyStatus,
        @Body('startDateFrom') startDateFrom: string,
        @Body('startDateTo') startDateTo: string,
        @Body('endDateFrom') endDateFrom: string,
        @Body('endDateTo') endDateTo: string,
        @Body('sortBy') sortBy: SortBy,
        @Res() response: Response,
    ) {
        if (!organisationId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data = await this.reportService.getContractDashboard({
                dashboardType,
                organisationId,
                tenancyType,
                durationDays,
                propertyId,
                landlordId,
                managerId,
                status,
                startDateFrom,
                startDateTo,
                endDateFrom,
                endDateTo,
                sortBy,
            });
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Dashboard Tenancy Widget' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(
                                DashboardTenancyWidgetResponseDTO,
                            ),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'organisationId',
        description: 'Organisation ID',
    })
    @Get('/getDashboardTenancyWidget')
    public async getDashboardTenancyWidget(
        @Query('organisationId') organisationId: string,
        @Res() response: Response,
    ) {
        if (!organisationId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data =
                await this.reportService.getDashboardTenancyWidget(
                    organisationId,
                );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }

    @ApiOperation({ summary: 'Get Document Certification Widget' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(
                                DocumentCertificationWidgetResponseDTO,
                            ),
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'organisationId',
        description: 'Organisation ID',
    })
    @Get('/getDocumentCertificationWidget')
    public async getDocumentCertificationWidget(
        @Query('organisationId') organisationId: string,
        @Res() response: Response,
    ) {
        if (!organisationId) {
            responseError(
                HttpStatus.BAD_REQUEST,
                response,
                ResponseCode.PARAM_INVALID,
            );
            return;
        }

        try {
            const data =
                await this.reportService.getDocumentCertificationWidget(
                    organisationId,
                );
            responseOk(response, data);
        } catch (e) {
            this.logger.error(e.stack);
            responseError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                response,
                new ResponseCode(
                    ResponseCode.SERVER_ERROR.code,
                    e.message || ResponseCode.SERVER_ERROR.message,
                ),
            );
        }
    }
}
