import { Injectable, Logger } from '@nestjs/common';

import { Document } from '@prisma/client';

import { DocumentService } from 'src/document/service/document.service';

import { OrganisationUserRepository } from 'src/common/repository/organisationUser';
import { PropertyRepository } from 'src/common/repository/property';
import { ReminderRepository } from 'src/common/repository/reminder';
import { TaskRepository } from 'src/common/repository/task';
import { TenancyRepository } from 'src/common/repository/tenancy';
import { UserRepository } from 'src/common/repository/user';

import { SortByDirection } from 'src/common/enum';
import { TaskStatus } from 'src/common/enum/task';
import { TenancyStatus } from 'src/common/enum/tenancy';

import { Task } from 'src/common/model/task';
import { Tenancy } from 'src/common/model/tenancy';

import { TenancyDashboardSubSectionType } from '../enum/report.enum';

import {
    BackendDashboardQueryDTO,
    ContractDashboardQueryDTO,
} from '../model/report.model';

import {
    buildContractDashboardSubSectionObject,
    filterContractDashboardSubSection,
    baseComparator,
    numberComparator,
    checkIfTenancyIsRelevant,
} from '../util/report.util';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const lodash = require('lodash');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const moment = require('moment');

@Injectable()
export class ReportService {
    private logger = new Logger('ReportService', { timestamp: true });

    constructor(
        private documentService: DocumentService,
        private organisationUserRepository: OrganisationUserRepository,
        private propertyRepository: PropertyRepository,
        private reminderRepository: ReminderRepository,
        private taskRepository: TaskRepository,
        private tenancyRepository: TenancyRepository,
        private userRepository: UserRepository,
    ) {}

    public async getBackendDashboard({
        organisationId,
        contractListType,
        contractDaysRange,
        docsExprDocumentType,
        docsExprDaysRange,
        myTaskUserId,
        myTaskExpDaysRange,
        onlyTasksType,
    }: BackendDashboardQueryDTO) {
        this.logger.log(
            JSON.stringify({
                organisationId,
                contractListType,
                contractDaysRange,
                docsExprDocumentType,
                docsExprDaysRange,
                myTaskUserId,
                myTaskExpDaysRange,
                onlyTasksType,
            }),
        );

        let contractList = [];
        let docExpList = [];
        let myTaskList = [];
        let taskList = [];

        if (contractListType && contractDaysRange) {
            contractList = await this.getContractList(
                organisationId,
                contractListType,
                contractDaysRange,
            );
        }

        if (docsExprDaysRange) {
            docExpList = await this.getDocExprList(
                organisationId,
                docsExprDocumentType,
                docsExprDaysRange,
            );
        }

        if (myTaskUserId && myTaskExpDaysRange) {
            myTaskList = await this.getMyTaskList(
                organisationId,
                myTaskUserId,
                myTaskExpDaysRange,
            );
        }

        if (onlyTasksType) {
            taskList = await this.getTaskList(organisationId, onlyTasksType);
        }

        return {
            contractList,
            docExpList,
            myTaskList,
            taskList,
        };
    }

    private async getContractList(
        organisationId: string,
        contractListType: string,
        contractDaysRange: number,
    ) {
        const startDate = moment().utc();
        const endDate = startDate.add(contractDaysRange, 'days');

        let contractList: any[] = [];

        const properties =
            await this.propertyRepository.findByOrganisationId(organisationId);

        const tenancies =
            await this.tenancyRepository.findByOrganisationId(organisationId);

        if (contractListType === 'starting') {
            contractList = tenancies
                ?.filter((tenancy) => {
                    const { tenancyPropertyId, startDate: tenancyStartDate } =
                        tenancy;

                    if (
                        tenancyPropertyId &&
                        tenancyStartDate &&
                        moment(tenancyStartDate).utc().isAfter(startDate) &&
                        moment(tenancyStartDate).utc().isBefore(endDate)
                    ) {
                    }
                })
                ?.reduce((prev, next: Tenancy) => {
                    const { id, tenancyPropertyId, startDate } = next;

                    const property = properties.find(
                        (property) => property.id === tenancyPropertyId,
                    );

                    prev = [
                        ...prev,
                        {
                            date: startDate,
                            contract: id,
                            property: tenancyPropertyId,
                            propertyAddress: property?.addressLine1,
                        },
                    ];

                    return prev;
                }, []);
        } else if (contractListType === 'ending') {
            contractList = tenancies
                ?.filter((tenancy) => {
                    const { tenancyPropertyId, endDate: tenancyEndDate } =
                        tenancy;

                    if (
                        tenancyPropertyId &&
                        tenancyEndDate &&
                        moment(tenancyEndDate).utc().isAfter(startDate) &&
                        moment(tenancyEndDate).utc().isBefore(endDate)
                    ) {
                    }
                })
                ?.reduce((prev, next) => {
                    const { id, tenancyPropertyId, endDate } = next;

                    const property = properties.find(
                        (property) => property.id === tenancyPropertyId,
                    );

                    prev = [
                        ...prev,
                        {
                            date: endDate,
                            contract: id,
                            property: tenancyPropertyId,
                            propertyAddress: property?.addressLine1,
                        },
                    ];

                    return prev;
                }, []);
        }

        return contractList;
    }

    private async getDocExprList(
        organisationId: string,
        docsExprDocumentType: string,
        docsExprDaysRange: number,
    ) {
        const startDate = moment().utc();
        const endDate = startDate.add(docsExprDaysRange, 'days');

        const documents = await this.documentService.getOrganisationDocuments(
            organisationId,
            'true',
        );

        const docExprList = documents?.reduce((prev, next: Document) => {
            const {
                type,
                expiry,
                documentPropertyId,
                name: documentPropertyName,
            } = next;

            if (
                type &&
                (!docsExprDocumentType ||
                    docsExprDocumentType === 'ALL_TYPES' ||
                    type === docsExprDocumentType) &&
                expiry &&
                moment(expiry).utc().isAfter(startDate) &&
                moment(expiry).utc().isBefore(endDate)
            ) {
                prev = [
                    ...prev,
                    {
                        date: expiry,
                        propertyId: documentPropertyId,
                        propertyName: documentPropertyName,
                    },
                ];
            }

            return prev;
        }, []);

        return docExprList;
    }

    private async getMyTaskList(
        organisationId: string,
        myTaskUserId: string,
        myTaskExpDaysRange: number,
    ) {
        const startDate = moment().utc();
        const endDate = startDate.add(myTaskExpDaysRange, 'days');

        const tasks =
            await this.taskRepository.findByOrganisationIdWithDeadline(
                organisationId,
            );

        const myTaskList = tasks?.reduce((prev, next: Task) => {
            const { id, name, deadline, taskBoardId, taskUserId } = next;

            if (
                deadline &&
                moment(deadline).utc().isAfter(startDate) &&
                moment(deadline).utc().isBefore(endDate) &&
                taskUserId &&
                taskUserId === myTaskUserId
            ) {
                prev = [
                    ...prev,
                    {
                        date: deadline,
                        taskId: id,
                        taskName: name,
                        taskBoardId,
                    },
                ];
            }

            return prev;
        }, []);

        return myTaskList;
    }

    private async getTaskList(organisationId: string, onlyTasksType: string) {
        let archived = 0;
        let deleted = 0;

        const tasks = (
            await this.taskRepository.findByOrganisationIdWithStatus(
                organisationId,
            )
        ).filter((task) => task.taskLabelId === onlyTasksType);

        for (const task of tasks) {
            const { status } = task;

            if (status === TaskStatus.ARCHIVED) {
                archived++;
            } else if (status === TaskStatus.DELETED) {
                deleted++;
            }
        }

        return [
            {
                archived,
                deleted,
            },
        ];
    }

    public async getContractDashboard({
        dashboardType,
        organisationId,
        tenancyType,
        durationDays,
        propertyId,
        landlordId,
        managerId,
        status,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        sortBy,
    }: ContractDashboardQueryDTO) {
        this.logger.log(
            JSON.stringify({
                dashboardType,
                organisationId,
                tenancyType,
                durationDays,
                propertyId,
                landlordId,
                managerId,
                status,
                startDateFrom,
                startDateTo,
                endDateFrom,
                endDateTo,
                sortBy,
            }),
        );

        if (dashboardType) {
            return await this.getContractDashboardSubSection({
                dashboardType,
                organisationId,
                tenancyType,
                managerId,
                landlordId,
                status,
                durationDays,
                startDateFrom,
                startDateTo,
                endDateFrom,
                endDateTo,
            });
        } else {
            return await this.getContractDashboardDefault({
                organisationId,
                tenancyType,
                durationDays,
                propertyId,
                landlordId,
                managerId,
                status,
                startDateFrom,
                startDateTo,
                endDateFrom,
                endDateTo,
                sortBy,
            });
        }
    }

    private async getContractDashboardSubSection({
        dashboardType,
        organisationId,
        tenancyType,
        managerId,
        landlordId,
        status,
        durationDays,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
    }: ContractDashboardQueryDTO) {
        const propertiesByManagerId = {};

        const userIds = (
            await this.organisationUserRepository.findByOrganisationId(
                organisationId,
            )
        ).map((user) => user.organisationUserUserId);

        const users = await this.userRepository.findByIds(userIds);

        const usersGroupById = lodash.groupBy(users, 'id');

        let tenancies = (
            await this.tenancyRepository.findByOrganisationId(organisationId)
        ).filter((tenancy) => {
            const { startDate, endDate } = tenancy;

            return (
                (!startDateFrom ||
                    (startDate &&
                        moment(startDate)
                            .utc()
                            .isAfter(moment(startDateFrom).utc()))) &&
                (!startDateTo ||
                    (startDate &&
                        moment(startDate)
                            .utc()
                            .isBefore(moment(startDateTo).utc()))) &&
                (!endDateFrom ||
                    (endDate &&
                        moment(endDate)
                            .utc()
                            .isAfter(moment(endDateFrom).utc()))) &&
                (!endDateTo ||
                    (endDate &&
                        moment(endDate)
                            .utc()
                            .isBefore(moment(endDateTo).utc())))
            );
        });

        if (tenancyType) {
            tenancies = tenancies.filter(
                (tenancy) => tenancy.type === tenancyType,
            );
        }

        if (landlordId) {
            tenancies = tenancies.filter((tenancy) =>
                tenancy.landlords?.includes(landlordId),
            );
        }

        if (managerId) {
            const properties =
                await this.propertyRepository.findByOrganisationId(
                    organisationId,
                );

            for (const property of properties) {
                if (property.managers?.includes(managerId)) {
                    propertiesByManagerId[property.id] = managerId;
                }
            }

            tenancies = tenancies.filter(
                (tenancy) =>
                    tenancy.tenancyPropertyId &&
                    propertiesByManagerId[tenancy.tenancyPropertyId],
            );
        }

        if (dashboardType === TenancyDashboardSubSectionType.ARCHIVED) {
            const archivedTenancies = tenancies
                .filter((tenancy) => tenancy.status === TenancyStatus.ARCHIVED)
                .map((tenancy) =>
                    buildContractDashboardSubSectionObject(
                        tenancy,
                        usersGroupById,
                    ),
                );

            return {
                archive: archivedTenancies,
            };
        } else {
            const activeTenancies = tenancies
                .filter(
                    (tenancy) =>
                        tenancy.status === TenancyStatus.ACTIVE &&
                        (!status || status === tenancy.status),
                )
                .map((tenancy) =>
                    buildContractDashboardSubSectionObject(
                        tenancy,
                        usersGroupById,
                    ),
                );

            const noticeGivenTenancies = tenancies
                .filter(
                    (tenancy) =>
                        tenancy.status === TenancyStatus.NOTICE_GIVEN &&
                        (!status || status === tenancy.status),
                )
                .map((tenancy) =>
                    buildContractDashboardSubSectionObject(
                        tenancy,
                        usersGroupById,
                    ),
                );

            const renewingTenancies = tenancies
                .filter(
                    (tenancy) =>
                        tenancy.status === TenancyStatus.RENEWED &&
                        (!status || status === tenancy.status),
                )
                .map((tenancy) =>
                    buildContractDashboardSubSectionObject(
                        tenancy,
                        usersGroupById,
                    ),
                );

            const periodicTenancies = tenancies
                .filter(
                    (tenancy) =>
                        tenancy.status === TenancyStatus.PERIODIC &&
                        (!status || status === tenancy.status),
                )
                .map((tenancy) =>
                    buildContractDashboardSubSectionObject(
                        tenancy,
                        usersGroupById,
                    ),
                );

            return filterContractDashboardSubSection(
                {
                    activeTenancies,
                    noticeGivenTenancies,
                    periodicTenancies,
                    renewingTenancies,
                },
                durationDays,
            );
        }
    }

    private async getContractDashboardDefault({
        organisationId,
        tenancyType,
        propertyId,
        durationDays,
        landlordId,
        managerId,
        status,
        startDateFrom,
        startDateTo,
        endDateFrom,
        endDateTo,
        sortBy,
    }: ContractDashboardQueryDTO) {
        const starting = [];
        const noticeGiven = [];
        const periodic = [];
        const renewing = [];
        const draft = [];

        let tenancies = (
            await this.tenancyRepository.findByOrganisationId(organisationId)
        ).filter((tenancy) => {
            const { startDate, endDate } = tenancy;

            return (
                (!startDateFrom ||
                    (startDate &&
                        moment(startDate)
                            .utc()
                            .isAfter(moment(startDateFrom).utc()))) &&
                (!startDateTo ||
                    (startDate &&
                        moment(startDate)
                            .utc()
                            .isBefore(moment(startDateTo).utc()))) &&
                (!endDateFrom ||
                    (endDate &&
                        moment(endDate)
                            .utc()
                            .isAfter(moment(endDateFrom).utc()))) &&
                (!endDateTo ||
                    (endDate &&
                        moment(endDate)
                            .utc()
                            .isBefore(moment(endDateTo).utc())))
            );
        });

        if (sortBy) {
            let sortField: string;

            let comparatorFunction: any = baseComparator;

            switch (sortBy.field) {
                case 'REFERENCE':
                    sortField = 'reference';
                    break;
                case 'PROPERTY':
                    sortField = 'address';
                    break;
                case 'TYPE':
                    sortField = 'type';
                    break;
                case 'PRIMARY_TENANT':
                    sortField = 'primaryTenantName';
                    break;
                case 'AMOUNT':
                    sortField = 'rent';
                    comparatorFunction = numberComparator;
                    break;
                case 'REMAINS':
                    sortField = 'daysRemaining';
                    comparatorFunction = numberComparator;
                    break;
                case 'ARREARS':
                    comparatorFunction = numberComparator;
                    sortField = 'arrears';
                    break;
                case 'STATUS':
                    sortField = 'status';
                    break;
                case 'START':
                    sortField = 'startDate';
                    break;
                case 'END':
                    sortField = 'endDate';
                    break;
                case 'PERIOD':
                    sortField = 'period';
                    break;
                default:
                    throw new Error('Unknown sortField ' + sortBy.field);
            }

            tenancies = tenancies.sort((tenancy1, tenancy2) =>
                comparatorFunction(tenancy1[sortField], tenancy2[sortField]),
            );

            if (sortBy.direction === SortByDirection.DESC) {
                tenancies.reverse();
            }
        }

        const properties =
            await this.propertyRepository.findByOrganisationId(organisationId);

        const userIds = (
            await this.organisationUserRepository.findByOrganisationId(
                organisationId,
            )
        ).map((user) => user.organisationUserUserId);

        const users = await this.userRepository.findByIds(userIds);

        const durationStartDate = moment().utc();
        const durationEndDate = durationDays
            ? durationStartDate.add(durationDays, 'days')
            : moment(9999 * 365 * 24 * 60 * 60 * 1000).utc();

        for (const tenancy of tenancies) {
            const tenancyPropertyId = tenancy.tenancyPropertyId;
            const propertyAddress = tenancy.address;
            const tenancyStatus = tenancy.status;
            const type = tenancy.type;
            const period = tenancy.period;
            const amount = tenancy.rent + '';
            const tenancyId = tenancy.id;
            const reference = tenancy.reference;
            const startDate = tenancy.startDate;
            const endDate = tenancy.endDate;
            const renewalDate = tenancy.renewalDate;
            const rentReviewDate = tenancy.rentReviewDate;
            const tenancyLandlords = tenancy.landlords || [];
            const tenancyManagers =
                properties.filter(
                    (property) => property.id === tenancyPropertyId,
                )?.[0]?.managers || [];

            let primaryTenant: string;

            if (tenancy.primaryTenant) {
                const user = users.find(
                    (user) => user.id === tenancy.primaryTenant,
                );
                if (user?.companyName) {
                    primaryTenant = user.companyName;
                } else if (user?.fname) {
                    primaryTenant = user.fname.concat(user.sname);
                }
            }

            if (!tenancyType || tenancyType == type) {
                if (
                    startDate &&
                    moment(startDate).utc().isAfter(durationStartDate) &&
                    moment(startDate).utc().isBefore(durationEndDate) &&
                    status !== TenancyStatus.DRAFT
                ) {
                    if (
                        checkIfTenancyIsRelevant(
                            propertyId,
                            landlordId,
                            managerId,
                            status,

                            tenancyPropertyId,
                            tenancyLandlords,
                            tenancyManagers,
                            tenancyStatus,
                        )
                    ) {
                        starting.push({
                            tenancyId,
                            startDate,
                            endDate,
                            tenancyPropertyId,
                            daysLeft: moment(startDate)
                                .utc()
                                .diff(durationStartDate, 'days'),
                            deadline: startDate,
                            type,
                            primaryTenant,
                            amount,
                            period,
                            status: tenancyStatus,
                            propertyAddress,
                            reference,
                        });
                    }
                }

                if (
                    endDate &&
                    moment(endDate).utc().isAfter(durationStartDate) &&
                    moment(endDate).utc().isBefore(durationEndDate) &&
                    status !== TenancyStatus.DRAFT
                ) {
                    if (
                        checkIfTenancyIsRelevant(
                            propertyId,
                            landlordId,
                            managerId,
                            status,
                            tenancyPropertyId,
                            tenancyLandlords,
                            tenancyManagers,
                            tenancyStatus,
                        )
                    ) {
                        noticeGiven.push({
                            tenancyId,
                            startDate,
                            endDate,
                            tenancyPropertyId,
                            daysLeft: moment(endDate)
                                .utc()
                                .diff(durationStartDate, 'days'),
                            deadline: endDate,
                            type,
                            primaryTenant,
                            amount,
                            period,
                            status: tenancyStatus,
                            propertyAddress,
                            reference,
                        });
                    }
                }

                if (
                    renewalDate &&
                    moment(renewalDate).utc().isAfter(durationStartDate) &&
                    moment(renewalDate).utc().isBefore(durationEndDate) &&
                    tenancy.status !== TenancyStatus.DRAFT
                ) {
                    if (
                        checkIfTenancyIsRelevant(
                            propertyId,
                            landlordId,
                            managerId,
                            status,
                            tenancyPropertyId,
                            tenancyLandlords,
                            tenancyManagers,
                            tenancyStatus,
                        )
                    ) {
                        renewing.push({
                            tenancyId,
                            tenancyPropertyId,
                            startDate,
                            endDate,
                            daysLeft: moment(renewalDate)
                                .utc()
                                .diff(durationStartDate, 'days'),
                            deadline: renewalDate,
                            type,
                            primaryTenant,
                            amount,
                            period,
                            status: tenancyStatus,
                            propertyAddress,
                            reference,
                        });
                    }
                }

                if (endDate && status === TenancyStatus.PERIODIC) {
                    if (
                        checkIfTenancyIsRelevant(
                            propertyId,
                            landlordId,
                            managerId,
                            status,
                            tenancyPropertyId,
                            tenancyLandlords,
                            tenancyManagers,
                            tenancyStatus,
                        )
                    ) {
                        periodic.push({
                            tenancyId,
                            startDate,
                            endDate,
                            tenancyPropertyId,
                            daysLeft: moment(endDate)
                                .utc()
                                .diff(durationStartDate, 'days'),
                            deadline: endDate,
                            type,
                            primaryTenant,
                            amount,
                            period,
                            status: tenancyStatus,
                            propertyAddress,
                            reference,
                        });
                    }
                }

                if (endDate && status === TenancyStatus.DRAFT) {
                    if (
                        checkIfTenancyIsRelevant(
                            propertyId,
                            landlordId,
                            managerId,
                            status,
                            tenancyPropertyId,
                            tenancyLandlords,
                            tenancyManagers,
                            tenancyStatus,
                        )
                    ) {
                        draft.push({
                            tenancyId,
                            tenancyPropertyId,
                            startDate,
                            endDate,
                            daysLeft: moment(endDate)
                                .utc()
                                .diff(durationStartDate, 'days'),
                            deadline: rentReviewDate,
                            type,
                            primaryTenant,
                            amount,
                            period,
                            status: tenancyStatus,
                            propertyAddress,
                            reference,
                        });
                    }
                }
            }
        }

        return {
            starting,
            noticeGiven,
            periodic,
            renewing,
            draft,
        };
    }

    public async getDashboardTenancyWidget(organisationId: string) {
        const [reminders, tenancies] = await Promise.all([
            this.reminderRepository.findByOrganisationId(organisationId),
            this.tenancyRepository.findByOrganisationId(organisationId),
        ]);

        const {
            notifyStartDate,
            notifyEndDate,
            notifyTenancyRenewal,
            startDateNotificationDays,
            endDateNotificationDays,
            tenancyRenewalNotificationDays: renewalNotificationDays,
        } = reminders?.[0] || {};

        let tenancyStartDateNotificationDays: number;
        let tenancyEndDateNotificationDays: number;
        let tenancyRenewalNotificationDays: number;

        if (notifyStartDate && startDateNotificationDays?.length > 0) {
            tenancyStartDateNotificationDays = startDateNotificationDays[0];
        }

        if (notifyEndDate && endDateNotificationDays?.length > 0) {
            tenancyEndDateNotificationDays = endDateNotificationDays[0];
        }

        if (notifyTenancyRenewal && renewalNotificationDays?.length > 0) {
            tenancyRenewalNotificationDays = renewalNotificationDays[0];
        }

        const upcomingScheduledMoveIns = tenancies.filter((tenancy) => {
            const { startDate } = tenancy;

            if (!tenancyStartDateNotificationDays || !startDate) {
                return false;
            }

            return (
                moment(startDate).utc().isAfter(moment().utc()) &&
                moment(startDate)
                    .utc()
                    .isBefore(
                        moment()
                            .utc()
                            .add(tenancyStartDateNotificationDays, 'days'),
                    )
            );
        });

        const upcomingVacatingTenancies = tenancies.filter((tenancy) => {
            const { endDate } = tenancy;

            if (!tenancyEndDateNotificationDays || !endDate) {
                return false;
            }

            return (
                moment(endDate).utc().isAfter(moment().utc()) &&
                moment(endDate)
                    .utc()
                    .isBefore(
                        moment()
                            .utc()
                            .add(tenancyEndDateNotificationDays, 'days'),
                    )
            );
        });

        const upcomingScheduledRenewals = tenancies.filter((tenancy) => {
            const { renewalDate } = tenancy;

            if (!tenancyRenewalNotificationDays || !renewalDate) {
                return false;
            }

            return (
                moment(renewalDate).utc().isAfter(moment().utc()) &&
                moment(renewalDate)
                    .utc()
                    .isBefore(
                        moment()
                            .utc()
                            .add(tenancyRenewalNotificationDays, 'days'),
                    )
            );
        });

        this.logger.log(
            JSON.stringify({
                tenancyStartDateNotificationDays,
                tenancyEndDateNotificationDays,
                tenancyRenewalNotificationDays,
                upcomingScheduledMoveIns: upcomingScheduledMoveIns.length,
                upcomingScheduledRenewals: upcomingScheduledRenewals.length,
                upcomingVacatingTenancies: upcomingVacatingTenancies.length,
            }),
        );

        return {
            tenancyStartDateNotificationDays,
            tenancyEndDateNotificationDays,
            tenancyRenewalNotificationDays,
            upcomingScheduledMoveIns: upcomingScheduledMoveIns.length,
            upcomingScheduledRenewals: upcomingScheduledRenewals.length,
            upcomingVacatingTenancies: upcomingVacatingTenancies.length,
        };
    }

    public async getDocumentCertificationWidget(organisationId: string) {
        const documents =
            await this.documentService.getOrganisationDocuments(organisationId);

        const expiredEPC = documents.filter((document) => {
            const { expiry, type } = document;

            return (
                expiry &&
                moment(expiry).utc().isBefore(moment().utc()) &&
                type === 'EPC'
            );
        }).length;

        const expiredGasSafety = documents.filter((document) => {
            const { expiry, type } = document;

            return (
                expiry &&
                moment(expiry).utc().isBefore(moment().utc()) &&
                type === 'GAS_SAFETY_CERTIFICATE'
            );
        }).length;

        const expiredOther = documents.filter((document) => {
            const { expiry, type } = document;

            return (
                expiry &&
                moment(expiry).utc().isBefore(moment().utc()) &&
                type === 'OTHER'
            );
        }).length;

        const unexpiredDocuments = documents
            .filter((document) => {
                const { expiry } = document;

                return expiry && moment(expiry).utc().isAfter(moment().utc());
            })
            .sort(
                (d1, d2) =>
                    moment(d1.expiry).utc().millisecond() -
                    moment(d2.expiry).utc().millisecond(),
            )
            .slice(0, 5);

        this.logger.log(JSON.stringify(unexpiredDocuments));

        const upcomingExpirations = (
            await Promise.all(
                unexpiredDocuments.map(async (document) => {
                    const {
                        type,
                        expiry,
                        documentPropertyId,
                        documentTenancyId,
                    } = document;

                    const response: any = {
                        documentType: type,
                        expires: expiry,
                    };

                    let propertyId = documentPropertyId;

                    if (!propertyId && documentTenancyId) {
                        const tenancy =
                            await this.tenancyRepository.findById(
                                documentTenancyId,
                            );
                        propertyId = tenancy.tenancyPropertyId;
                    }

                    if (propertyId) {
                        const property =
                            await this.propertyRepository.findById(propertyId);

                        if (property) {
                            response.propertyAddress = [
                                property.addressLine1,
                                property.city,
                            ]
                                .filter(Boolean)
                                .join(' ');
                        }
                    }

                    return response;
                }),
            )
        ).filter(
            (upcomingExpiration) =>
                upcomingExpiration && upcomingExpiration.documentType,
        );

        this.logger.log(
            JSON.stringify({
                expiredEPC,
                expiredGasSafety,
                expiredOther,
                upcomingExpirations,
            }),
        );

        return {
            expiredEPC,
            expiredGasSafety,
            expiredOther,
            upcomingExpirations,
        };
    }
}
