import { Module } from '@nestjs/common';

import { ReportController } from './controller/report.controller';

import { EventsEmitterService } from 'src/common/service/eventsEmitter.service';
import { DocumentService } from 'src/document/service/document.service';
import { ReportService } from './service/report.service';

import { OrganisationUserRepository } from 'src/common/repository/organisationUser';
import { PropertyRepository } from 'src/common/repository/property';
import { ReminderRepository } from 'src/common/repository/reminder';
import { TaskRepository } from 'src/common/repository/task';
import { TenancyRepository } from 'src/common/repository/tenancy';
import { UserRepository } from 'src/common/repository/user';

@Module({
    controllers: [ReportController],
    providers: [
        EventsEmitterService,
        DocumentService,
        ReportService,
        OrganisationUserRepository,
        PropertyRepository,
        ReminderRepository,
        TaskRepository,
        TenancyRepository,
        UserRepository,
    ],
    exports: [],
})
export class ReportModule {}
