import { TenancyStatus } from 'src/common/enum/tenancy';

import { Tenancy } from 'src/common/model/tenancy';
import { User } from 'src/common/model/user';

// eslint-disable-next-line @typescript-eslint/no-require-imports
const moment = require('moment');

export function buildContractDashboardSubSectionObject(
    tenancy: Tenancy,
    usersGroupById: Record<string, User[]>,
) {
    const deadline =
        tenancy.status === TenancyStatus.RENEWED
            ? tenancy.renewalDate
            : tenancy.endDate;

    let primaryTenant = null;

    if (tenancy.primaryTenant) {
        const {
            companyName,
            fname = '',
            sname = '',
            title = '',
        } = usersGroupById[tenancy.primaryTenant]?.[0] || {};

        primaryTenant =
            companyName || [fname, title, sname].filter((s) => s).join(' ');
    }

    return {
        tenancyId: tenancy.id,
        daysLeft: 0,
        deadline,
        type: tenancy.type,
        primaryTenant,
        amount: tenancy.rent,
        period: tenancy.period,
        startDate: tenancy.startDate,
        endDate: tenancy.endDate,
        status: tenancy.status,
        propertyAddress: tenancy.address,
        tenancyPropertyId: tenancy.tenancyPropertyId,
        reference: tenancy.reference,
    };
}

export function filterContractDashboardSubSection(
    {
        activeTenancies,
        noticeGivenTenancies,
        periodicTenancies,
        renewingTenancies,
    },
    durationDays: number,
) {
    if (durationDays) {
        const durationStartDate = moment().utc();
        const durationEndDate = durationStartDate.add(10, 'days');

        const active = activeTenancies.filter((tenancy) => {
            const { endDate } = tenancy;

            return (
                endDate &&
                moment(endDate).utc().isAfter(durationStartDate) &&
                moment(endDate).utc().isBefore(durationEndDate)
            );
        });

        const noticeGiven = noticeGivenTenancies.filter((tenancy) => {
            const { endDate } = tenancy;

            return (
                endDate &&
                moment(endDate).utc().isAfter(durationStartDate) &&
                moment(endDate).utc().isBefore(durationEndDate)
            );
        });

        const periodic = periodicTenancies;

        const renewing = renewingTenancies.filter((tenancy) => {
            const { renewalDate } = tenancy;

            return (
                renewalDate &&
                moment(renewalDate).utc().isAfter(durationStartDate) &&
                moment(renewalDate).utc().isBefore(durationEndDate)
            );
        });

        return {
            active,
            noticeGiven,
            periodic,
            renewing,
        };
    }

    return {
        active: activeTenancies,
        noticeGiven: noticeGivenTenancies,
        periodic: periodicTenancies,
        renewing: renewingTenancies,
    };
}

export function baseComparator(a: string, b: string) {
    return a.localeCompare(b);
}

export function numberComparator(a: number, b: number) {
    return a - b;
}

export function checkIfTenancyIsRelevant(
    filterPropertyId: string,
    filterLandlord: string,
    filterManager: string,
    filterStatus: TenancyStatus,
    tenancyPropertyId: string,
    tenancyLandlords: string[],
    tenancyManagers: string[],
    tenantStatus: TenancyStatus,
) {
    return (
        (!filterPropertyId || filterPropertyId === tenancyPropertyId) &&
        (!filterLandlord || tenancyLandlords.includes(filterLandlord)) &&
        (!filterManager || tenancyManagers.includes(filterManager)) &&
        (!filterStatus || filterStatus === tenantStatus)
    );
}
