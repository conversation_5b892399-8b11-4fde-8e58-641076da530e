import { Injectable, Logger } from '@nestjs/common';
import {
    PropertySearchVO,
    PropertySearchPageDTO,
} from '../model/property.model';
import {
    search as osSearch,
    searchWithTotal,
} from 'src/common/service/opensearch.service';
import {
    getInvoiceIdsByTenancyId,
    getInvoicesByIds,
    findPropertyTenancies,
} from 'src/common/service/ddb.service';

@Injectable()
export class PropertyService {
    private logger = new Logger('PropertyService', { timestamp: true });

    constructor() {}

    async searchOrgPropertiesByName(
        organisationId: string,
        name: string,
    ): Promise<PropertySearchVO[]> {
        try {
            const result = await osSearch({
                index: 'property',
                body: {
                    query: {
                        bool: {
                            must: [
                                {
                                    term: {
                                        'propertyOrganisationId.keyword':
                                            organisationId,
                                    },
                                },
                                {
                                    match: {
                                        addressLine1: {
                                            query: name,
                                            fuzziness: 'AUTO',
                                        },
                                    },
                                },
                            ],
                        },
                    },
                    _source: ['id', 'addressLine1'],
                    size: 100,
                },
            });
            const searchVOs = result.map(({ id, addressLine1 }) => {
                return {
                    id: id,
                    name: addressLine1,
                } as PropertySearchVO;
            });
            return searchVOs;
        } catch (e) {
            this.logger.error(e);
            throw new Error('es query fails');
        }
    }

    async searchOrgPropertiesByManagerIdAndTime(
        organisationId: string,
        managerId: string,
    ): Promise<PropertySearchVO[]> {
        try {
            const result = await osSearch({
                index: 'property',
                body: {
                    query: {
                        bool: {
                            must: [
                                {
                                    term: {
                                        'propertyOrganisationId.keyword':
                                            organisationId,
                                    },
                                },
                                {
                                    match_phrase: {
                                        managers: managerId,
                                    },
                                },
                            ],
                        },
                    },
                    _source: ['id', 'addressLine1', 'managers'],
                    size: 100,
                },
            });
            const searchVOs = result.map(({ id, addressLine1 }) => {
                return {
                    id: id,
                    name: addressLine1,
                } as PropertySearchVO;
            });
            return searchVOs;
        } catch (e) {
            this.logger.error(e);
            throw new Error('es query fails');
        }
    }

    public async getInvoicesByPropertyId(propertyId: string): Promise<any> {
        const tenancyInfos = await findPropertyTenancies(propertyId);
        const tenancyIds = tenancyInfos.map((tenancyInfo) => tenancyInfo.id);

        const tenancyInvoiceResults = await Promise.all(
            tenancyIds.map((tenancyId) => getInvoiceIdsByTenancyId(tenancyId)),
        );

        const invoiceIds = tenancyInvoiceResults.flatMap(
            (tenancyInvoiceInfos) =>
                tenancyInvoiceInfos.map(
                    (tenancyInvoiceInfo) =>
                        tenancyInvoiceInfo.invoiceTenancyInvoiceId,
                ),
        );

        return await getInvoicesByIds(invoiceIds);
    }

    async searchOrgProperties({
        organisationId,
        address,
        type,
        status,
        managerId,
        landlordId,
    }: PropertySearchPageDTO): Promise<{
        total: number;
        propertyList: PropertySearchVO[];
    }> {
        try {
            const query: any = {
                bool: {
                    must: [
                        {
                            term: {
                                'propertyOrganisationId.keyword':
                                    organisationId,
                            },
                        },
                    ],
                },
            };

            if (address) {
                query.bool.must.push({
                    wildcard: {
                        'addressLine1.keyword': `*${address}*`,
                    },
                });
            }

            if (type) {
                query.bool.must.push({
                    term: {
                        'type.keyword': type,
                    },
                });
            }

            if (status) {
                query.bool.must.push({
                    term: {
                        'status.keyword': status,
                    },
                });
            }

            if (managerId) {
                query.bool.must.push({
                    terms: {
                        'managers.keyword': [managerId],
                    },
                });
            }

            if (landlordId) {
                query.bool.must.push({
                    term: {
                        'landlords.keyword': landlordId,
                    },
                });
            }

            const result = await searchWithTotal({
                index: 'property',
                body: {
                    query,
                    _source: ['id', 'addressLine1', 'reference'],
                    size: 100,
                    track_total_hits: true,
                },
            });

            const searchVOs = result.hits.map(
                ({ id, addressLine1, reference }) =>
                    ({
                        id,
                        name: addressLine1,
                        reference,
                    }) as PropertySearchVO,
            );

            return {
                total: result.total,
                propertyList: searchVOs,
            };
        } catch (e) {
            this.logger.error(e);
            throw new Error('es query fails');
        }
    }
}
