import { ApiProperty } from '@nestjs/swagger';

export class PropertySearchVO {
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    reference: string;
}

export class PropertySearchPageDTO {
    @ApiProperty()
    organisationId: string;

    @ApiProperty()
    address: string;

    @ApiProperty({ required: false })
    type?: string;

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    managerId?: string;

    @ApiProperty({ required: false })
    landlordId?: string;
}
