import { <PERSON>, Get, Logger, Query, Req, Res } from '@nestjs/common';
import { Response } from 'express';
import { BaseRes, LwRequest, responseError, responseOk } from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import { ApiExtraModels, ApiOkResponse, ApiOperation, ApiQuery, getSchemaPath } from '@nestjs/swagger';
import { PropertySearchVO } from 'src/property/model/property.model';
import { PropertyService } from 'src/property/service/property.service';

@Controller("api/v1/property")
@ApiExtraModels(BaseRes,PropertySearchVO)
export class PropertyController{
    private logger = new Logger('PropertyController', { timestamp: true });
    constructor(private propertyService: PropertyService) {}

    @ApiOperation({ summary: 'Search organisation property by name' })
    @ApiQuery({
        name: 'name',
        description: 'Property name',
    })
    @ApiOkResponse({
        schema: {
          allOf: [
            { $ref: getSchemaPath(BaseRes) },
            {
              properties: {
                data: { type: 'array', items: { $ref: getSchemaPath(PropertySearchVO) } },
              },
            },
          ],
        },
    })
    @Get('search')
    public async searchProperty(@Query('name') name: string, @Req() req: LwRequest, @Res() res: Response) {
        if (!name) {
            responseError(400, res, ResponseCode.PARAM_INVALID);
            return;
        }
        try {
            const result = await this.propertyService.searchOrgPropertiesByName(req.user['custom:organisationId'], name);
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get invoices by property ID' })
    @ApiQuery({
      name: 'propertyId',
      description: 'Property ID',
      required: true,
    })
    @Get('invoices')
    public async getInvoicesByProperty(@Query('propertyId') propertyId: string, @Req() req: LwRequest, @Res() res: Response) {
        if (!propertyId) {
          responseError(400, res, ResponseCode.PARAM_INVALID);
          return;
        }
        try {
          const result = await this.propertyService.getInvoicesByPropertyId(propertyId);
          responseOk(res, result);
        } catch (e) {
          this.logger.error(e);
          responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    

}
