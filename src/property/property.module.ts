import { Module, forwardRef } from '@nestjs/common';
import { PropertyController } from './controller/api/property.controller';
import { PropertyService } from './service/property.service';
import { ApplicationModule } from '../application/application.module';
@Module({
    imports: [forwardRef(() => ApplicationModule)],
    controllers: [PropertyController],
    providers: [PropertyService],
    exports: [PropertyService],
})
export class PropertyModule {}
