import {
    Body,
    Controller,
    Delete,
    Get,
    Logger,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import { Response } from 'express';
import {
    BaseRes,
    LwRequest,
    responseError,
    responseOk,
} from '../../../common/util/requestUtil';
import { ResponseCode } from '../../../common/constant/responseCode';
import {
    ApiBearerAuth,
    ApiBody,
    ApiExtraModels,
    ApiOkResponse,
    ApiOperation,
    ApiQuery,
    getSchemaPath,
} from '@nestjs/swagger';
import { OrganisationService } from 'src/organisation/service/organisation.service';
import {
    CreateOrganisationVO,
    EligibleOrganisationVO,
    ExpireFlagDTO,
    OrganisationBasicVO,
    OrganisationLedgerCodeVO,
    OrganisationTaskChecklistVO,
    OrganisationTaskReminderVO,
    OrganisationVO,
} from '../../model/organisation.model';
import { TemplateLetter } from '../../../common/model/templateLetter';
import { UserType } from '../../../common/model/user';
import { LedgerCode } from '../../../common/model/organisation';

@Controller('api/v1/organisation')
@ApiExtraModels(
    BaseRes,
    OrganisationTaskChecklistVO,
    EligibleOrganisationVO,
    TemplateLetter,
    OrganisationLedgerCodeVO,
    OrganisationBasicVO,
    OrganisationVO,
    OrganisationTaskReminderVO,
)
@ApiBearerAuth()
export class OrganisationController {
    private readonly logger = new Logger('OrganisationController', {
        timestamp: true,
    });

    constructor(private readonly organisationService: OrganisationService) {}

    @ApiOperation({ summary: 'Get organisation reporting history' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'dateFrom',
        required: false,
    })
    @Get('/reporting-history')
    public async getOrganisationReportingHistory(
        @Query('label') label: string,
        @Query('dateFrom') dateFrom: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.organisationService.getOrganisationReportingHistory(
                    req.user['custom:organisationId'],
                    label,
                    dateFrom,
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Search eligible organisations' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(EligibleOrganisationVO),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/eligibleOrganisations')
    public async listEligibleOrganisations(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = (
                await this.organisationService.listUserOrganisationsV1(
                    req.user['custom:organisationId'],
                )
            ).map((org) => {
                return {
                    id: org.id,
                    name: org.name,
                    createdAt: org.createdAt,
                    logo: org.logo,
                };
            }) as EligibleOrganisationVO[];
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Switch Organisation' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'string',
                        },
                    },
                },
            ],
        },
    })
    @Post('/switch/:newOrganisationId')
    public async switchOrganisation(
        @Param('newOrganisationId') newOrganisationId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.organisationService.switchOrganisation(
                req.user.sub,
                newOrganisationId,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Add User To Organisation' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(EligibleOrganisationVO),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Post('/add-to/:newOrganisationId')
    public async addUserToOrganisation(
        @Param('newOrganisationId') newOrganisationId: string,
        @Query('userId') userId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = (
                await this.organisationService.addUserToOrganisation(
                    userId,
                    newOrganisationId,
                )
            ).map((org) => {
                return {
                    id: org.id,
                    emailId: org.emailId,
                    name: org.name,
                    email: org.email,
                    phone: org.phone,
                };
            }) as EligibleOrganisationVO[];
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Remove User From Organisation' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(EligibleOrganisationVO),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Post('/remove-from')
    public async removeUserFromOrganisation(
        @Query('userId') userId: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = (
                await this.organisationService.removeUserFromOrganisation(
                    userId,
                    req.user['custom:organisationId'],
                )
            ).map((org) => {
                return {
                    id: org.id,
                    emailId: org.emailId,
                    name: org.name,
                    email: org.email,
                    phone: org.phone,
                };
            }) as EligibleOrganisationVO[];
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Add Organisation Mail' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'boolean',
                        },
                    },
                },
            ],
        },
    })
    @Post('/add-mail')
    public async addOrganisationMail(
        @Query('organisationMail') organisationMail: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.organisationService.addOrganisationMail(
                req.user['custom:organisationId'],
                organisationMail,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    /*@ApiOperation({ summary: 'Delete Organisation Data' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'null',
                            example: null,
                        },
                    },
                },
            ],
        },
    })
    @Delete('/')
    public async deleteOrganisationData(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            await this.organisationService.deleteOrganisationData(
                req.user['custom:organisationId'],
            );
            responseOk(res, null);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }*/

    @ApiOperation({ summary: 'Update Organisation ExpiredFlag' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'boolean',
                        },
                    },
                },
            ],
        },
    })
    @ApiBody({
        type: ExpireFlagDTO,
        description:
            'Data transfer object to update organisations expired flag',
    })
    @Post('/update/expiredFlag')
    public async updateOrganisationExpiredFlag(
        @Body() expireFlagDTO: ExpireFlagDTO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.organisationService.updateOrganisationExpiredFlag(
                    expireFlagDTO,
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get Organisation templateLetters' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(TemplateLetter),
                            },
                        },
                    },
                },
            ],
        },
    })
    @ApiQuery({
        name: 'contactType',
        required: false,
        enum: UserType,
    })
    @ApiQuery({
        name: 'category',
        required: false,
    })
    @ApiQuery({
        name: 'letterName',
        required: false,
    })
    @Get('/template-letters')
    public async listTemplateLetters(
        @Query('contactType') contactType: UserType,
        @Query('category') category: string,
        @Query('letterName') letterName: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.organisationService.listTemplateLetters(
                req.user['custom:organisationId'],
                contactType,
                category,
                letterName,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update Organisation LedgerCodes' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(OrganisationLedgerCodeVO) },
                    },
                },
            ],
        },
    })
    @ApiBody({
        type: LedgerCode,
        isArray: true,
        description: 'Data transfer object to update organisation ledgerCodes',
    })
    @Post('/ledgerCodes')
    public async updateOrganisationLedgerCodes(
        @Body() ledgerCodes: LedgerCode[],
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.organisationService.updateOrganisationLedgerCodes(
                    req.user['custom:organisationId'],
                    ledgerCodes,
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.message, e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get Organisation basic data' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(OrganisationBasicVO) },
                    },
                },
            ],
        },
    })
    @Get('/basic-data')
    public async getOrganisationBasicData(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.organisationService.getOrganisationBasicData(
                    req.user['custom:organisationId'],
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get Organisation' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: { $ref: getSchemaPath(OrganisationVO) },
                    },
                },
            ],
        },
    })
    @Get('/')
    public async getOrganisation(@Req() req: LwRequest, @Res() res: Response) {
        try {
            const result = await this.organisationService.getOrganisation(
                req.user['custom:organisationId'],
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.message, e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get OrganisationTaskChecklist ' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'array',
                            items: {
                                $ref: getSchemaPath(
                                    OrganisationTaskChecklistVO,
                                ),
                            },
                        },
                    },
                },
            ],
        },
    })
    @Get('/organisation-task-checklists')
    public async getOrganisationTaskChecklists(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.organisationService.getOrganisationTaskChecklists(
                    req.user['custom:organisationId'],
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get Organisation TaskReminder ' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            $ref: getSchemaPath(OrganisationTaskReminderVO),
                        },
                    },
                },
            ],
        },
    })
    @Get('/task-reminders')
    public async getOrganisationTaskReminders(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result =
                await this.organisationService.getOrganisationTaskReminders(
                    req.user['custom:organisationId'],
                );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Create Work Space ' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'string',
                        },
                    },
                },
            ],
        },
    })
    @Put('/')
    public async createOrganisation(
        @Body() organisationVO: CreateOrganisationVO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.organisationService.initWorkspace(
                req.user.sub,
                organisationVO,
                false,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e.message, e.stack);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete WorkSpace ' })
    @ApiOkResponse({
        schema: {
            allOf: [
                { $ref: getSchemaPath(BaseRes) },
                {
                    properties: {
                        data: {
                            type: 'string',
                        },
                    },
                },
            ],
        },
    })
    @Delete('/')
    public async deleteOrganisation(
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const result = await this.organisationService.deleteOrganisation(
                req.user['custom:organisationId'],
                req.user.sub,
            );
            responseOk(res, result);
        } catch (e) {
            this.logger.error(e);
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
