import { Modu<PERSON> } from '@nestjs/common';
import { OrganisationController } from './controller/api/organisation.controller';
import { OrganisationService } from './service/organisation.service';
import { DocumentSupplierService } from '../document/service/documentSupplier.service';
import { DocumentTemplateService } from '../document/service/documentTemplate.service';
import { DocumentTemplateTypeService } from '../document/service/documentTemplateType.service';
import { DocumentService } from '../document/service/document.service';
import { EventsEmitterService } from '../common/service/eventsEmitter.service';

@Module({
    controllers: [OrganisationController],
    providers: [
        OrganisationService,
        DocumentSupplierService,
        DocumentTemplateService,
        DocumentTemplateTypeService,
        DocumentService,
        EventsEmitterService,
    ],
    exports: [OrganisationService],
})
export class OrganisationModule {}
