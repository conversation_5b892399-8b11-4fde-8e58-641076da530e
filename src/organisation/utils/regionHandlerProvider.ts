import { RolesDeterminatorUS } from '../service/roles/implementations/rolesDeterminatorUS';
import { RolesDeterminatorUK } from '../service/roles/implementations/rolesDeterminatorUK';
import { ENV_CONFIG } from '../../common/constant/config.constant';

export class RegionHandlerProviderUtil {
    static regionHandlerProvider() {
        switch (ENV_CONFIG.APP_MARKET) {
            case 'UK':
                return new RolesDeterminatorUK();
            case 'US':
                return new RolesDeterminatorUS();
            default:
                throw new Error(
                    `Region handler not found for ${ENV_CONFIG.APP_MARKET}`,
                );
        }
    }
}
