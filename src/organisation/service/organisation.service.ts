import { Injectable, Logger } from '@nestjs/common';
import { LedgerCode, Organisation } from '../../common/model/organisation';
import { UserRepository } from '../../common/repository/user';
import { OrganisationRepository } from '../../common/repository/organisation';
import { OrganisationUserRepository } from '../../common/repository/organisationUser';
import { OrganisationUserMetaDataRepository } from '../../common/repository/organisationUserMetaData';
import { InvitationRepository } from '../../common/repository/invitation';
import { AdminUpdateUserAttributesCommandInput } from '@aws-sdk/client-cognito-identity-provider/dist-types/commands/AdminUpdateUserAttributesCommand';
import { AdminDeleteUserAttributesCommandInput } from '@aws-sdk/client-cognito-identity-provider/dist-types/commands/AdminDeleteUserAttributesCommand';
import { SesUtil } from '../../common/util/sesUtil';
import { v1 as uuid } from 'uuid';
import {
    DocusignTemplateVO,
    ExpireFlagDTO,
    OrganisationLedgerCodeVO,
    OrganisationTaskChecklistVO,
    OrganisationTaskReminderVO,
    OrganisationBasicVO,
    TenancySettingsVO,
    OrganisationVO,
    CreateOrganisationVO,
} from '../model/organisation.model';
import { TemplateLetterRepository } from '../../common/repository/templateLetter';
import { TemplateLetter } from '../../common/model/templateLetter';
import { User, UserType } from '../../common/model/user';
import { SqsUtil } from '../../common/util/sqsUtil';
import { LambdaUtil } from '../../common/util/lambdaUtil';
import { DynamodbService } from '../../common/service/dynamodb.service';
import { ENV_CONFIG } from '../../common/constant/config.constant';
import { INDEX, TABLE } from '../../common/constant/dynamodb.constants';
import { IntegrationRepository } from '../../common/repository/integration';
import { DocumentSupplierService } from '../../document/service/documentSupplier.service';
import { DocumentTemplateService } from '../../document/service/documentTemplate.service';
import { DocumentTemplateTypeService } from '../../document/service/documentTemplateType.service';
import { DocumentService } from '../../document/service/document.service';
import { ReportingHistoryRepository } from '../../common/repository/reportingHistory';
import { DocusignTemplateRepository } from '../../common/repository/docusignTemplate';
import { TenancySettingRepository } from '../../common/repository/tenancySetting';
import { OrganisationTaskChecklistRepository } from '../../common/repository/organisationTaskChecklist';
import { ReminderRepository } from '../../common/repository/reminder';
import { TaskReminderRepository } from '../../common/repository/taskReminder';
import { Reminder } from '../../common/model/reminder';
import { TaskReminder } from '../../common/model/taskReminder';
import { RegionHandlerProviderUtil } from '../utils/regionHandlerProvider';
import { CategoryDocumentRepository } from '../../common/repository/categoryDocument';
import { DocumentTemplateType } from '@prisma/client';
import { DynamoDBDocument } from '@aws-sdk/lib-dynamodb';
import { DynamoDB } from '@aws-sdk/client-dynamodb';

const moment = require('moment');

@Injectable()
export class OrganisationService {
    private readonly logger = new Logger('OrganisationService', {
        timestamp: true,
    });
    protected readonly ddb: DynamoDBDocument;

    private readonly countryUsers = [
        {
            name: 'HMRC',
            country: 'GB',
        },
    ];

    private readonly LEDGER_CODES = [
        {
            name: 'Rent Income',
            code: '200',
            displayName: 'Rent Income',
        },
        {
            name: 'Lease Income',
            code: '201',
            displayName: 'Lease Income',
        },
        {
            name: 'Service Charge Income',
            code: '202',
            displayName: 'Service Charge Income',
        },
        {
            name: 'Ground Rent Income',
            code: '203',
            displayName: 'Ground Rent Income',
        },
        {
            name: 'Reservations Income',
            code: '204',
            displayName: 'Reservations Income',
        },
        {
            name: 'Other Income',
            code: '205',
            displayName: 'Other Income',
        },
        {
            name: 'Deposits',
            code: '206',
            displayName: 'Deposits',
        },
        {
            name: 'Deposit Scheme',
            code: '207',
            displayName: 'Deposit Scheme',
        },
        {
            name: 'Property Expenses',
            code: '325',
            displayName: 'Property Expenses',
        },
        {
            name: 'Management Fees',
            code: '326',
            displayName: 'Management Fees',
        },
        {
            name: 'Agent Commission',
            code: '327',
            displayName: 'Agent Commission',
        },
        {
            name: 'Landlord Payments',
            code: '328',
            displayName: 'Landlord Payments',
        },
        {
            name: 'Non-Resident',
            code: '330',
            displayName: 'Non-Resident',
        },
        {
            name: 'Suspense Account',
            code: '329',
            displayName: 'Suspense Account',
        },
        {
            name: 'Reserve 1',
            code: '210',
            displayName: 'Reserve 1',
        },
        {
            name: 'Reserve 2',
            code: '211',
            displayName: 'Reserve 2',
        },
        {
            name: 'Reserve 3',
            code: '212',
            displayName: 'Reserve 3',
        },
        {
            name: 'Reserve 4',
            code: '213',
            displayName: 'Reserve 4',
        },
        {
            name: 'Reserve 5',
            code: '214',
            displayName: 'Reserve 5',
        },
        {
            name: 'Tenant Balances',
            code: '700',
            displayName: 'Tenant Balances',
        },
        {
            name: 'Supplier Balances',
            code: '710',
            displayName: 'Supplier Balances',
        },
        {
            name: 'Landlord Balances',
            code: '704',
            displayName: 'Landlord Balances',
        },
        {
            name: 'Suspense Balances',
            code: '720',
            displayName: 'Suspense Balances',
        },
        {
            name: 'Council Tax',
            code: '332',
            displayName: 'Council Tax',
        },
        {
            name: 'Utilities',
            code: '333',
            displayName: 'Utilities',
        },
        {
            name: 'Float Balance',
            code: '315',
            displayName: 'Float Balance',
        },
    ];

    private readonly incomeLedgerCodeNames = [
        'Rent Income',
        'Lease Income',
        'Service Charge Income',
        'Ground Rent Income',
        'Reservations Income',
        'Other Income',
        'Property Expenses',
        'Management Fees',
        'Agent Commission',
        'Landlord Payments',
        'Non-Resident',
        'Tenant Balances',
    ];

    private readonly INVOICE_TEMPLATES = [
        {
            type: 'AST',
            templateName: 'Standard',
        },
        {
            type: 'ASSURED',
            templateName: 'Standard',
        },
        {
            type: 'LICENSE',
            templateName: 'Standard',
        },
        {
            type: 'COMMERCIAL',
            templateName: 'Standard',
        },
        {
            type: 'NA',
            templateName: 'Standard',
        },
        {
            type: 'DPS_CUSTODIAL',
            templateName: 'Standard',
        },
        {
            type: 'DPS_INSURANCE',
            templateName: 'Standard',
        },
        {
            type: 'MY_DEPOSITS',
            templateName: 'Standard',
        },
        {
            type: 'TDS_CUSTODIAL',
            templateName: 'Standard',
        },
        {
            type: 'TDS_INSURANCE',
            templateName: 'Standard',
        },
        {
            type: 'REPOSIT',
            templateName: 'Standard',
        },
        {
            type: 'DISPUDE_SERVICE_CUSTODIAL',
            templateName: 'Standard',
        },
        {
            type: 'INSURANCE',
            templateName: 'Standard',
        },
        {
            type: 'HELD_BY_AGENT',
            templateName: 'Standard',
        },
        {
            type: 'HELD_BY_LANDLORD',
            templateName: 'Standard',
        },
        {
            type: 'CONTRACTUAL',
            templateName: 'Standard',
        },
        {
            type: 'COMMON_LAW',
            templateName: 'Standard',
        },
        {
            type: 'SERVICE_CHARGE',
            templateName: 'Standard',
        },
        {
            type: 'HOLIDAYLET',
            templateName: 'Standard',
        },
        {
            type: 'NONHOUSINGACT',
            templateName: 'Standard',
        },
        {
            type: 'SALE',
            templateName: 'Standard',
        },
        {
            type: 'LEASE',
            templateName: 'Standard',
        },
        {
            type: 'GROUND_RENT',
            templateName: 'Standard',
        },
    ];

    private readonly TRACKING_CATEGORIES = [
        {
            name: 'Property',
            xeroName: 'Property',
        },
        {
            name: 'Contacts',
            xeroName: 'Contacts',
        },
    ];

    private readonly ORGANISATION_ENTITIES = [
        {
            table: 'DocusignTemplates',
            index: INDEX.DOCUSIGN_TEMPLATES_ORGANISATION_INDEX,
            key: 'docusignTemplatesOrganisationId',
        },
        {
            table: 'Message',
            index: INDEX.MESSAGE_ORGANISATION_INDEX,
            key: 'messageOrganisationId',
        },
        {
            table: 'OrganisationTaskChecklist',
            index: INDEX.ORGANISATION_TASK_CHECKLIST_ORGANISATION_INDEX,
            key: 'organisationTaskChecklistOrganisationId',
        },
        {
            table: 'TaskReminder',
            index: INDEX.TASK_REMINDER_ORGANISATION_INDEX,
            key: 'taskReminderOrganisationId',
        },
        {
            table: 'Reminder',
            index: INDEX.REMINDER_ORGANISATION_INDEX,
            key: 'reminderOrganisationId',
        },
        {
            table: 'Address',
            index: INDEX.ADDRESS_ORGANISATION_INDEX,
            key: 'addressOrganisationId',
        },
        {
            table: 'Account',
            index: INDEX.ACCOUNT_ORGANISATION_INDEX,
            key: 'accountOrganisationId',
        },
        {
            table: 'Invoice',
            index: INDEX.INVOICE_ORGANISATION_INDEX,
            key: 'invoiceOrganisationId',
        },
        {
            table: 'Transaction',
            index: INDEX.TRANSACTION_ORGANISATION_INDEX,
            key: 'transactionOrganisationId',
        },
        {
            table: 'Payment',
            index: INDEX.PAYMENT_ORGANISATION_INDEX,
            key: 'paymentOrganisationId',
        },
        {
            table: 'Tenancy',
            index: INDEX.TENANCY_ORGANISATION_INDEX,
            key: 'tenancyOrganisationId',
        },
        {
            table: 'Document',
            index: INDEX.DOCUMENT_ORGANISATION_UNSORTED_INDEX,
            key: 'documentOrganisationId',
        },
        {
            table: 'OrganisationUser',
            index: INDEX.ORGANISATION_USER_ORGANISATION_INDEX,
            key: 'organisationUserOrganisationId',
        },
    ];

    constructor(
        private readonly userRepository: UserRepository,
        private readonly organisationRepository: OrganisationRepository,
        private readonly organisationUserRepository: OrganisationUserRepository,
        private readonly organisationUserMetaDataRepository: OrganisationUserMetaDataRepository,
        private readonly invitationRepository: InvitationRepository,
        private readonly templateLetterRepository: TemplateLetterRepository,
        private readonly dynamodbService: DynamodbService,
        private readonly integrationRepository: IntegrationRepository,
        private readonly documentSupplierService: DocumentSupplierService,
        private readonly documentTemplateService: DocumentTemplateService,
        private readonly documentTemplateTypeService: DocumentTemplateTypeService,
        private readonly documentService: DocumentService,
        private readonly reportingHistoryRepository: ReportingHistoryRepository,
        private readonly docusignTemplateRepository: DocusignTemplateRepository,
        private readonly tenancySettingRepository: TenancySettingRepository,
        private readonly organisationTaskChecklistRepository: OrganisationTaskChecklistRepository,
        private readonly reminderRepository: ReminderRepository,
        private readonly taskReminderRepository: TaskReminderRepository,
        private readonly categoryDocumentRepository: CategoryDocumentRepository,
    ) {
        this.ddb = DynamoDBDocument.from(
            new DynamoDB({ region: ENV_CONFIG.REGION } as any),
        );
    }

    async listUserOrganisations(cognitoId: string): Promise<Organisation[]> {
        const user = await this.userRepository.getCognitoUser(cognitoId);
        const attribute = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:eligibleWorkspaces',
        );
        if (!attribute) return [];
        const organisationIds = Object.keys(JSON.parse(attribute.Value));
        return await this.organisationRepository.findByIds(organisationIds);
    }

    async listUserOrganisationsV1(
        organisationId: string,
    ): Promise<Organisation[]> {
        const organisationIds = [organisationId];
        return await this.organisationRepository.findByIds(organisationIds);
    }

    async switchOrganisation(cognitoId: string, newOrganisationId: string) {
        const user = await this.userRepository.findByCognitoId(cognitoId);
        await this.userRepository.changeCognitoOrganisation(
            cognitoId,
            newOrganisationId,
        );
        await this.userRepository.updateUserOrganisation(
            user.id,
            newOrganisationId,
        );

        // TODO I think it causes some issue on FE, will comment this line
        // await idp.adminUserGlobalSignOut(params);

        return newOrganisationId;
    }

    async addUserToOrganisation(
        userId,
        newOrganisationId,
    ): Promise<Organisation[]> {
        const newOrganisation =
            await this.organisationRepository.findById(newOrganisationId);
        if (!newOrganisation) {
            throw new Error(
                `Organisation does not exist - ${newOrganisationId}`,
            );
        }
        const dynamoUser = await this.userRepository.findById(userId);
        const user = await this.userRepository.getCognitoUser(
            dynamoUser.cognitoId,
        );

        const attribute = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:eligibleWorkspaces',
        );
        //custom:organisationId
        const currentOrganisation = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:organisationId',
        );
        let eligibleOrganisations = [];
        if (attribute && attribute.Value) {
            eligibleOrganisations = Object.keys(JSON.parse(attribute.Value));
        }
        if (eligibleOrganisations.includes(newOrganisationId)) {
            throw new Error('User is already part of this organisation');
        }

        eligibleOrganisations.push(newOrganisationId);

        const organisationObject = {};
        eligibleOrganisations.forEach(
            (org) => (organisationObject[org] = true),
        );
        const updateUserAttributes: AdminUpdateUserAttributesCommandInput = {
            UserPoolId: '',
            UserAttributes: [
                {
                    Name: 'custom:eligibleWorkspaces',
                    Value: JSON.stringify(organisationObject),
                },
            ],
            Username: user.Username,
        };
        if (!currentOrganisation?.Value) {
            updateUserAttributes.UserAttributes.push({
                Name: 'custom:organisationId',
                Value: newOrganisationId,
            });
        }
        await this.userRepository.updateCognitoUserAttributes(
            updateUserAttributes,
        );
        const organisationUser =
            await this.organisationUserRepository.findByUserIdAndOrganisationId(
                userId,
                newOrganisationId,
            );
        if (!organisationUser) {
            await this.organisationUserRepository.createOrganisationUser(
                userId,
                newOrganisationId,
            );
        }
        const organisationUserMetaData =
            await this.organisationUserMetaDataRepository.findById(
                `${newOrganisationId}|${userId}`,
            );
        if (!organisationUserMetaData) {
            await this.organisationUserMetaDataRepository.createOrganisationUserMetaData(
                newOrganisationId,
                userId,
            );
        }
        return await this.organisationRepository.findByIds(
            eligibleOrganisations,
        );
    }

    async removeUserFromOrganisation(userId, organisationId) {
        const dynamoUser = await this.userRepository.findById(userId);
        let email = dynamoUser.cognitoEmail;

        const organisationUser =
            await this.organisationUserRepository.findByUserIdAndOrganisationId(
                userId,
                organisationId,
            );
        if (organisationUser) {
            await this.organisationUserRepository.deleteById(
                organisationUser.id,
            );
            await this.organisationUserMetaDataRepository.deleteOrganisationUserMetaData(
                organisationId,
                organisationUser.organisationUserUserId,
            );
        }
        if (!dynamoUser.cognitoId) {
            //haven't signed in yet
            email = dynamoUser.emails[0].email;
            await this.invitationRepository.deleteInvitations(
                email,
                organisationId,
            );
            await this.userRepository.deleteById(userId);
            return [];
        }
        //delete invitations
        await this.invitationRepository.deleteInvitations(
            email,
            organisationId,
        );

        const user = await this.userRepository.getCognitoUser(
            dynamoUser.cognitoId,
        );
        const attribute = user.UserAttributes.find(
            (attr) => attr.Name === 'custom:eligibleWorkspaces',
        );
        let eligibleOrganisations = [];

        if (attribute && attribute.Value) {
            eligibleOrganisations = Object.keys(JSON.parse(attribute.Value));
        }

        const allOrganisationIds = eligibleOrganisations.filter(
            (org) => org !== organisationId,
        );

        if (!allOrganisationIds || allOrganisationIds.length === 0) {
            //remove custom:eligibleWorkspaces custom:organisationId
            const deleteUserAttributes: AdminDeleteUserAttributesCommandInput =
                {
                    UserAttributeNames: [
                        'custom:eligibleWorkspaces',
                        'custom:organisationId',
                    ],
                    UserPoolId: '',
                    Username: user.Username,
                };
            if (attribute && attribute.Value) {
                await this.userRepository.deleteCognitoUserAttributes(
                    deleteUserAttributes,
                );
            }
            //remove currentOrganisation in table User
            await this.userRepository.removeCurrentOrganisationFromUser(userId);
        } else {
            const organisationObject = {};
            allOrganisationIds.forEach(
                (org) => (organisationObject[org] = true),
            );

            const updateWorkspaceParams: AdminUpdateUserAttributesCommandInput =
                {
                    UserAttributes: [
                        {
                            Name: 'custom:eligibleWorkspaces',
                            Value: JSON.stringify(organisationObject),
                        },
                    ],
                    UserPoolId: '',
                    Username: user.Username,
                };
            //update currentOrganisation in table User
            if (dynamoUser.currentOrganisation === organisationId) {
                await this.userRepository.updateUserOrganisation(
                    userId,
                    allOrganisationIds[0],
                );
                updateWorkspaceParams.UserAttributes.push({
                    Name: 'custom:organisationId',
                    Value: allOrganisationIds[0],
                });
            }
            await this.userRepository.updateCognitoUserAttributes(
                updateWorkspaceParams,
            );
        }

        return await this.organisationRepository.findByIds(allOrganisationIds);
    }

    async addOrganisationMail(
        organisationId: string,
        organisationMail: string,
    ): Promise<boolean> {
        await SesUtil.verifyEmailIdentity(organisationMail);
        await this.organisationRepository.updateOrganisationMailStatus(
            organisationId,
            organisationMail,
            'UNVERIFIED',
        );

        return true;
    }

    async updateOrganisationExpiredFlag(input: ExpireFlagDTO) {
        const { organisationIds, expiredFlag } = input;
        await Promise.all(
            organisationIds
                .filter((id) => id && id !== '')
                .map(async (id) => {
                    id = this.getOrganisationId(id);
                    await this.organisationRepository.updateItem(id, {
                        expiredFlag: expiredFlag,
                    });
                }),
        );
        return true;
    }

    private getOrganisationId(name): string {
        return name.replace(/[^0-9a-z]/gi, '').toLowerCase();
    }

    async listTemplateLetters(
        organisationId: string,
        contactType: UserType,
        category: string,
        letterName: string,
    ): Promise<TemplateLetter[]> {
        const templateLetters =
            await this.templateLetterRepository.findOrganisationTemplateLetters(
                organisationId,
                contactType,
                category,
            );
        this.logger.log(
            `Template letters - ${JSON.stringify(templateLetters)}`,
        );

        if (letterName) {
            return templateLetters.filter((tl) =>
                tl.letterName.toLowerCase().includes(letterName.toLowerCase()),
            );
        }

        return templateLetters;
    }

    async updateOrganisationLedgerCodes(
        organisationId: string,
        ledgerCodes: LedgerCode[],
    ): Promise<OrganisationLedgerCodeVO> {
        if (!ledgerCodes || ledgerCodes.length < 1) {
            throw new Error('Ledger codes not provided');
        }

        const organisation =
            await this.organisationRepository.findById(organisationId);
        const tenancySettingsUpdateRequired =
            this.shouldUpdateTenancySettingsFeeLedgerCode(
                organisation,
                ledgerCodes,
            );

        this.logger.debug(
            `Tenancy settings update required: ${tenancySettingsUpdateRequired}`,
        );
        const tenancySettingsUpdateMessageSent = tenancySettingsUpdateRequired
            ? await this.sendMessageToUpdateTenancySettingsFeeLedgerCodeQueue(
                  organisationId,
                  ledgerCodes,
              )
            : false;

        const success =
            !tenancySettingsUpdateRequired ||
            (tenancySettingsUpdateRequired && tenancySettingsUpdateMessageSent);
        if (success) {
            this.logger.log(
                `Updating organisation ${organisationId} with new ledger codes`,
            );
            await this.organisationRepository.updateItem(organisationId, {
                ledgerCodes: ledgerCodes,
            });
        }

        return {
            organisationId: organisation.id,
            tenanciesUpdateInProgress: tenancySettingsUpdateMessageSent,
            success,
        } as OrganisationLedgerCodeVO;
    }

    private shouldUpdateTenancySettingsFeeLedgerCode(
        organisation: Organisation,
        ledgerCodes: LedgerCode[],
    ) {
        const currentFeeLedgerCode = this.findFeeLedgerCode(
            organisation.ledgerCodes,
        );
        const updatedFeeLedgerCode = this.findFeeLedgerCode(ledgerCodes);
        return updatedFeeLedgerCode?.code !== currentFeeLedgerCode?.code;
    }

    private findFeeLedgerCode(ledgerCodes: LedgerCode[] = []): LedgerCode {
        const feeLedgerCodes = ledgerCodes.filter(
            (it) => it.name === 'Management Fees',
        );
        if (feeLedgerCodes.length > 1) {
            throw new Error('Ambiguous "Management Fees" code update');
        }

        return feeLedgerCodes.length === 0 ? undefined : feeLedgerCodes[0];
    }

    private async sendMessageToUpdateTenancySettingsFeeLedgerCodeQueue(
        organisationId: string,
        ledgerCodes: LedgerCode[],
    ): Promise<boolean> {
        const newFeeLedgerCode = this.findFeeLedgerCode(ledgerCodes);
        const params = {
            QueueUrl:
                SqsUtil.QUEUE_NAME.UPDATE_TENANCY_SETTINGS_FEE_LEDGER_CODE,
            MessageBody: JSON.stringify({
                organisationId,
                newFeeLedgerCode: newFeeLedgerCode
                    ? {
                          name: newFeeLedgerCode.name,
                          displayName: newFeeLedgerCode.displayName,
                          code: newFeeLedgerCode.code,
                      }
                    : undefined,
            }),
        };

        try {
            const result = await SqsUtil.sendMessage(params);
            this.logger.log(
                `Message with id ${result.MessageId} to update tenancy settings fee ledger code sent successfully`,
            );
            return true;
        } catch (error) {
            this.logger.error(
                'Error while sending message to update tenancy settings fee ledger code:',
                error,
            );
            return false;
        }
    }

    private async deleteDocusignWebhook(organisationId: string) {
        const body = {
            queryStringParameters: {
                organisationId,
            },
        };

        const params = {
            FunctionName: LambdaUtil.FUNCTION_NAME.DOCUSIGN_REMOVE_CONNECTION,
            Payload: Buffer.from(JSON.stringify(body)),
        };

        await LambdaUtil.invoke(params);
    }

    private async findAndDeleteItems(
        table: string,
        index: string,
        indexKeyName: string,
        indexKeyValue: string,
    ) {
        return this.findOrganisationObjects(
            indexKeyValue,
            indexKeyName,
            table,
            index,
        )
            .then((objects) => {
                return Promise.all(
                    objects.map((eachObject) => {
                        return this.dynamodbService.delete(
                            table,
                            eachObject.id,
                        );
                    }),
                );
            })
            .catch((e) => {
                this.logger.error(`Failed to delete ${table} table items`, e);
            });
    }

    private async findOrganisationObjects(
        organisationId: string,
        expressionAttributeValue: string,
        tableName: string,
        tableIndex: string,
    ) {
        const params = {
            ExpressionAttributeValues: {
                [`:${expressionAttributeValue}`]: organisationId,
            },
            ExpressionAttributeNames: {
                [`#${expressionAttributeValue}`]: `${expressionAttributeValue}`,
            },
            KeyConditionExpression: `#${expressionAttributeValue} = :${expressionAttributeValue}`,
            TableName: `${tableName}-${ENV_CONFIG.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${ENV_CONFIG.ENV}`,
            IndexName: tableIndex || undefined,
        };
        return await this.dynamodbService.query(params);
    }

    async changeCurrentOrganisationAndDeleteUser(userId, organisationId) {
        try {
            const items = [];
            const user = await this.userRepository.findById(userId);
            const userWorkspaces = await this.findOrganisationObjects(
                userId,
                'organisationUserUserId',
                'OrganisationUser',
                INDEX.ORGANISATION_USER_USER_INDEX,
            );
            this.logger.log(`User - ${JSON.stringify(user)}`);
            this.logger.log(
                `User workspaces - ${JSON.stringify(userWorkspaces)}`,
            );
            if (userWorkspaces.length > 1) {
                this.logger.log(`If statement`);
                const cognitoUser = await this.userRepository.getCognitoUser(
                    user.cognitoId,
                );
                const eligibleWorkspaces = cognitoUser.UserAttributes.filter(
                    (each) => each.Name === 'custom:eligibleWorkspaces',
                );
                const eligible = JSON.parse(eligibleWorkspaces[0].Value);
                delete eligible[organisationId];
                this.logger.log(
                    `Updated workspaces - ${JSON.stringify(eligible)}`,
                );
                const otherOrganisations = userWorkspaces.filter(
                    (each) =>
                        each.organisationUserOrganisationId !== organisationId,
                );
                items.push(
                    this.userRepository.updateUserOrganisation(
                        userId,
                        otherOrganisations[0].organisationUserOrganisationId,
                    ),
                );
                items.push(
                    this.organisationUserRepository.deleteById(
                        userWorkspaces.filter(
                            (each) =>
                                each.organisationUserOrganisationId ===
                                organisationId,
                        )[0].id,
                    ),
                );
                items.push(
                    this.userRepository.updateCognitoUserOrganisations(
                        otherOrganisations[0].organisationUserOrganisationId,
                        user.cognitoId,
                        eligible,
                    ),
                );
            } else {
                items.push(this.userRepository.deleteById(userId));
                if (user.cognitoId) {
                    items.push(
                        this.userRepository.adminDeleteUser(user.cognitoId),
                    );
                }
                if (userWorkspaces.length) {
                    items.push(
                        this.organisationUserRepository.deleteById(
                            userWorkspaces[0].id,
                        ),
                    );
                }
            }
            this.logger.log(
                `Items were deleted in user and organisationUser tables - ${items.length}`,
            );
            await Promise.all(items);
        } catch (e) {
            this.logger.error(`Failed to delete user info - ${userId}`, e);
        }
    }

    async getOrganisationReportingHistory(
        organisationId: string,
        label: string,
        dateFrom: string,
    ) {
        const pastLabelDateQuery = dateFrom
            ? `${label}|${dateFrom}`
            : `${label}|1990-01-01`;
        const futureDate = moment().utc().add(2, 'days').format('YYYY-MM-DD');
        const futureLabelDateQuery = `${label}|${futureDate}`;
        const reportingHistories =
            await this.reportingHistoryRepository.getSortedOrganisationReportHistories(
                organisationId,
                pastLabelDateQuery,
                futureLabelDateQuery,
            );
        return reportingHistories.map((repHistory) => {
            const formattedReportHistory = repHistory;
            formattedReportHistory.data = JSON.stringify(
                formattedReportHistory.data,
            );
            return formattedReportHistory;
        });
    }

    async getOrganisationBasicData(
        organisationId: string,
    ): Promise<OrganisationBasicVO> {
        const [organisation, docusignTemplates, settings] = await Promise.all([
            this.organisationRepository.findById(organisationId) as any,
            this.docusignTemplateRepository.getDocusignTemplatesByOrgId(
                organisationId,
            ) as any,
            this.tenancySettingRepository.findByOrganisationId(
                organisationId,
            ) as any,
        ]);
        const organisationVO = new OrganisationBasicVO(organisation);
        organisationVO.docusignTemplates = docusignTemplates.map(
            (d) => new DocusignTemplateVO(d),
        );
        organisationVO.tenancySettings = new TenancySettingsVO(settings);
        return organisationVO;
    }

    async getOrganisation(organisationId: string): Promise<OrganisationVO> {
        const [organisation, docusignTemplates, settings] = await Promise.all([
            this.organisationRepository.findById(organisationId) as any,
            this.docusignTemplateRepository.getDocusignTemplatesByOrgId(
                organisationId,
            ) as any,
            this.tenancySettingRepository.findByOrganisationId(
                organisationId,
            ) as any,
        ]);
        const organisationVO = new OrganisationVO(organisation);
        organisationVO.docusignTemplates = docusignTemplates.map(
            (d) => new DocusignTemplateVO(d),
        );
        organisationVO.tenancySettings = new TenancySettingsVO(settings);
        return organisationVO;
    }

    async getOrganisationTaskChecklists(
        organisationId: string,
    ): Promise<OrganisationTaskChecklistVO[]> {
        const list =
            await this.organisationTaskChecklistRepository.getOrganisationTaskChecklistsByOrgId(
                organisationId,
            );
        return list.map((i) => new OrganisationTaskChecklistVO(i));
    }

    async getOrganisationTaskReminders(
        organisationId: string,
    ): Promise<OrganisationTaskReminderVO> {
        const [reminders, taskReminders] = await Promise.all([
            this.reminderRepository.findByOrganisationId(organisationId),
            this.taskReminderRepository.findByOrganisationId(organisationId),
        ] as Promise<Reminder[] | TaskReminder[]>[]);
        return {
            reminders,
            taskReminders,
        } as OrganisationTaskReminderVO;
    }

    async initWorkspace(
        cognitoUser: string,
        organisationVo: CreateOrganisationVO,
        fromLofty: boolean,
    ): Promise<string> {
        let organisationId = this.getOrganisationId(
            organisationVo.organisationName,
        );
        if (fromLofty) {
            organisationId = organisationVo.organisationId;
            this.logger.log('orgId - ', JSON.stringify(organisationId));
        }
        const items = [];
        const organisation =
            await this.organisationRepository.findById(organisationId);
        const adminUser =
            await this.userRepository.findByCognitoId(cognitoUser);
        const adminUserId = adminUser.id;
        const adminUserType = adminUser.type;
        const tenancySettingsId = uuid();
        const botUserId = uuid();
        const supportCognitoEmail = `support+${organisationId}@${ENV_CONFIG.MAIL_DOMAIN}`;
        if (organisation) {
            throw new Error(
                `Organisation already exists: ${JSON.stringify(organisation)}`,
            );
        }
        if (adminUser.type === 'TENANT') {
            throw new Error(
                `Tenants can't create new workspaces. userId=${adminUser.id},attemptedOrganisationName:${organisationVo.organisationName}`,
            );
        }

        const cognitoId = await this.userRepository.createCognitoUser(
            organisationId,
            supportCognitoEmail,
        );
        const defaultCountryTaxBotId =
            await this.createCountryUsers(organisationId);
        const [
            balanceTransferContactUserId,
            openingBalanceContactUserId,
            depositSchemeLedgerContactUserId,
        ] = await Promise.all([
            this.createBalanceTransferUser(organisationId),
            this.createOpeningBalanceUser(organisationId),
            this.createdepositSchemeContactUser(organisationId),
        ]);

        const createOrganisationParams = {
            organisationId,
            organisationName: organisationVo.organisationName,
            adminUserId,
            tenancySettingsId,
            botUserId,
            defaultCountryTaxBotId,
            balanceTransferContactUserId,
            openingBalanceContactUserId,
            depositSchemeLedgerContactUserId,
            payoutVersion: organisationVo.payoutVersion,
            utmCustomerAttributionSource:
                organisationVo.utmCustomerAttributionSource,
            adminUserType,
        };

        const adminUserRoles =
            RegionHandlerProviderUtil.regionHandlerProvider().determineRoles(
                adminUser,
            );
        this.logger.log(`Cognito id - ${cognitoId}`);
        this.logger.log(`Admin user roles - ${JSON.stringify(adminUserRoles)}`);

        items.push(
            this.createTenancySettings(tenancySettingsId, organisationId),
        );
        items.push(this.createOrganisation(createOrganisationParams));
        items.push(
            this.createUser(
                botUserId,
                supportCognitoEmail,
                cognitoId,
                organisationId,
            ),
        );
        items.push(
            this.updateUser(adminUserId, adminUserRoles, organisationId),
        );

        if (
            !(
                await this.organisationUserRepository.findUserOrganisations(
                    adminUserId,
                    organisationId,
                )
            ).length
        ) {
            items.push(
                this.createOrganisationUser(adminUserId, organisationId),
            );
        }
        this.logger.log(`Items - ${JSON.stringify(items)}`);

        await this.organisationRepository.transactWrite(items);
        //await this.addUserToCognitoGroup(cognitoUser, adminUserRoles);
        //await this.addOrganisationAttributes(cognitoUser, organisationId);
        await this.categoryDocumentRepository.createCategoryDocuments(
            organisationId,
        );
        await this.createDocumentTemplates(organisationId);
        await this.organisationUserMetaDataRepository.createOrganisationUserMetaData(
            organisationId,
            adminUserId,
        );
        return organisationId;
    }

    async createDocumentTemplates(organisationId) {
        const types = [
            'Tenancy Agreement',
            'Application for Tenancy',
            'Guarantor Form',
            'Other type 1',
            'Other type 2',
        ];
        const items = [];
        for (const type of types) {
            const templateType = {
                name: type,
                index: types.indexOf(type),
                organisationId: organisationId,
            } as DocumentTemplateType;
            const templType =
                await this.documentTemplateTypeService.createDocumentTemplateType(
                    templateType,
                );
            items.push(templType);
        }
        this.logger.log(`Updating ${items.length} item`);
        await Promise.all(items);
    }

    async addOrganisationAttributes(cognitoId, organisationId) {
        const workspaces =
            await this.userRepository.findUserWorkspaces(cognitoId);
        this.logger.log(`User workspaces - ${JSON.stringify(workspaces)}`);
        const params = {
            UserAttributes: [
                {
                    Name: 'custom:organisationId',
                    Value: organisationId,
                },
                {
                    Name: 'custom:eligibleWorkspaces',
                    Value: JSON.stringify(
                        Object.assign({ [organisationId]: true }, workspaces),
                    ),
                },
            ],
            Username: cognitoId,
        } as AdminUpdateUserAttributesCommandInput;
        await this.userRepository.updateCognitoUserAttributes(params);
    }

    async addUserToCognitoGroup(cognitoId, roles) {
        await Promise.all(
            roles.map((role) => {
                this.userRepository.addCognitoRole(cognitoId, role);
            }),
        );
    }

    createOrganisationUser(userId, organisationId) {
        const params = {
            TableName: TABLE.ORGANISATION_USER_TABLE,
            Item: {
                id: uuid(),
                organisationUserOrganisationId: organisationId,
                organisationUserUserId: userId,
                createdAt: new Date().toISOString(),
                sourceType: 'createOrganisation',
            },
        };

        return {
            Put: params,
        };
    }

    updateUser(id, roles, organisation) {
        const params = {
            ExpressionAttributeNames: {
                '#attr': 'roles',
                '#attr1': 'currentOrganisation',
            },
            ExpressionAttributeValues: {
                ':attr': roles,
                ':attr1': organisation,
            },
            Key: {
                id: id,
            },
            TableName: TABLE.USER_TABLE,
            UpdateExpression: 'SET #attr = :attr, #attr1 = :attr1',
        };

        return {
            Update: params,
        };
    }

    createUser(id, email, cognitoId, organisationId) {
        const params = {
            TableName: TABLE.USER_TABLE,
            Item: {
                id: id,
                cognitoEmail: email,
                cognitoId: cognitoId,
                currentOrganisation: organisationId,
                fname: 'LoftyWorks',
                sname: 'Bot',
                public: true,
                owner: cognitoId,
                onboardingStep: -1,
                onboardingSeen: false,
                type: 'TEAM',
                emails: [{ type: 'Work', email: email }],
                createdAt: moment().utc().format(),
            },
        };

        return {
            Put: params,
        };
    }

    createOrganisation({
        organisationId,
        organisationName,
        adminUserId,
        tenancySettingsId,
        botUserId,
        defaultCountryTaxBotId,
        balanceTransferContactUserId,
        openingBalanceContactUserId,
        depositSchemeLedgerContactUserId,
        payoutVersion,
        utmCustomerAttributionSource,
        adminUserType,
    }) {
        const params = {
            TableName: TABLE.ORGANISATION_TABLE,
            Item: {
                id: organisationId,
                name: organisationName,
                adminUser: adminUserId,
                botUser: botUserId,
                currency: this.getDefaultCurrency(),
                ledgerCodes: this.LEDGER_CODES,
                incomeLedgerCodeNames: this.incomeLedgerCodeNames,
                utmCustomerAttributionSource: utmCustomerAttributionSource,
                invoiceTemplates: this.INVOICE_TEMPLATES,
                trackingCategories: this.TRACKING_CATEGORIES,
                organisationTenancySettingsId: tenancySettingsId,
                createdAt: moment().utc().format(),
                updatedAt: moment().utc().format(),
                country: 'GB',
                type: 'AGENT',
                rentancyMail: `info+${organisationId}@${ENV_CONFIG.MAIL_DOMAIN}`,
                clientBatchPayments: [5, 15],
                defaultCountryTaxBotId,
                utcMinuteOffset: 0,
                timeZoneName: '(UTC) Edinburgh, London',
                payoutVersion: payoutVersion ? payoutVersion : 'PROPERTY',
                balanceTransferContactUserId,
                openingBalanceContactUserId,
                depositSchemeLedgerContactUserId,
                ...(adminUserType === 'LOFTYPAY_ADMIN'
                    ? { subscriptionType: 'LOFTYPAY' }
                    : {}),
            },
        };
        if (ENV_CONFIG.ENV.includes('us')) {
            params.Item.country = 'US';
            params.Item.timeZoneName = '(UTC-06:00) Central America';
            params.Item.currency = 'USD';
            params.Item.utcMinuteOffset = -6;
        }

        return {
            Put: params,
        };
    }

    createTenancySettings(id, organisationId) {
        const params = {
            TableName: TABLE.TENANCY_SETTINGS_TABLE,
            Item: {
                id: id,
                rentCommission: 8,
                invoiceRentInAdvanceDays: 7,
                currency: this.getDefaultCurrency(),
                autoInvoiceRent: false,
                tenancySettingsOrganisationId: organisationId,
                feeType: 'NONE',
                organisationId: organisationId,
                fixedFee: 0,
                percentageFee: 0,
                sendInvoiceToTenant: false,
            },
        };

        return {
            Put: params,
        };
    }

    getDefaultCurrency() {
        if (ENV_CONFIG.ENV.includes('us')) {
            return 'USD';
        } else {
            return 'GBP';
        }
    }

    async createdepositSchemeContactUser(organisationId) {
        const depositSchemeLedgerContactUserId = uuid();
        const createdAt = moment().utc().format();
        const userItem = {
            id: depositSchemeLedgerContactUserId,
            companyName: 'Deposit Scheme',
            fname: 'Deposit',
            sname: 'Scheme',
            title: '',
            type: 'SYSTEM',
            internalNotes: 'This is a system contact. Do not delete.',
            public: true,
            currentOrganisation: organisationId,
            __typename: 'User',
            createdAt,
            createdIn: 'RENTANCY',
            updatedAt: createdAt,
        } as User;
        await this.userRepository.createItem(userItem);

        return depositSchemeLedgerContactUserId;
    }

    async createOpeningBalanceUser(organisationId) {
        const openingBalanceContactUserId = uuid();
        const name = 'Opening Balance';
        const createdAt = moment().utc().format();
        const userItem = {
            id: openingBalanceContactUserId,
            companyName: name,
            fname: name,
            title: '',
            type: 'SYSTEM',
            internalNotes: 'This is a system contact. Do not delete.',
            public: true,
            currentOrganisation: organisationId,
            __typename: 'User',
            createdAt,
            createdIn: 'RENTANCY',
            updatedAt: createdAt,
        } as User;
        await this.userRepository.createItem(userItem);
        return openingBalanceContactUserId;
    }

    async createBalanceTransferUser(organisationId) {
        const balanceTransferContactUserId = uuid();
        const name = 'Balance Transfer';
        const createdAt = moment().utc().format();
        const userItem = {
            id: balanceTransferContactUserId,
            companyName: name,
            fname: name,
            title: '',
            type: 'SYSTEM',
            internalNotes: 'This is a system contact. Do not delete.',
            public: true,
            currentOrganisation: organisationId,
            __typename: 'User',
            createdAt,
            createdIn: 'RENTANCY',
            updatedAt: createdAt,
        } as User;
        await this.userRepository.createItem(userItem);
        return balanceTransferContactUserId;
    }

    async createCountryUsers(organisationId) {
        const defaultCountryTaxBotId = uuid();
        await Promise.all(
            this.countryUsers.map(async (user) => {
                const userId =
                    user.country === 'GB' ? defaultCountryTaxBotId : uuid();
                const userItem = {
                    id: userId,
                    currentOrganisation: organisationId,
                    companyName: user.name,
                    updatedAt: moment().utc().format(),
                    type: 'SYSTEM',
                    internalNotes: 'This is a system contact. Do not delete.',
                    public: true,
                } as User;
                await this.userRepository.createItem(userItem);
            }),
        );
        return defaultCountryTaxBotId;
    }

    async processOrganisationId(orgId) {
        let counter = 1;
        let organisation = await this.organisationRepository.findById(orgId);
        while (organisation) {
            counter++;
            organisation = await this.organisationRepository.findById(
                orgId + '_' + counter,
            );
        }
        return counter === 1 ? orgId : orgId + '_' + counter;
    }

    async deleteOrganisation(organisationId: string, cognitoId: string) {
        await this.hasAccessToOrganisation(organisationId, cognitoId);
        return await this.deleteOrganisationData(organisationId);
    }

    private async deleteOrganisationData(organisationId) {
        try {
            this.logger.log(
                `Starting deletion for organisation ${organisationId}`,
            );

            await this.organisationRepository.deleteById(organisationId);
            this.logger.log(`Removed organisation with ID: ${organisationId}`);

            // Delete all related entities in parallel
            await Promise.all(
                this.ORGANISATION_ENTITIES.map((entity) =>
                    this.findAndDeleteOrganisationObjects(
                        entity.table,
                        entity.index,
                        entity.key,
                        organisationId,
                    ),
                ),
            );

            // Handle Organisation Users separately
            await this.handleOrganisationUsers(organisationId);

            // Handle Cognito Users leftovers
            await this.handleCognitoUsers(organisationId);

            this.logger.log(
                `Successfully deleted all organisation data for ${organisationId}`,
            );
        } catch (error) {
            this.logger.error(
                `Error deleting organisation ${organisationId}:`,
                error,
            );
        }
    }

    async handleCognitoUsers(organisationId) {
        try {
            const actions = [];

            for await (const user of this.userRepository.findAllCognitoUsersWithWorkspace(
                organisationId,
            )) {
                const eligibleWorkspaces = JSON.parse(
                    user.Attributes.find(
                        (attr) => attr.Name === 'custom:eligibleWorkspaces',
                    ).Value,
                );
                delete eligibleWorkspaces[organisationId];

                if (Object.keys(eligibleWorkspaces).length > 1) {
                    actions.push(
                        this.userRepository.updateCognitoUserOrganisations(
                            Object.keys(eligibleWorkspaces)[0],
                            user.Username,
                            eligibleWorkspaces,
                        ),
                    );
                } else {
                    actions.push(
                        this.userRepository.adminDeleteUser(user.Username),
                    );
                }
            }

            await Promise.all(actions);
        } catch (error) {
            this.logger.error(`Failed to clean cognito Users`, error);
        }
    }

    async handleOrganisationUsers(organisationId) {
        try {
            const orgUsers = await this.findOrganisationObjects(
                organisationId,
                'organisationUserOrganisationId',
                'OrganisationUser',
                INDEX.ORGANISATION_USER_ORGANISATION_INDEX,
            );

            await Promise.all(
                orgUsers.map((user) =>
                    this.changeCurrentOrganisationAndDeleteUser(
                        user.organisationUserUserId,
                        organisationId,
                    ),
                ),
            );
        } catch (error) {
            this.logger.error(`Failed to delete User table items`, error);
        }
    }

    private async findAndDeleteOrganisationObjects(
        table,
        index,
        key,
        organisationId,
    ) {
        try {
            const params = {
                TableName: `${table}-${ENV_CONFIG.API_RENTANCYAPI_GRAPHQLAPIIDOUTPUT}-${ENV_CONFIG.ENV}`,
                IndexName: index,
                KeyConditionExpression: `#${key} = :${key}`,
                ExpressionAttributeNames: { [`#${key}`]: key },
                ExpressionAttributeValues: { [`:${key}`]: organisationId },
            };

            const batchDelete = this.getBatchDeleteForTable(params.TableName);
            await this.query(params, batchDelete);
        } catch (error) {
            this.logger.error(`Failed to process items for ${table}`, error);
        }
    }

    async query(params, processingFunc) {
        let lastKey;
        let items = [];
        do {
            params.ExclusiveStartKey = lastKey;
            const result = await this.ddb.query(params);

            if (processingFunc) {
                await processingFunc(result.Items);
            }

            items = items.concat(result.Items);
            lastKey = result.LastEvaluatedKey;
        } while (lastKey);

        return items;
    }

    private getBatchDeleteForTable(tableName) {
        return async (ids) =>
            await this.batchDelete(
                tableName,
                ids.map((item) => item.id),
            );
    }

    private async batchDelete(table, ids) {
        if (ids.length === 0) {
            this.logger.log(`No items to delete in ${table}`);
            return;
        }

        const allUniqueIds = [...new Set(ids)];

        const chunks = this.chunkList(allUniqueIds, 125);

        for (const chunk of chunks) {
            const operations = this.chunkList(chunk, 25).map((chunk) => {
                return {
                    RequestItems: {
                        [table]: chunk.map((id) => ({
                            DeleteRequest: {
                                Key: {
                                    id,
                                },
                            },
                        })),
                    },
                };
            });

            await Promise.all(
                operations.map((operation) => this.ddb.batchWrite(operation)),
            );
        }

        this.logger.log(
            `Succesfully deleted ${allUniqueIds.length} items in ${table}`,
        );
    }

    private chunkList(list, chunkSize) {
        const chunks = [];
        for (let i = 0; i < list.length; i += chunkSize) {
            chunks.push(list.slice(i, i + chunkSize));
        }
        return chunks;
    }

    private async hasAccessToOrganisation(
        organisationId: string,
        cognitoId: string,
    ) {
        const organisation =
            await this.organisationRepository.findById(organisationId);

        if (!organisation) {
            throw new Error('Organisation not found');
        }
        const user = await this.userRepository.findByCognitoId(cognitoId);

        if (organisation.adminUser !== user.id) {
            throw new Error(
                'Only the creator of the organisation can delete it',
            );
        }
    }

    async isOrganisationExist(organisationId: string): Promise<boolean> {
        const organisation =
            await this.organisationRepository.findById(organisationId);
        return !!organisation;
    }
}
