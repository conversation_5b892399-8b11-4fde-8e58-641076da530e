import { User } from '../../../common/model/user';

export abstract class RolesDeterminatorAbstract {
    private readonly defaultMessage: string;
    private readonly tenantErrorMessage: string;
    protected readonly region: string;

    constructor(region) {
        if (this.constructor === RolesDeterminatorAbstract) {
            throw new TypeError('Cannot construct Abstract instances directly');
        }

        this.region = region;
        this.defaultMessage = 'Method not implemented';
        this.tenantErrorMessage = 'Tenants cannot create workspaces.';
    }

    abstract determineRoles(user): string[];

    verifyUserType(user: User) {
        if (user.type === 'TENANT') {
            throw new Error(this.tenantErrorMessage);
        }
    }
}
