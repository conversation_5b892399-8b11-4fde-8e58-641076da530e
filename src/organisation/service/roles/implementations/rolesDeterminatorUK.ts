import { RolesDeterminatorAbstract } from '../rolesDeterminator.abstract';

export class RolesDeterminatorUK extends RolesDeterminatorAbstract {
    constructor() {
        super('UK');
    }

    determineRoles(user): string[] {
        this.verifyUserType(user);
        const roles = ['ADMIN_AGENT'];
        if (user.type === 'LOFTYPAY_ADMIN') {
            roles.push('LOFTYPAY_ADMIN');
        }
        return roles;
    }
}
