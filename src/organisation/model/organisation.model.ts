import { ApiProperty } from '@nestjs/swagger';
import {
    ConversationTag,
    InvoiceTemplate,
    JournalPeriod,
    LedgerCode,
    NPMetadata,
    Organisation,
    OrganisationLedgerType,
    OrganisationSubscriptionType,
    OrganisationType,
    PayoutVersion,
    RentancyTrackingCategory,
} from '../../common/model/organisation';
import { DocusignTemplate } from '../../common/model/docusignTemplates';
import {
    LateRentAutoSettings,
    RentDueAutoSettings,
    TenancySettings,
} from '../../common/model/tenancySettings';
import {
    Checklist,
    OrganisationTaskChecklist,
} from '../../common/model/organisationTaskChecklist';
import { Reminder } from '../../common/model/reminder';
import { TaskReminder } from '../../common/model/taskReminder';

export class TenancySettingsVO {
    constructor(tenancySettings: TenancySettings) {
        this.id = tenancySettings.id;
        this.currency = tenancySettings.currency;
        this.feeType = tenancySettings.feeType;
        this.fixedFee = tenancySettings.fixedFee;
        this.rentCommission = tenancySettings.rentCommission;
        this.percentageFee = tenancySettings.percentageFee;
        this.autoInvoiceRent = tenancySettings.autoInvoiceRent;
        this.invoiceRentInAdvanceDays =
            tenancySettings.invoiceRentInAdvanceDays;
        this.autoApprovalBill = tenancySettings.autoApprovalBill;
        this.feeLedgerCode = tenancySettings.feeLedgerCode;
        this.lateRentAutoSettings = tenancySettings.lateRentAutoSettings;
        this.rentDueAutoSettings = tenancySettings.rentDueAutoSettings;
    }

    @ApiProperty()
    id: string;

    @ApiProperty()
    currency: string;

    @ApiProperty()
    feeType: string;

    @ApiProperty()
    fixedFee: number;

    @ApiProperty()
    rentCommission: number;

    @ApiProperty()
    percentageFee: number;

    @ApiProperty()
    autoInvoiceRent: boolean;

    @ApiProperty()
    invoiceRentInAdvanceDays: number;

    @ApiProperty()
    autoApprovalBill: boolean;

    @ApiProperty({ type: () => LedgerCode })
    feeLedgerCode: LedgerCode;

    @ApiProperty({ type: () => LateRentAutoSettings })
    lateRentAutoSettings: LateRentAutoSettings;

    @ApiProperty({ type: () => RentDueAutoSettings })
    rentDueAutoSettings: RentDueAutoSettings;
}
export class EligibleOrganisationVO {
    @ApiProperty()
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty()
    createdAt: Date;
    @ApiProperty()
    logo: string;
    @ApiProperty()
    emailId: string;
    @ApiProperty()
    email: string;
    @ApiProperty()
    phone: string;
}

export class ExpireFlagDTO {
    @ApiProperty({ required: true })
    organisationIds: string[];
    @ApiProperty({ required: true })
    expiredFlag: string;
}

export class OrganisationLedgerCodeVO {
    @ApiProperty()
    organisationId: string;
    @ApiProperty()
    tenanciesUpdateInProgress: boolean;
    @ApiProperty()
    success: boolean;
}

export class OrganisationBasicVO {
    constructor(organisation: Organisation) {
        this.id = organisation.id;
        this.country = organisation.country;
        this.name = organisation.name;
        this.createdAt = organisation.createdAt;
        this.adminUser = organisation.adminUser;
        this.logo = organisation.logo;
        this.dailyNotificationsEnabled = organisation.dailyNotificationsEnabled;
        this.docusignAccountId = organisation.docusignAccountId;
        this.stripeFreeTrialCompleted = organisation.stripeFreeTrialCompleted;
        this.stripeChargeDayOfTheMonth = organisation.stripeChargeDayOfTheMonth;
        this.payer = organisation.payer;
        this.rightmoveBranchID = organisation.rightmoveBranchID;
        this.stripeHasOutstandingPaymentIntent =
            organisation.stripeHasOutstandingPaymentIntent;
        this.stripePaymentMethodId = organisation.stripePaymentMethodId;
        this.type = organisation.type;
        this.zillowIntegration = organisation.zillowIntegration;
        this.zooplaBranchID = organisation.zooplaBranchID;
        this.currency = organisation.currency;
        this.botUser = organisation.botUser;
        this.landlordStatementTemplateType =
            organisation.landlordStatementTemplateType;
        this.whatsAppAddress = organisation.whatsAppAddress;
        this.expiredFlag = organisation.expiredFlag;
        this.conversationTags = organisation.conversationTags;
        this.ledgerType = organisation.ledgerType;
        this.subscriptionType = organisation.subscriptionType;
    }
    @ApiProperty()
    id: string;

    @ApiProperty()
    country: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    adminUser: string;

    @ApiProperty()
    logo: string;

    @ApiProperty()
    dailyNotificationsEnabled: boolean;

    @ApiProperty()
    docusignAccountId: string;

    @ApiProperty()
    stripeFreeTrialCompleted: boolean;

    @ApiProperty()
    stripeChargeDayOfTheMonth: number;

    @ApiProperty()
    payer: boolean;

    @ApiProperty()
    rightmoveBranchID: string;

    @ApiProperty()
    stripeHasOutstandingPaymentIntent: boolean;

    @ApiProperty()
    stripePaymentMethodId: string;

    @ApiProperty({
        enum: OrganisationType,
    })
    type: OrganisationType;

    @ApiProperty()
    zillowIntegration: boolean;

    @ApiProperty()
    zooplaBranchID: string;

    @ApiProperty()
    currency: string;

    @ApiProperty({ type: () => [DocusignTemplateVO] })
    docusignTemplates: DocusignTemplateVO[];

    @ApiProperty({ type: () => TenancySettingsVO })
    tenancySettings: TenancySettingsVO;

    @ApiProperty({ type: () => [ConversationTag] })
    conversationTags: ConversationTag[];

    @ApiProperty()
    botUser: string;

    @ApiProperty()
    landlordStatementTemplateType: string;

    @ApiProperty()
    whatsAppAddress: string;

    @ApiProperty()
    expiredFlag: string;

    @ApiProperty()
    ledgerType: OrganisationLedgerType;

    @ApiProperty()
    subscriptionType: OrganisationSubscriptionType;
}

export class OrganisationVO extends OrganisationBasicVO {
    constructor(organisation: Organisation) {
        super(organisation);
        this.emailId = organisation.emailId;
        this.website = organisation.website;
        this.clientBatchPayments = organisation.clientBatchPayments;
        this.clientBatchPaymentsOnDemand =
            organisation.clientBatchPaymentsOnDemand;
        this.phone = organisation.phone;
        this.addressLine1 = organisation.addressLine1;
        this.addressLine2 = organisation.addressLine2;
        this.addressLine3 = organisation.addressLine3;
        this.city = organisation.city;
        this.postcode = organisation.postcode;
        this.state = organisation.state;
        this.invoiceTemplates = organisation.invoiceTemplates;
        this.rentancyApiKey = organisation.rentancyApiKey;
        this.addCommissionBillVatInclusive =
            organisation.addCommissionBillVatInclusive;
        this.addCommissionBillVat = organisation.addCommissionBillVat;
        this.commissionBillVatNumber = organisation.commissionBillVatNumber;
        this.transUnionConnectionEnabled =
            organisation.transUnionConnectionEnabled;
        this.transUnionLandlordId = organisation.transUnionLandlordId;
        this.receiveMarketingEmailConsent =
            organisation.receiveMarketingEmailConsent;
        this.hideImportExportPage = organisation.hideImportExportPage;
        this.nationalProcessingMetadata =
            organisation.nationalProcessingMetadata;
        this.email = organisation.email;
        this.emailStatus = organisation.emailStatus;
        this.customMail = organisation.customMail;
        this.rentancyMail = organisation.rentancyMail;
        this.utcMinuteOffset = organisation.utcMinuteOffset;
        this.timeZoneName = organisation.timeZoneName;
        this.enableJournal = organisation.enableJournal;
        this.journalDate = organisation.journalDate;
        this.journalPeriod = organisation.journalPeriod;
        this.payoutVersion = organisation.payoutVersion;
        this.trackingCategories = organisation.trackingCategories;
        this.ledgerCodes = organisation.ledgerCodes;
    }
    @ApiProperty()
    emailId: string;
    @ApiProperty()
    website: string;
    @ApiProperty()
    clientBatchPayments: number[];
    @ApiProperty()
    clientBatchPaymentsOnDemand: boolean;
    @ApiProperty()
    phone: string;
    @ApiProperty()
    addressLine1: string;
    @ApiProperty()
    addressLine2: string;
    @ApiProperty()
    addressLine3: string;
    @ApiProperty()
    city: string;
    @ApiProperty()
    postcode: string;
    @ApiProperty()
    state: string;
    @ApiProperty({ type: () => [InvoiceTemplate] })
    invoiceTemplates: InvoiceTemplate[];
    @ApiProperty()
    rentancyApiKey: string;
    @ApiProperty()
    addCommissionBillVatInclusive: boolean;
    @ApiProperty()
    addCommissionBillVat: boolean;
    @ApiProperty()
    commissionBillVatNumber: string;
    @ApiProperty()
    transUnionConnectionEnabled: boolean;
    @ApiProperty()
    transUnionLandlordId: string;
    @ApiProperty()
    receiveMarketingEmailConsent: boolean;
    @ApiProperty()
    hideImportExportPage: boolean;
    @ApiProperty({ type: () => NPMetadata })
    nationalProcessingMetadata: NPMetadata;
    @ApiProperty()
    email: string;
    @ApiProperty()
    emailStatus: string;
    @ApiProperty()
    customMail: string;
    @ApiProperty()
    rentancyMail: string;
    @ApiProperty()
    utcMinuteOffset: number;
    @ApiProperty()
    timeZoneName: string;
    @ApiProperty()
    enableJournal: boolean;
    @ApiProperty()
    journalDate: number;
    @ApiProperty()
    journalPeriod: JournalPeriod;
    @ApiProperty()
    payoutVersion: PayoutVersion;
    @ApiProperty()
    trackingCategories: RentancyTrackingCategory[];
    @ApiProperty()
    ledgerCodes: LedgerCode[];
}

export class DocusignTemplateVO {
    constructor(docusignTemplate: DocusignTemplate) {
        this.id = docusignTemplate.id;
        this.name = docusignTemplate.name;
        this.docusignTemplateId = docusignTemplate.docusignTemplateId;
        this.roles = docusignTemplate.roles;
    }
    @ApiProperty()
    id: string;

    @ApiProperty()
    name: string;

    @ApiProperty()
    docusignTemplateId: string;

    @ApiProperty()
    roles: string[];
}

export class OrganisationTaskChecklistVO {
    constructor(item: OrganisationTaskChecklist) {
        this.id = item.id;
        this.name = item.name;
        this.defaultChecklists = item.defaultChecklists.map(
            (i) => new ChecklistVO(i),
        );
        this.createdAt = item.createdAt;
        this.updatedAt = item.updatedAt;
    }
    @ApiProperty()
    id: string;
    @ApiProperty()
    name: string;
    @ApiProperty({ type: () => [ChecklistVO] })
    defaultChecklists: ChecklistVO[];
    @ApiProperty()
    createdAt: string;
    @ApiProperty()
    updatedAt: string;
}

export class ChecklistVO {
    constructor(item: Checklist) {
        this.name = item.name;
        this.status = item.status;
    }
    @ApiProperty()
    name: string;
    @ApiProperty()
    status: boolean;
}

export class OrganisationTaskReminderVO {
    @ApiProperty({ type: () => [Reminder] })
    reminders: Reminder[];
    @ApiProperty({ type: () => [TaskReminder] })
    taskReminders: TaskReminder[];
}

export class CreateOrganisationVO {
    @ApiProperty()
    organisationName: string;
    @ApiProperty({ enum: PayoutVersion })
    payoutVersion: PayoutVersion;
    @ApiProperty()
    utmCustomerAttributionSource: string;
    @ApiProperty()
    organisationId?: string;
}
