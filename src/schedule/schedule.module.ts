import { Module } from '@nestjs/common';
import { ScheduleService } from './service/schedule.service';
import { ContractModule } from '../contract/contract.module';
import { ScheduleController } from './controller/api/schedule.controller';
import { EventsEmitterService } from '../common/service/eventsEmitter.service';

@Module({
  imports: [ContractModule],
  providers: [ScheduleService, EventsEmitterService],
  controllers: [ScheduleController]
})
export class TaskModule {}
