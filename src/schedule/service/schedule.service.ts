import { Injectable, Logger } from '@nestjs/common';
import { ContractService } from 'src/contract/service/contract.service';
import { EotService } from 'src/contract/service/eot.service';
import {
    getPropertiesBatch,
    getUserByIds,
    listOrganisationList,
    listHistoryUserLoginRecord,
    deleteHistoryUserLoginRecord,
} from 'src/common/service/ddb.service';
import { v1 as uuidV1 } from 'uuid';
import { UpdateEndOfTenancyDto } from '../../contract/model/eot.model';
import { EndOfTenancyStage } from 'src/contract/enum/eotStage';
import { EndOfTenancyStatus } from 'src/contract/enum/eotStatus';
import { EventsEmitterService } from '../../common/service/eventsEmitter.service';
import { createTenancyExpiringEvent } from '@rentancy-com/loftyworks-events';
import { UserLoginSessionRepository } from 'src/common/repository/userLoginSession';

@Injectable()
export class ScheduleService {
    private readonly logger = new Logger(ScheduleService.name);

    constructor(
        private contractService: ContractService,
        private eotService: EotService,
        private eventsEmitter: EventsEmitterService,
        private userLoginSessionRepository: UserLoginSessionRepository,
    ) {}

    public async generateEotDailyData() {
        const today = new Date();
        const dateAfter90Days = new Date();
        dateAfter90Days.setDate(dateAfter90Days.getDate() + 90);
        const formattedFromEndDate = today.toISOString().split('T')[0];
        const formattedToEndDate = dateAfter90Days.toISOString().split('T')[0];

        const organisationList = await listOrganisationList();
        if (organisationList && organisationList.length > 0) {
            for (const organisation of organisationList) {
                const tenancyList =
                    await this.contractService.searchTenanciesByDate(
                        organisation?.id,
                        formattedFromEndDate,
                        formattedToEndDate,
                    );
                const existEotTenancyInfo =
                    await this.eotService.getAllTenancyIdByOrganisationId(
                        organisation?.id,
                    );
                const existTenancyIds = existEotTenancyInfo
                    .map((item: any) => item.tenancyId)
                    .filter((id: any) => id !== undefined && id !== null);
                if (tenancyList && tenancyList.length > 0) {
                    const eotList = [];
                    for (const tenancy of tenancyList) {
                        if (
                            existTenancyIds.includes(tenancy.id) ||
                            tenancy.status != 'ACTIVE'
                        ) {
                            continue;
                        }
                        const eotModel: any = this.convertTenancyToEot(
                            tenancy,
                            organisation,
                        );
                        if (!eotModel.landlordId || !eotModel.tenantId) {
                            continue;
                        }
                        eotList.push(eotModel);
                    }
                    if (eotList && eotList.length > 0) {
                        const domainEvents = [];
                        const tenantUserIdList = [
                            ...new Set(eotList.map((eot: any) => eot.tenantId)),
                        ];
                        const propertyIdList = [
                            ...new Set(
                                eotList.map((eot: any) => eot.propertyId),
                            ),
                        ];
                        const [tenantUserList, propertyList] =
                            await Promise.all([
                                getUserByIds(tenantUserIdList),
                                getPropertiesBatch(propertyIdList),
                            ]);
                        const tenantIdToNameMap = tenantUserList.reduce(
                            (map: any, user: any) => {
                                map[user.id] = user.fname + ' ' + user.sname;
                                return map;
                            },
                            {},
                        );
                        const propertyMap = propertyList.reduce(
                            (map: any, property: any) => {
                                map[property.id] = property;
                                return map;
                            },
                            {},
                        );
                        eotList.forEach((eot: any) => {
                            eot.primaryTenantName =
                                tenantIdToNameMap[eot.tenantId];
                            eot.addressLine2 =
                                propertyMap[eot.propertyId]?.addressLine2;
                            eot.addressLine3 =
                                propertyMap[eot.propertyId]?.addressLine3;
                            eot.city = propertyMap[eot.propertyId]?.city;
                            eot.country = propertyMap[eot.propertyId]?.country;
                            eot.postcode =
                                propertyMap[eot.propertyId]?.postcode;
                            domainEvents.push(
                                createTenancyExpiringEvent({
                                    propertyId: eot.propertyId,
                                    subjectId: eot.tenancyId,
                                    eotId: eot.id,
                                    organisationId: eot.organisationId,
                                }),
                            );
                        });
                        await this.eotService.batchCreateEot(eotList);
                        await this.eventsEmitter.putEvents(domainEvents);
                    }
                }
            }
        }
    }

    public async updateEotExpiredData() {
        const expiredEotList = await this.eotService.getExpiredEotList();
        if (expiredEotList && expiredEotList.length > 0) {
            for (const eot of expiredEotList) {
                const updateEotDto = {} as UpdateEndOfTenancyDto;
                updateEotDto.id = eot.id;
                updateEotDto.stage = EndOfTenancyStage.EXPIRED;
                updateEotDto.status = EndOfTenancyStatus.ARCHIVED;
                await this.eotService.updateEot(updateEotDto);
            }
        }
    }

    public async processContractRenewal() {
        const expiredEotList =
            await this.eotService.getCompletedAndExpiredEotList();
        if (expiredEotList && expiredEotList.length > 0) {
            for (const eotInfo of expiredEotList) {
                const tenancyId = eotInfo.tenancyId;
                const tenancyInfo =
                    await this.contractService.getTenancyById(tenancyId);
                if (tenancyInfo.status !== EndOfTenancyStatus.ARCHIVED) {
                    const archivedTenancy = {
                        id: tenancyId,
                        status: EndOfTenancyStatus.ARCHIVED,
                    };
                    await this.contractService.updateTenancyById(
                        tenancyId,
                        archivedTenancy,
                    );
                    await this.contractService.renewTenancy(
                        eotInfo,
                        tenancyInfo,
                    );
                }
            }
        }
    }

    private convertTenancyToEot(tenancy: any, organisation: any): any {
        return {
            id: uuidV1(),
            tenancyId: tenancy.id,
            propertyId: tenancy.propertyId,
            address: tenancy.address,
            organisationId: organisation?.id,
            landlordId: tenancy.landlords?.[0] ?? null,
            tenantId: tenancy.tenants?.[0] ?? null,
            endDate: new Date(tenancy.endDate).toISOString(),
            stage: EndOfTenancyStage.WAITING_TO_START,
            status: EndOfTenancyStatus.ACTIVE,
            reference: tenancy.reference,
            oldPrice: tenancy?.rent
                ? (Number(tenancy.rent) / 100).toString()
                : '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
    }

    public async deleteHistoryUserLoginRecord() {
        try {
            const scanResult = await listHistoryUserLoginRecord();
            this.logger.log(
                `userLoginRecordHandler:scanResult.length=${scanResult.length}`,
            );
            scanResult.forEach(async (item) => {
                await deleteHistoryUserLoginRecord(item.id);
            });
        } catch (error) {
            this.logger.error('userLoginRecordHandler:', error);
        }
    }

    public async deleteHistoryUserLoginSession() {
        try {
            const scanResult =
                await this.userLoginSessionRepository.listHistoryUserLoginSession();
            this.logger.log(
                `userLoginSessionHandler:scanResult.length=${scanResult.length}`,
            );
            scanResult.forEach(async (item) => {
                await this.userLoginSessionRepository.deleteById(item.id);
            });
        } catch (error) {
            this.logger.error('userLoginSessionHandler:', error);
        }
    }
}
