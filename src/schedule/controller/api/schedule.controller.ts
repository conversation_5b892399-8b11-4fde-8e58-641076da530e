import { <PERSON>, Get, Logger} from '@nestjs/common';
import { ScheduleService } from 'src/schedule/service/schedule.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'src/common/service/prisma.service';

@Controller("api/v1/schedule")
export class ScheduleController{
    private logger = new Logger('ScheduleController', { timestamp: true });
    constructor(
        private scheduleService: ScheduleService,
        private prismaService: PrismaService
    ) {}

    private EOT_GENERATE_TASK_LOCK_KEY = 10001;
    private EOT_UPDATE_TASK_LOCK_KEY = 10002;
    private USER_LOGIN_RECORD_TASK_LOCK_KEY = 10003;

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    public async generateEotDailyData() {
        const lockAcquired = await this.prismaService.acquireLock(this.EOT_GENERATE_TASK_LOCK_KEY);
        if (!lockAcquired) {
            this.logger.log('Another instance is already running the scheduled task.');
            return;
        }
        try {
            this.logger.log(`start generateEotDailyData `)
            await this.scheduleService.generateEotDailyData();
            this.logger.log(`end generateEotDailyData `)
        }catch (error) {
            this.logger.log(`eot generateEotDailyData error: ${error}`)
        } finally {
            await this.prismaService.releaseLock(this.EOT_GENERATE_TASK_LOCK_KEY);
        }
    }

    @Get('eot_generate_task')
    public async generateEotDailyDataTrigger() {
        await this.generateEotDailyData();
    }

    @Get('eot_update_task')
    public async eotExpiredHandlerTrigger() {
        await this.eotExpiredHandler();
    }


    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    public async eotExpiredHandler() {
        const lockAcquired = await this.prismaService.acquireLock(this.EOT_UPDATE_TASK_LOCK_KEY);
        if (!lockAcquired) {
            this.logger.log('Another instance is already running the scheduled task.');
            return;
        }
        try {
            this.logger.log(`start eotExpiredHandler `)
            await this.scheduleService.updateEotExpiredData();
            await this.scheduleService.processContractRenewal();
            this.logger.log(`end eotExpiredHandler `)
        }catch (error) {
            this.logger.log(`eotExpiredHandler error: ${error}`)
        }finally {
            await this.prismaService.releaseLock(this.EOT_UPDATE_TASK_LOCK_KEY);
        }
    }

    @Cron(CronExpression.EVERY_DAY_AT_1AM)
    //@Cron(CronExpression.EVERY_MINUTE)
    public async userLoginRecordHandler() {
        const lockAcquired = await this.prismaService.acquireLock(this.USER_LOGIN_RECORD_TASK_LOCK_KEY);
        if (!lockAcquired) {
            this.logger.log('userLoginRecordHandler:Another instance is already running the scheduled task.');
            return;
        }
        try {
            this.logger.log(`userLoginRecordHandler:start`);
            await this.scheduleService.deleteHistoryUserLoginRecord();
            this.logger.log(`userLoginRecordHandler:end`);
        }catch (error) {
            this.logger.log(`userLoginRecordHandler:error=${error}`)
        }finally {
            await this.prismaService.releaseLock(this.USER_LOGIN_RECORD_TASK_LOCK_KEY);
        }
    }

}
