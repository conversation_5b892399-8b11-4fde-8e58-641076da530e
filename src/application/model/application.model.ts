import { ApiProperty } from '@nestjs/swagger';

export enum ApplicationStatus {
    PENDING = 'Pending',
    CANCELLED = 'Cancelled',
    COMPLETED = 'Completed',
}

export enum ApplicationTaskStatus {
    TODO = 'ToDo',
    FAILED = 'Failed',
    COMPLETED = 'Completed',
}

export class ApplicationCreateDTO {
    @ApiProperty({ required: true })
    propertyId?: string;

    @ApiProperty({ required: true })
    tenancyId?: string;

    @ApiProperty({ required: true })
    organisationId: string;

    @ApiProperty()
    createdBy: string;
}

export class ApplicationUpdateDTO {
    @ApiProperty({ required: true })
    id: string;

    @ApiProperty({ required: false })
    managers?: string[];

    @ApiProperty({ required: false })
    status?: string;

    @ApiProperty({ required: false })
    remark?: string;

    @ApiProperty()
    updatedBy: string;
}

export class ApplicationQueryDTO {
    @ApiProperty({ required: true })
    page: any;

    @ApiProperty({ required: true })
    size: any;

    @ApiProperty({ required: false })
    keyword?: string;

    @ApiProperty({ required: false })
    status?: ApplicationStatus;

    @ApiProperty({ required: true })
    organisationId: string;

    @ApiProperty({ required: false })
    userId?: string;
}

export class Application {
    @ApiProperty()
    id: string;

    @ApiProperty()
    title: string;

    @ApiProperty()
    description: string;

    @ApiProperty({ enum: ApplicationStatus })
    status: ApplicationStatus;

    @ApiProperty({ required: false })
    propertyId?: string;

    @ApiProperty({ required: false })
    tenancyId?: string;

    @ApiProperty({ required: false })
    userId?: string;

    @ApiProperty()
    organisationId: string;

    @ApiProperty({ required: false })
    metadata?: Record<string, any>;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    updatedAt: Date;
}
