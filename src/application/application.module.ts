import { Module, forwardRef } from '@nestjs/common';
import { ApplicationController } from './controller/application.controller';
import { ApplicationService } from './service/application.service';
import { CommonModule } from '../common/common.module';
import { PropertyModule } from '../property/property.module';
import { ContractModule } from '../contract/contract.module';
import { UserModule } from '../user/user.module';

@Module({
    imports: [
        CommonModule,
        forwardRef(() => PropertyModule),
        ContractModule,
        UserModule,
    ],
    controllers: [ApplicationController],
    providers: [ApplicationService],
    exports: [ApplicationService],
})
export class ApplicationModule {}
