import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/service/prisma.service';
import {
    ApplicationCreateDTO,
    ApplicationQueryDTO,
    ApplicationStatus,
    ApplicationUpdateDTO,
} from '../model/application.model';
import { v1 as uuidV1 } from 'uuid';
import { Page } from '../../common/util/requestUtil';
import { PropertyRepository } from '../../common/repository/property';
import { TenancyRepository } from '../../common/repository/tenancy';
import { UserRepository } from '../../common/repository/user';
import { PropertyService } from '../../property/service/property.service';
import { UserService } from '../../user/service/user.service';

@Injectable()
export class ApplicationService {
    private readonly logger = new Logger('ApplicationService', {
        timestamp: true,
    });

    constructor(
        private readonly prisma: PrismaService,
        private readonly propertyRepository: PropertyRepository,
        private readonly tenancyRepository: TenancyRepository,
        private readonly propertyService: PropertyService,
        private readonly userService: UserService,
        private readonly userRepository: UserRepository,
    ) {}

    async createApplication(dto: ApplicationCreateDTO): Promise<any> {
        try {
            const [propertyInfo, tenancyInfo, applicationTaskDefinitions] =
                await Promise.all([
                    this.propertyRepository.findById(dto.propertyId),
                    this.tenancyRepository.findById(dto.tenancyId),
                    this.prisma.applicationTaskDefinition.findMany(),
                ]);
            const application = await this.prisma.tenancyApplication.create({
                data: {
                    id: uuidV1(),
                    propertyId: dto.propertyId,
                    tenancyId: dto.tenancyId,
                    status: ApplicationStatus.PENDING,
                    primaryLandlordId: propertyInfo.primaryLandlordId,
                    landlords: propertyInfo.landlords,
                    primaryTenantId: tenancyInfo.primaryTenant,
                    tenants: tenancyInfo.tenants,
                    managers: propertyInfo.managers,
                    moveInDate: tenancyInfo.startDate,
                    isDeleted: false,
                    organisationId: dto.organisationId,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            });

            //init application tasks
            await Promise.all(
                applicationTaskDefinitions.map(async (taskDefinition) => {
                    await this.prisma.applicationTask.create({
                        data: {
                            id: uuidV1(),
                            applicationId: application.id,
                            taskDefinitionId: taskDefinition.id,
                            status: ApplicationStatus.PENDING,
                            createdAt: new Date(),
                            updatedAt: new Date(),
                        },
                    });
                }),
            );
        } catch (error) {
            this.logger.error(
                `Error creating application: ${error.message}`,
                error.stack,
            );
            throw new Error('Failed to create application');
        }
    }

    async getApplicationById(id: string): Promise<any> {
        try {
            const [application, applicationTaskDefinitions, applicationTasks] =
                await Promise.all([
                    this.prisma.tenancyApplication.findUnique({
                        where: { id },
                    }),
                    this.prisma.applicationTaskDefinition.findMany(),
                    this.prisma.applicationTask.findMany({
                        where: { applicationId: id },
                    }),
                ]);

            const applicationTaskDefinitionMap = new Map(
                applicationTaskDefinitions.map((definition) => [
                    definition.id,
                    definition,
                ]),
            );

            const applicationWithTasks: any = {
                ...application,
                tasks: applicationTasks.map((task) => ({
                    id: task.id,
                    name: applicationTaskDefinitionMap.get(
                        task.taskDefinitionId,
                    )?.taskName,
                    category: applicationTaskDefinitionMap.get(
                        task.taskDefinitionId,
                    )?.category,
                    status: task.status,
                })),
            };

            const [propertyInfo, userInfo] = await Promise.all([
                this.propertyRepository.findById(application.propertyId),
                this.userRepository.findById(application.primaryTenantId),
            ]);

            applicationWithTasks.propertyName = propertyInfo.addressLine1;
            applicationWithTasks.tenantName = `${userInfo.fname} ${userInfo.sname}`;

            return applicationWithTasks;
        } catch (error) {
            this.logger.error(
                `Error getting application by ID: ${error.message}`,
                error.stack,
            );
            throw new Error('Failed to get application');
        }
    }

    async updateApplication(dto: ApplicationUpdateDTO): Promise<void> {
        try {
            await this.prisma.tenancyApplication.update({
                where: { id: dto.id },
                data: {
                    ...(dto.status && { status: dto.status }),
                    ...(dto.remark && { remark: dto.remark }),
                    updatedAt: new Date(),
                },
            });
        } catch (error) {
            this.logger.error(
                `Error updating application: ${error.message}`,
                error.stack,
            );
            throw new Error('Failed to update application');
        }
    }

    async deleteApplication(id: string): Promise<boolean> {
        try {
            await this.prisma.tenancyApplication.update({
                where: { id },
                data: {
                    isDeleted: true,
                    updatedAt: new Date(),
                },
            });

            return true;
        } catch (error) {
            this.logger.error(
                `Error deleting application: ${error.message}`,
                error.stack,
            );
            throw new Error('Failed to delete application');
        }
    }

    async getApplicationsByQuery(query: ApplicationQueryDTO): Promise<Page> {
        try {
            const filter: any = {
                organisationId: query.organisationId,
                isDeleted: false,
            };
            if (query.keyword) {
                const [propertyInfos, userInfos] = await Promise.all([
                    this.propertyService.searchOrgPropertiesByName(
                        query.organisationId,
                        query.keyword,
                    ),
                    this.userService.searchUsers({
                        organisationId: query.organisationId,
                        name: query.keyword,
                    }),
                ]);
                const propertyIds = propertyInfos.map(
                    (propertyInfo) => propertyInfo.id,
                );
                const userIds = userInfos.contactList.map(
                    (userInfo) => userInfo.id,
                );
                filter.OR = [];
                if (propertyIds.length > 0) {
                    filter.OR.push({
                        propertyId: {
                            in: propertyIds,
                        },
                    });
                }
                if (userIds.length > 0) {
                    filter.OR.push({
                        tenants: {
                            hasSome: userIds,
                        },
                    });
                }
                if (filter.OR.length === 0) {
                    delete filter.OR;
                }
            }
            if (query.status) {
                filter.status = query.status;
            }
            const [total, data] = await Promise.all([
                this.prisma.tenancyApplication.count({
                    where: filter,
                }),
                this.prisma.tenancyApplication.findMany({
                    where: filter,
                    skip: (query.page - 1) * query.size,
                    take: query.size,
                }),
            ]);

            const tenancyApplicationIds = data.map(
                (tenancyApplication) => tenancyApplication.id,
            );
            const propertyIds = data.map(
                (tenancyApplication) => tenancyApplication.propertyId,
            );
            const tenantIds = data
                .map((tenancyApplication) => tenancyApplication.primaryTenantId)
                .flat();
            const [
                applicationTasks,
                applicationTaskDefinitions,
                properties,
                tenants,
            ] = await Promise.all([
                this.prisma.applicationTask.findMany({
                    where: {
                        applicationId: {
                            in: tenancyApplicationIds,
                        },
                    },
                }),
                this.prisma.applicationTaskDefinition.findMany(),
                this.propertyRepository.findByIds(propertyIds),
                this.userRepository.findByIds(tenantIds),
            ]);
            const applicationTaskDefinitionMap = new Map(
                applicationTaskDefinitions.map((definition) => [
                    definition.id,
                    definition,
                ]),
            );
            const applicationTaskListMap = new Map(
                data.map((application) => [
                    application.id,
                    applicationTasks.filter(
                        (task) => task.applicationId === application.id,
                    ),
                ]),
            );
            const propertyAddressMap = new Map(
                properties.map((property) => [
                    property.id,
                    property.addressLine1,
                ]),
            );
            const tenantNameMap = new Map(
                tenants.map((tenant) => [
                    tenant.id,
                    `${tenant.fname} ${tenant.sname}`,
                ]),
            );

            data.forEach((application: any) => {
                const tasks: any = applicationTaskListMap.get(application.id);
                application.tasks = tasks.map((task: any) => ({
                    id: task.id,
                    name: applicationTaskDefinitionMap.get(
                        task.taskDefinitionId,
                    )?.taskName,
                    category: applicationTaskDefinitionMap.get(
                        task.taskDefinitionId,
                    )?.category,
                    status: task.status,
                }));
                application.propertyName = propertyAddressMap.get(
                    application.propertyId,
                );
                application.primaryTenantName = tenantNameMap.get(
                    application.primaryTenantId,
                );
            });

            return new Page(total, data);
        } catch (error) {
            this.logger.error(
                `Error querying applications: ${error.message}`,
                error.stack,
            );
            throw new Error('Failed to query applications');
        }
    }

    async updateApplicationTask(
        taskId: string,
        dto: { status: string; detail: any; documentIds: string[] },
    ) {
        return this.prisma.applicationTask.update({
            where: { id: taskId },
            data: {
                ...(dto.status && { status: dto.status }),
                ...(dto.detail && { detailsJson: dto.detail }),
                ...(dto.documentIds && { documents: dto.documentIds }),
                updatedAt: new Date(),
            },
        });
    }

    async getApplicationsByPropertyId(propertyId: string): Promise<any[]> {
        try {
            const applications = await this.prisma.tenancyApplication.findMany({
                where: {
                    propertyId: propertyId,
                    isDeleted: false,
                },
            });

            const applicationIds = applications.map(
                (application) => application.id,
            );
            const propertyIds = applications.map(
                (application) => application.propertyId,
            );
            const primaryTenantIds = applications.map(
                (application) => application.primaryTenantId,
            );
            const [taskDefinitions, applicationTasks, properties, tenants] =
                await Promise.all([
                    this.prisma.applicationTaskDefinition.findMany(),
                    this.prisma.applicationTask.findMany({
                        where: {
                            applicationId: {
                                in: applicationIds,
                            },
                        },
                    }),
                    this.propertyRepository.findByIds(propertyIds),
                    this.userRepository.findByIds(primaryTenantIds),
                ]);
            const applicationTaskDefinitionMap = new Map(
                taskDefinitions.map((definition) => [
                    definition.id,
                    definition,
                ]),
            );
            const applicationTaskListMap = new Map(
                applications.map((application) => [
                    application.id,
                    applicationTasks.filter(
                        (task) => task.applicationId === application.id,
                    ),
                ]),
            );
            const propertyAddressMap = new Map(
                properties.map((property: any) => [
                    property.id,
                    property.address,
                ]),
            );
            const tenantNameMap = new Map(
                tenants.map((tenant: any) => [
                    tenant.id,
                    `${tenant.fname} ${tenant.sname}`,
                ]),
            );
            applications.forEach((application: any) => {
                const tasks: any = applicationTaskListMap.get(application.id);
                application.tasks = tasks.map((task: any) => ({
                    id: task.id,
                    name: applicationTaskDefinitionMap.get(
                        task.taskDefinitionId,
                    )?.taskName,
                    category: applicationTaskDefinitionMap.get(
                        task.taskDefinitionId,
                    )?.category,
                    status: task.status,
                }));
                application.propertyName = propertyAddressMap.get(
                    application.propertyId,
                );
                application.primaryTenantName = tenantNameMap.get(
                    application.primaryTenantId,
                );
            });

            return applications;
        } catch (error) {
            this.logger.error(
                `Error querying applications by property ID: ${error.message}`,
                error.stack,
            );
            throw new Error('Failed to query applications by property ID');
        }
    }
}
