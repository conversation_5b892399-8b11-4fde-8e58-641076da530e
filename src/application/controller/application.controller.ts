import {
    Body,
    Controller,
    Delete,
    Get,
    Logger,
    Param,
    Post,
    Put,
    Query,
    Req,
    Res,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ApplicationService } from '../service/application.service';
import {
    ApplicationCreateDTO,
    ApplicationQueryDTO,
    ApplicationUpdateDTO,
} from '../model/application.model';
import {
    LwRequest,
    responseError,
    responseOk,
} from '../../common/util/requestUtil';
import { ResponseCode } from '../../common/constant/responseCode';

@ApiTags('Application')
@Controller('api/v1/application')
export class ApplicationController {
    private readonly logger = new Logger('ApplicationController', {
        timestamp: true,
    });

    constructor(private readonly applicationService: ApplicationService) {}

    @ApiOperation({ summary: 'Create a new application' })
    @Post()
    async createApplication(
        @Body() dto: ApplicationCreateDTO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const currentOrgId = req.user['custom:organisationId'];
            const currentUserId = req.user['sub'];
            dto.createdBy = currentUserId;
            dto.organisationId = currentOrgId;
            const application =
                await this.applicationService.createApplication(dto);
            responseOk(res, application);
        } catch (error) {
            this.logger.error(
                `Error in createApplication: ${error.message}`,
                error.stack,
            );
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Get application by ID' })
    @ApiParam({ name: 'id', description: 'Application ID' })
    @Get(':id')
    async getApplicationById(
        @Param('id') id: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const currentOrgId = req.user['custom:organisationId'];
            const application =
                await this.applicationService.getApplicationById(id);

            if (!application) {
                return responseError(404, res, ResponseCode.NOT_FOUND);
            }

            if (currentOrgId !== application.organisationId) {
                return responseError(403, res, ResponseCode.FOR_BIDDEN);
            }

            responseOk(res, application);
        } catch (error) {
            this.logger.error(
                `Error in getApplicationById: ${error.message}`,
                error.stack,
            );
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update application' })
    @Put(':id')
    async updateApplication(
        @Body() dto: ApplicationUpdateDTO,
        @Param('id') id: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const currentOrgId = req.user['custom:organisationId'];
            const currentUserId = req.user['sub'];
            dto.updatedBy = currentUserId;
            // Check if application exists and user has access
            const existingApplication =
                await this.applicationService.getApplicationById(id);

            if (!existingApplication) {
                return responseError(404, res, ResponseCode.NOT_FOUND);
            }

            // Check if user has access to this application
            if (currentOrgId !== existingApplication.organisationId) {
                return responseError(403, res, ResponseCode.FOR_BIDDEN);
            }
            dto.id = id;
            await this.applicationService.updateApplication(dto);
            responseOk(res);
        } catch (error) {
            this.logger.error(
                `Error in updateApplication: ${error.message}`,
                error.stack,
            );
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Delete application' })
    @ApiParam({ name: 'id', description: 'Application ID' })
    @Delete(':id')
    async deleteApplication(
        @Param('id') id: string,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const currentOrgId = req.user['custom:organisationId'];
            // Check if application exists and user has access
            const existingApplication =
                await this.applicationService.getApplicationById(id);

            if (!existingApplication) {
                return responseError(404, res, ResponseCode.NOT_FOUND);
            }

            // Check if user has access to this application
            if (currentOrgId !== existingApplication.organisationId) {
                return responseError(403, res, ResponseCode.FOR_BIDDEN);
            }

            await this.applicationService.deleteApplication(id);
            responseOk(res);
        } catch (error) {
            this.logger.error(
                `Error in deleteApplication: ${error.message}`,
                error.stack,
            );
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Query applications' })
    @Get()
    async queryApplications(
        @Query() query: ApplicationQueryDTO,
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        const currentOrgId = req.user['custom:organisationId'];

        try {
            // Set organisation ID from authenticated user if not provided
            if (!query.organisationId && req.user) {
                query.organisationId = currentOrgId;
            }

            // Convert page and size from string to int
            query.page = parseInt(query.page, 10);
            query.size = parseInt(query.size, 10);

            const result =
                await this.applicationService.getApplicationsByQuery(query);
            responseOk(res, result);
        } catch (error) {
            this.logger.error(
                `Error in queryApplications: ${error.message}`,
                error.stack,
            );
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }

    @ApiOperation({ summary: 'Update application task status' })
    @Put(':id/task/:taskId')
    async updateApplicationTask(
        @Param('id') id: string,
        @Param('taskId') taskId: string,
        @Body() dto: { status: string; detail: any; documentIds: string[] },
        @Req() req: LwRequest,
        @Res() res: Response,
    ) {
        try {
            const currentOrgId = req.user['custom:organisationId'];
            const existingApplication =
                await this.applicationService.getApplicationById(id);

            if (!existingApplication) {
                return responseError(404, res, ResponseCode.NOT_FOUND);
            }

            if (currentOrgId !== existingApplication.organisationId) {
                return responseError(403, res, ResponseCode.FOR_BIDDEN);
            }

            await this.applicationService.updateApplicationTask(taskId, dto);
            responseOk(res);
        } catch (error) {
            this.logger.error(
                `Error in updateApplicationTaskStatus: ${error.message}`,
                error.stack,
            );
            responseError(500, res, ResponseCode.SERVER_ERROR);
        }
    }
}
