# MainService
## Description

The main service for Loftyworks project.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npx prisma generate
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment
1. Build a docker image
2. Push the image to ECR of the environment you want to deploy
3. Update the deployment in EKS

## Data migration from DynamoDB to PG
1. Add a configuration for tables to migrate under directory 'scripts/db_migration_cfgs'
2. Add a local .env file contains 'DATABASE_URL', which is the url for target postgresql
3. Run script: ```python scripts/ddb2pgs.py --file scripts/db_migration_cfgs/<cfg>.json```

## New env params in .env
1. In local env, add **GOOGLE_APPLICATION_CREDENTIALS** to **.env** file, this value is from **loftyworks-backend-js\webPushSubscriptionService\rentancy-test-firebase-adminsdk-18gjf-a485b01489.json**

   prod file is: **rentancy-4c242-firebase-adminsdk-vnym6-921bcfb0eb.json**

   For stage/prod env, add **GOOGLE_APPLICATION_CREDENTIALS** in AWS Secrets Manager **secrets/loftyworks-main**

2. In local env, add **SUB_API_KEY** to **.env** file, this value is from **loftyworks-uk-dev** -> AppSync ->APIs -> WebSocket -> Settings

   For stage/prod env, need to create AppSync WebSocket event API, add Namespaces named **sub**, config this **Publish authorization** as **API_KEY**, **Subscribe authorization** as **AMAZON_COGNITO_USER_POOLS**. 

   Add **SUB_ENDPOINT** in config file, this value is from AppSync WebSocket event API **HTTP Endpoint**.

   Add **SUB_API_KEY** in AWS Secrets Manager **secrets/loftyworks-main**

Attention
- Make sure the environment you are going run the script has the permission to read dynamodb tables.
- Python version must >= 3.10