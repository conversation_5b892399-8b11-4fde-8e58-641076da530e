# MainService
## Description

The main service for Loftyworks project.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npx prisma generate
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment
1. Build a docker image
2. Push the image to ECR of the environment you want to deploy
3. Update the deployment in EKS

## Data migration from DynamoDB to PG
1. Add a configuration for tables to migrate under directory 'scripts/db_migration_cfgs'
2. Add a local .env file contains 'DATABASE_URL', which is the url for target postgresql
3. Run script: ```python scripts/ddb2pgs.py --file scripts/db_migration_cfgs/<cfg>.json```

Attention
- Make sure the environment you are going run the script has the permission to read dynamodb tables.
- Python version must >= 3.10