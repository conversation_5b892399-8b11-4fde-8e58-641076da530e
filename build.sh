#!/usr/bin/env bash

# --------------------------------------------------------------------------------
# default work environment: devuk
# Usage instructions:
# 1. Make the script executable:  
#    chmod +x build.sh

# 2. Run the script with the desired command parameter, for example:  
#    • Log in to Docker Registry:  
#      ./build.sh login  
#    • Build a Docker image:  
#      ./build.sh build  
#    • Push a Docker image:  
#      ./build.sh push  

# Show usage instructions
usage() {
  echo "Usage: $0 [login|build|push]"
  exit 1
}

# Check if at least one argument is provided
if [ $# -lt 1 ]; then
  usage
fi

COMMAND=$1

case $COMMAND in
  login)
    echo "Executing Docker login..."
    aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin 686255943894.dkr.ecr.eu-west-2.amazonaws.com
    ;;
  build)
    echo "Executing Docker build..."
    DOCKER_BUILDKIT=1 docker build -t loftyworks-main --platform linux/amd64 --secret id=npmrc,src=$HOME/.npmrc .
    ;;
  push)
    echo "Executing Docker push..."
    docker tag loftyworks-main:latest 686255943894.dkr.ecr.eu-west-2.amazonaws.com/loftyworks-main:latest
    docker push 686255943894.dkr.ecr.eu-west-2.amazonaws.com/loftyworks-main:latest
    ;;
  *)
    usage
    ;;
esac

